import { CTMS } from "./clinical-trials/ClinicalTrialsSchemaWithCTMS";
import { TrialsPaginatedResponse } from "./pagination";

export type ClinicalTrialFilterInput = {
  name: string;
  value: string | null;
};

export type ClinicalTrialSortInput = {
  sortBy: string;
  direction: string;
};

export type ClinicalTrialInput = {
  filters?: ClinicalTrialFilterInput[];
  ids?: string[];
  limit: number;
  offset: number;
  idsOnly?: boolean;
  sort: ClinicalTrialSortInput;
  // allows us to override launch darkly variation
  variationOverride?: number;
  projectId?: string;
  userId?: string;
  useAllIndications?: boolean;
};

export type ClinicalTrialFilterAutocompleteInput = {
  filters?: ClinicalTrialFilterInput[];
  ids?: string[];
  limit: number;
  offset: number;
  idsOnly?: boolean;
  sort: ClinicalTrialSortInput;
  // allows us to override launch darkly variation
  variationOverride?: number;
  projectId?: string;
  userId?: string;
  filterField?: ClinicalTrialFilterAutocompleteFilterField;
  filterValue?: string;
};

export enum ClinicalTrialFilterAutocompleteFilterField {
  CONDITIONS = "CONDITIONS",
  COUNTRY = "COUNTRY",
  STATE = "STATE",
  CITY = "CITY",
  SPONSORS = "SPONSORS",
  SPONSOR_TYPES = "SPONSOR_TYPES",
  BIOMARKERS = "BIOMARKERS",
  BIOMARKER_INCLUSION = "BIOMARKER_INCLUSION",
  BIOMARKER_EXCLUSION = "BIOMARKER_EXCLUSION",
  INDICATIONS = "INDICATIONS",
  INDICATION_INCLUSION = "INDICATION_INCLUSION",
  INDICATION_EXCLUSION = "INDICATION_EXCLUSION",
  GENERIC_DRUG_NAMES = "GENERIC_DRUG_NAMES",
  GENERIC_DRUG_NAMES_INCLUSION = "GENERIC_DRUG_NAMES_INCLUSION",
  GENERIC_DRUG_NAMES_EXCLUSION = "GENERIC_DRUG_NAMES_EXCLUSION"
}

export type ClinicalTrialDocument = {
  studyNctId?: string;
  studyPrimaryId?: string;
  studyRegistrySource?: string;
  studyStartDate?: number;
  studyEndDate?: number;
  studyPrimaryCompletionDate?: number;
  studyLastUpdatePostedDate?: number;
  studyBriefTitle?: string;
  studyOfficialTitle?: string;
  studyPhase?: string;
  studyOverallStatus?: string;
  studyEnrollment?: number;
  studyEnrollmentType?: string;
  studyBriefSummary?: string;
  studyId: number;
  designAllocation?: string[];
  designInterventionModel?: string[];
  designObservationalModel?: string[];
  designPrimaryPurpose?: string[];
  designTimePerspective?: string[];
  designMasking?: string[];
  designMaskingDescription?: string[];
  designInterventionModelDescription?: string[];
  designSubjectMasked?: boolean[];
  designCaregiverMasked?: boolean[];
  designInvestigatorMasked?: boolean[];
  designOutcomesAssessorMasked?: boolean[];
  designOutcomeType?: string[];
  designOutcomeMeasure?: string[];
  designOutcomeTimeFrame?: string[];
  designOutcomeDescription?: string[];
  designGroupType?: string[];
  designGroupTitle?: string[];
  designGroupDescription?: string[];
  conditionName?: string[];
  keywordName?: string[];
  interventionType?: string[];
  interventionName?: string[];
  interventionDescription?: string[];
  interventionOtherName?: string[];
  facilityName?: string[];
  facilityCity?: string[];
  facilityState?: string[];
  facilityZip?: string[];
  facilityCountry?: string[];
  facilityInvestigatorRole?: string[];
  facilityInvestigatorName?: string[];
  eligibilitySamplingMethod?: string[];
  eligibilityGender?: string[];
  eligibilityMinimumAgeRaw?: string[];
  eligibilityMinimumAgeInMinutes?: number[];
  eligibilityMaximumAgeRaw?: string[];
  eligibilityMaximumAgeInMinutes?: number[];
  eligibilityAcceptsHealthyVolunteers?: boolean[];
  eligibilityPopulation?: string[];
  eligibilityCriteria?: string[];
  eligibilityGenderDescription?: string[];
  eligibilityGenderBased?: boolean[];
  responsiblePartyType?: string[];
  responsiblePartyName?: string[];
  responsiblePartyTitle?: string[];
  responsiblePartyAffiliation?: string[];
  sponsorName?: string[];
  sponsorAgencyClass?: string[];
  sponsorLeadOrCollaborator?: string[];
  centralContactType?: string[];
  centralContactName?: string[];
  centralContactPhone?: string[];
  centralContactEmail?: string[];
  facilityContactType?: string[];
  facilityContactName?: string[];
  facilityContactPhone?: string[];
  facilityContactEmail?: string[];
  overallOfficialRole?: string[];
  overallOfficialName?: string[];
  overallOfficialAffiliation?: string[];
  uniqueCountOfSites?: number;
  patientsPerMonth?: number;
  avgPatientsPerSite?: number;
  patientsPerSitePerMonth?: number;
};

export enum ClinicalTrialSource {
  NCT = "NCT",
  AACT = "AACT",
  ChiCTR = "ChiCTR",
  DRKS = "DRKS",
  EUDRA = "EUDRA",
  UMIN = "UMIN",
  CTIS = "CTIS",
  ISRCTN = "ISRCTN",
  UNKNOWN = "Unknown"
}

export type ClinicalTrialContacts = {
  name: string;
  isActive: boolean;
};

export type ClinicalTrialH1People = {
  id: string;
  isActive: boolean;
};

export type EnrollmentHistory = {
  effective_date?: string;
  enrollment?: string;
  enrollment_type?: string;
};

export type ClinicalTrial = {
  id: number;
  h1Id?: string;
  source?: ClinicalTrialSource;
  sources?: ClinicalTrialSource[];
  briefTitle?: string;
  status?: string;
  startDate?: number;
  nctId?: string;
  studyPhase?: string;
  acronym?: string;
  conditions?: string[];
  interventions?: string[];
  interventionTypes?: string[];
  sponsors?: string[];
  interventionOtherNames?: string[];
  estimatedEndDate?: number;
  primaryCompletionDate?: number;
  studyCompletionDate?: number;
  studyType?: string;
  enrollment?: number;
  enrollmentType?: string;
  eligibilityMinAge?: string[];
  allocation?: string[];
  interventionModel?: string[];
  masking?: string[];
  primaryPurpose?: string[];
  investigators?: string[];
  facilityName?: string[];
  facilityCity?: string[];
  facilityState?: string[];
  facilityCountry?: string[];
  lastUpdatedAt?: number;
  // @deprecated replaced by contacts as to help show what contacts are active vs inactive
  contactNames?: string[];
  contacts?: ClinicalTrialContacts[];
  trialDuration?: string;
  sitesCount?: number;
  avgPatientsPerSites?: number;
  patientsPerMonths?: number;
  patientsPerSitesPerMonth?: number;
  // @deprecated replaced by h1People as to help show what people are active vs inactive
  personIds?: string[];
  h1People?: ClinicalTrialH1People[];
  facilities?: FacilitiesV2[];
  studyDirectors?: string[];
  ctms?: CTMS[];
  hasCTMSData?: boolean;
  enrollmentDurationInMonths?: number;
  studyLinks?: string[];
  people?: Person[];
  biomarkers?: string[];
  biomarkerInclusion?: string[];
  biomarkerExclusion?: string[];
  genericDrugNames?: string[];
  genericDrugNameInclusion?: string[];
  genericDrugNameExclusion?: string[];
  indicationInclusions?: string[];
  indicationExclusions?: string[];
  indications?: string[];
  enrollmentHistory?: EnrollmentHistory[];
  actualEnrollment?: EnrollmentHistory | null;
  estimatedEnrollment?: {
    earliest: EnrollmentHistory | null;
    latest: EnrollmentHistory | null;
  };
};

export enum TrialsDiscoverySortProperty {
  Status = "Status",
  NCTID = "NctId",
  StudyPhase = "StudyPhase",
  Conditions = "Conditions",
  Interventions = "Interventions",
  InterventionType = "InterventionType",
  Sponsor = "Sponsor",
  InterventionOtherNames = "InterventionOtherNames",
  StartDate = "StartDate",
  EstimatedEndDate = "EstimatedEndDate",
  PrimaryCompletionDate = "PrimaryCompletionDate",
  StudyCompletionDate = "StudyCompletionDate",
  StudyType = "StudyType",
  Enrollment = "Enrollment",
  EnrollmentType = "EnrollmentType",
  EligibilityMinAge = "EligibilityMinAge",
  Allocation = "Allocation",
  InterventionModel = "InterventionModel",
  Masking = "Masking",
  PrimaryPurpose = "PrimaryPurpose",
  Investigators = "Investigators",
  Sites = "Sites",
  City = "City",
  States = "States",
  Country = "Country",
  FunderType = "FunderType",
  LastUpdated = "LastUpdated",
  ContactNames = "ContactNames",
  SitesCount = "SitesCount",
  AvgPatientsPerSite = "AvgPatientsPerSite",
  PatientsPerMonth = "PatientsPerMonth",
  PatientsPerSitePerMonth = "PatientsPerSitePerMonth",
  Acronym = "Acronym"
}

export type ClinicalTrialsSearchResponse =
  TrialsPaginatedResponse<ClinicalTrial>;
export interface ClinicalTrialDocumentBase {
  h1_clinical_trial_id?: string;
  is_deprecated?: boolean;
  survivor_h1_clinical_trial_id?: string;
}

// generated using https://app.quicktype.io/
// based off json schema: https://github.com/h1insights/data-platform-gold-to-product/blob/main/src/ct/schema/aact/topic/latest.schema.json
export interface ClinicalTrialDocumentV2 {
  h1_clinical_trial_id: string;
  identifiers: Identifier[];
  person?: Person[];
  facility?: Facility[];
  effective_date: string;
  study: Study;
  version: number;
}

// // generated using https://app.quicktype.io/
// // based off json schema: https://github.com/h1insights/data-platform-infra/blob/main/teams/data-platform-ct/multi-env/glue/schemas/aact/topic/latest.json
export interface ClinicalTrialDocumentV3 extends ClinicalTrialDocumentBase {
  h1_clinical_trial_id: string;
  version: number;
  is_deprecated?: boolean;
  survivor_h1_clinical_trial_id?: string;
  // below values all required if is_deprecated = false
  identifiers?: Identifier[];
  person?: Person[];
  facility?: Facility[];
  effective_date?: string;
  study?: Study;
}

// **
// FacilitiesV2 interface is to match interface expaected by pipeline DTO resolver
export interface FacilitiesV2 {
  id: number;
  name: string;
  city: string;
  country: string;
  zip: string;
  state: string;
  statusHistory?: StatusHistory[];
  h1MasterOrganizationId?: number | null;
}

export interface Facility {
  city?: string[];
  collection_source_id?: number;
  country?: string[];
  external_uuid: string;
  h1_clinical_trial_id?: string;
  h1_organization_id?: number;
  name?: string;
  personHistory?: PersonHistory[];
  state?: string[];
  statusHistory?: StatusHistory[];
  study_organization_hash: string;
  zip?: string;
}

export interface PersonHistory {
  collection_source_id?: number;
  end_date?: string;
  facility_person_hash: string;
  h1_clinical_trial_id?: string;
  hash_or_id?: string;
  role?: string;
  start_date?: string;
  study_organization_hash?: string;
  study_person_hash: string;
}

export interface StatusHistory {
  collection_source_id?: number;
  end_date?: string;
  facility_hash: string;
  h1_clinical_trial_id?: string;
  hash_or_id?: string;
  start_date?: string;
  status?: string;
  study_organization_hash?: string;
}

export interface Identifier {
  collection_source_id: number;
  external_uuid: string;
  h1_clinical_trial_id?: string;
}

export interface Person {
  collection_source_id?: number;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  h1_person_id?: number;
  name?: string;
  roleHistory?: RoleHistory[];
  study_person_hash: string;
}

export interface RoleHistory {
  affiliation?: string;
  collection_source_id?: number;
  email?: string;
  end_date?: string;
  h1_clinical_trial_id?: string;
  hash_or_id?: string;
  phone?: string;
  role?: string;
  start_date?: string;
  study_person_hash?: string;
  study_person_role_hash: string;
}

export interface Study {
  acronym?: string;
  baseline_population?: string;
  biospec_description?: string;
  biospec_retention?: string;
  brief_summaries?: string[];
  brief_title?: string;
  browse_conditions?: string[];
  browse_interventions?: string[];
  collection_source_id?: number;
  completion_date?: string;
  completion_date_type?: string;
  completion_month_year?: string;
  conditions?: string[];
  created_at?: string;
  design?: Design[];
  designGroup?: DesignGroup[];
  designOutcome?: DesignOutcome[];
  detailed_descriptions?: string[];
  disposition_first_posted_date?: string;
  disposition_first_posted_date_type?: string;
  disposition_first_submitted_date?: string;
  disposition_first_submitted_qc_date?: string;
  effective_date?: string;
  eligibility?: Eligibility[];
  enrollment?: number;
  enrollment_type?: string;
  enrollment_history?: {
    effective_date: string;
    enrollment: string;
    enrollment_type: string;
  }[];
  expanded_access_type_individual?: boolean;
  expanded_access_type_intermediate?: boolean;
  expanded_access_type_treatment?: boolean;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  has_dmc?: boolean;
  has_expanded_access?: boolean;
  idInformation?: IDInformation[];
  intervention?: StudyIntervention[];
  ipd_access_criteria?: string;
  ipd_time_frame?: string;
  ipd_url?: string;
  is_fda_regulated_device?: boolean;
  is_fda_regulated_drug?: boolean;
  is_ppsd?: boolean;
  is_unapproved_device?: boolean;
  is_us_export?: boolean;
  keywords?: string[];
  last_known_status?: string;
  last_update_posted_date_type?: string;
  last_update_submitted_date?: string;
  last_update_submitted_qc_date?: string;
  latest?: Latest[];
  limitations_and_caveats?: string;
  link?: Link[];
  nlm_download_date_description?: string;
  number_of_arms?: number;
  number_of_groups?: number;
  official_title?: string;
  outcome?: Outcome[];
  overall_status?: string;
  participantFlow?: ParticipantFlow[];
  pendingResult?: PendingResult[];
  phase?: string;
  plan_to_share_ipd?: string;
  plan_to_share_ipd_description?: string;
  primary_completion_date?: string;
  primary_completion_date_type?: string;
  primary_completion_month_year?: string;
  reference?: Reference[];
  resultAgreement?: ResultAgreement[];
  resultGroup?: ResultGroup[];
  results_first_posted_date?: string;
  results_first_posted_date_type?: string;
  results_first_submitted_date?: string;
  results_first_submitted_qc_date?: string;
  source?: string;
  sponsor?: Sponsor[];
  start_date?: string;
  start_date_type?: string;
  start_month_year?: string;
  study_first_posted_date?: string;
  study_first_posted_date_type?: string;
  study_first_submitted_date?: string;
  study_first_submitted_qc_date?: string;
  study_type?: string;
  target_duration?: string;
  updated_at?: string;
  verification_date?: string;
  verification_month_year?: string;
  why_stopped?: string;
  start_date_history?: {
    effective_date: string;
    start_date?: string;
  }[];
  primary_completion_date_history?: {
    effective_date: string;
    primary_completion_date?: string;
  }[];
  status_history?: {
    effective_date: string;
    status?: string;
  }[];
  completion_date_history?: {
    effective_date: string;
    completion_date?: string;
  }[];
}

export interface Design {
  allocation?: string;
  caregiver_masked?: boolean;
  collection_source_id?: number;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  intervention_model?: string;
  intervention_model_description?: string;
  investigator_masked?: boolean;
  masking?: string;
  masking_description?: string;
  observational_model?: string;
  outcomes_assessor_masked?: boolean;
  primary_purpose?: string;
  subject_masked?: boolean;
  time_perspective?: string;
}

export interface DesignGroup {
  collection_source_id?: number;
  description?: string;
  design_group_hash: string;
  effective_date?: string;
  external_uuid: string;
  group_type?: string;
  h1_clinical_trial_id?: string;
  intervention?: DesignGroupIntervention[];
  title?: string;
}

export interface DesignGroupIntervention {
  collection_source_id?: number;
  design_group_hash?: string;
  design_group_intervention_hash: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  intervention_hash: string;
}

export interface DesignOutcome {
  collection_source_id?: number;
  description?: string;
  design_outcome_hash: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  measure?: string;
  outcome_type?: string;
  population?: string;
  time_frame?: string;
}

export interface Eligibility {
  collection_source_id?: number;
  criteria?: string;
  effective_date?: string;
  exclusion_criteria_parsed?: string;
  external_uuid: string;
  gender?: string;
  gender_based?: boolean;
  gender_description?: string;
  h1_clinical_trial_id?: string;
  healthy_volunteers?: string;
  inclusion_criteria_parsed?: string;
  maximum_age?: string;
  minimum_age?: string;
  population?: string;
  sampling_method?: string;
}

export interface IDInformation {
  collection_source_id?: number;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  id_information_hash: string;
  id_type?: string;
  id_value?: string;
}

export interface StudyIntervention {
  collection_source_id?: number;
  description?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  intervention_hash: string;
  intervention_other_names?: string[];
  intervention_type?: string;
  name?: string;
  nct_id?: string;
}

export interface Latest {
  effective_date: string;
  h1_clinical_trial_id?: string;
}

export interface Link {
  collection_source_id?: number;
  description?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  link_hash: string;
  url?: string;
}

export interface Outcome {
  analysis?: Analysis[];
  anticipated_posting_date?: string;
  anticipated_posting_month_year?: string;
  collection_source_id?: number;
  count?: Count[];
  description?: string;
  dispersion_type?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  measurement?: Measurement[];
  outcome_hash: string;
  outcome_type?: string;
  param_type?: string;
  population?: string;
  time_frame?: string;
  title?: string;
  units?: string;
  units_analyzed?: string;
}

export interface Analysis {
  ci_lower_limit?: number;
  ci_n_sides?: string;
  ci_percent?: number;
  ci_upper_limit?: number;
  ci_upper_limit_na_comment?: string;
  collection_source_id?: number;
  dispersion_type?: string;
  dispersion_value?: number;
  effective_date?: string;
  estimate_description?: string;
  external_uuid: string;
  group?: Group[];
  groups_description?: string;
  h1_clinical_trial_id?: string;
  method?: string;
  method_description?: string;
  non_inferiority_description?: string;
  non_inferiority_type?: string;
  other_analysis_description?: string;
  outcome_analysis_hash: string;
  outcome_hash?: string;
  p_value?: number;
  p_value_description?: string;
  p_value_modifier?: number;
  param_type?: string;
  param_value?: number;
}

export interface Group {
  collection_source_id?: number;
  ctgov_group_code?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  outcome_analysis_group_hash: string;
  outcome_analysis_hash?: string;
  result_group_hash: string;
}

export interface Count {
  collection_source_id?: number;
  count?: number;
  ctgov_group_code?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  outcome_count_hash: string;
  outcome_hash?: string;
  result_group_hash: string;
  scope?: string;
  units?: string;
}

export interface Measurement {
  category?: string;
  classification?: string;
  collection_source_id?: number;
  ctgov_group_code?: string;
  description?: string;
  dispersion_lower_limit?: number;
  dispersion_type?: string;
  dispersion_upper_limit?: number;
  dispersion_value?: string;
  dispersion_value_num?: number;
  effective_date?: string;
  explanation_of_na?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  outcome_hash?: string;
  outcome_measurement_hash: string;
  param_type?: string;
  param_value?: string;
  param_value_num?: number;
  result_group_hash: string;
  title?: string;
  units?: string;
}

export interface ParticipantFlow {
  collection_source_id?: number;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  pre_assignment_details?: string;
  recruitment_details?: string;
}

export interface PendingResult {
  collection_source_id?: number;
  effective_date?: string;
  event?: string;
  event_date?: string;
  event_date_description?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  pending_result_hash: string;
}

export interface Reference {
  citation?: string;
  collection_source_id?: number;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  pmid?: number;
  reference_type?: string;
  study_reference_hash: string;
}

export interface ResultAgreement {
  agreement?: string;
  collection_source_id?: number;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  other_details?: string;
  pi_employee?: string;
  restriction_type?: string;
  restrictive_agreement?: string;
  result_agreement_hash: string;
}

export interface ResultGroup {
  baselineCount?: BaselineCount[];
  baselineMeasurement?: BaselineMeasurement[];
  collection_source_id?: number;
  ctgov_group_code?: string;
  description?: string;
  dropWithdrawal?: DropWithdrawal[];
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  milestone?: Milestone[];
  result_group_hash: string;
  result_type?: string;
  title?: string;
}

export interface BaselineCount {
  baseline_count_hash: string;
  collection_source_id?: number;
  count?: number;
  ctgov_group_code?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  result_group_hash?: string;
  scope?: string;
  units?: string;
}

export interface BaselineMeasurement {
  baseline_measurement_hash: string;
  category?: string;
  classification?: string;
  collection_source_id?: number;
  ctgov_group_code?: string;
  description?: string;
  dispersion_lower_limit?: number;
  dispersion_type?: string;
  dispersion_upper_limit?: number;
  dispersion_value?: string;
  dispersion_value_num?: number;
  effective_date?: string;
  explanation_of_na?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  param_type?: string;
  param_value?: string;
  param_value_num?: number;
  result_group_hash?: string;
  title?: string;
  units?: string;
}

export interface DropWithdrawal {
  collection_source_id?: number;
  count?: number;
  ctgov_group_code?: string;
  drop_withdrawal_hash: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  period?: string;
  reason?: string;
  result_group_hash?: string;
}

export interface Milestone {
  collection_source_id?: number;
  count?: number;
  ctgov_group_code?: string;
  description?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  milestone_hash: string;
  period?: string;
  result_group_hash?: string;
  title?: string;
}

export interface Sponsor {
  agency_class?: string;
  collection_source_id?: number;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  is_organization?: boolean;
  is_responsible_party?: boolean;
  lead_or_collaborator?: string;
  sponsor_hash: string;
  study_organization_hash?: string;
  study_person_hash?: string;
}

export enum TrialPersonRole {
  PRINCIPAL_INVESTIGATOR = "Principal Investigator",
  CENTRAL_CONTACT = "Central Contact",
  RESPONSIBLE_PARTY = "Responsible Party",
  FACILITY_CONTACT = "Facility Contact",
  STUDY_DIRECTOR = "Study Director",
  RESULT_CONTACT = "Result Contact",
  STUDY_CHAIR = "Study Chair",
  SUB_INVESTIGATOR = "Sub-Investigator"
}

/**
 * key: the group value returned by the ES aggregation
 * doc_count: the number of records that had that group
 *
 * Example: if we ran a group by country on the following data
 *
 * [{ country: 'US' }, { country: 'EU' }, { country: 'EU' }]
 *
 * we'd get two 'GroupedByItem's { key: 'US', doc_count: 1 }, { key: 'EU', doc_count: 2 }
 */
export type GroupedByItem<T extends string> = { key: T; doc_count: number };

export type GroupedByPhaseAndStatus = Array<
  GroupedByItem<string> & {
    group_by_status: {
      buckets: Array<GroupedByItem<string>>;
    };
  }
>;

export type GroupedByCountryTrials = Array<
  GroupedByItem<string> & {
    trials: {
      doc_count: number;
    };
  }
>;

export type GroupedByTopCountryAvgEnrollmentRate = Array<
  GroupedByItem<string> & {
    trials: {
      doc_count: number;
      sum_patients_per_site_per_month: {
        value: number;
      };
    };
    avg_patients_per_site_per_month: {
      value: number;
    };
  }
>;

/**
 * Top level aggregations for a trial search
 */
export interface TrialsAggregations {
  /**
   * Average duration of all trials in ms
   */
  avgTrialsDuration: number;
  /**
   * Median duration of all trials in ms
   */
  medianTrialsDuration: number;
  /**
   * enrollment count of all trials / facilityCount
   *
   * null when facilityCount is 0
   */
  avgPatientsPerSite: number | null;
  /**
   * enrollment count of trials / facilityCount / Trial Duration
   *
   * null when facilityCount is 0
   */
  avgPatientsPerSitePerMonth: number | null;
  /**
   * enrollment count of all trials / # of trials
   *
   * null when the number of trials in the search response is 0
   */
  avgPatientCount: number | null;
  medianPatientCount: number | null;
  /**
   * Sum of enrollment counts across all trials in the search response.
   */
  totalEnrollment: number;
  /**
   * Sum of facility counts across all trials in the search response.
   */
  facilityCount: number;

  investigatorCount: number;
  /**
   * Count of all 'Principal Investigator' and 'Sub-Investigators' / # of trials
   *
   * null when the number of trials in the search response is 0
   */
  avgInvestigatorCount: number | null;
  /**
   * Returns the count of all person roles within a trial search response.
   *
   * For example, a search that returned trials with 30 Principle investigators and 10 Contacts would look like
   * Ex: [ { key: 'Principal Investigator', doc_count: 30 }, { key: 'Contact', doc_count: 10 } ]
   */
  groupByPersonRole: Array<GroupedByItem<TrialPersonRole>>;
  /**
   * Returns the count of all countries within a trial search response.
   *
   * For example, a search that returned 30 US trials and 10 canadian trial would look like
   * Ex: [ { key: 'United States', doc_count: 30 }, { key: 'Canada', doc_count: 10 } ]
   */
  groupByCountry: Array<GroupedByItem<string>>;
  /**
   * Trials grouped by phase and then status
   *
   * `groupByPhaseAndStatus` is an array of objects containing a `key` (the trial phase), `doc_count` (# of trials with that phase) and a `group_by_status` object.
   *
   * the `group_by_status` object contains a `buckets` array containing objects with a `key` (the trial status) and a `doc_count` (# of trials with that status [and parent phase])
   */
  groupByPhaseAndStatus: GroupedByPhaseAndStatus;
  /**
   * Trials grouped by enrollment duration in months
   */
  groupByEnrollmentDuration: Array<GroupedByItem<string>>;
  /**
   * Trials grouped by number of patients recruited per site per enrollment duration in months
   */
  groupByPatientsPerSitePerEnrollmentDuration: Array<GroupedByItem<string>>;
  /**
   * facilityCount of all trials / number of trials
   *
   * null when the number of trials in the search response is 0
   */
  avgFacilityCount: number | null;
  medianFacilityCount: number | null;
  medianInvestigatorCount: number | null;
  avgPatientsPerSitePerMonthV2: number | null;
  medianPatientsPerSitePerMonthV2: number;
  medianEnrollmentDurationV2: number | null;
  /**
   * Returns the count of top countries within a trial search response.
   *
   * For example, a search that returned 30 US sites and 10 canadian sites would look like
   * Ex: [ { key: 'United States', doc_count: 30 }, { key: 'Canada', doc_count: 10 } ]
   */
  groupByTopCountrySites: Array<GroupedByItem<string>>;
  /**
   * Returns the count of top countries within a trial search response.
   *
   * For example, a search that returned 18 US trials and 6 canadian trials would look like
   * Ex: [ { key: 'United States', doc_count: 18 }, { key: 'Canada', doc_count: 6 } ]
   */
  groupByTopCountryTrials: Array<GroupedByItem<string>>;
  groupByTopCountryAvgEnrollmentRate: Array<GroupedByItem<string>>;
}

export enum IndicationsType {
  ALL = "all",
  INCLUSION = "inclusion",
  EXCLUSION = "exclusion"
}

/**
 * Histogram bucket for trial data distribution
 */
export interface HistogramBucket {
  key: string;
  doc_count: number;
}

/**
 * Histogram data for a specific metric
 */
export type TrialHistogramData = Record<
  string,
  { buckets: HistogramBucket[]; total: number; hasOutliers: boolean }
>;

/**
 * Available metrics for histogram generation
 */
export enum TrialHistogramMetric {
  ENROLLMENT = "enrollment",
  TRIAL_DURATION = "trial_duration",
  ENROLLMENT_DURATION = "enrollment_duration",
  PATIENTS_PER_SITE_PER_MONTH = "patients_per_site_per_month",
  FACILITY_COUNT = "facility_count",
  INVESTIGATOR_COUNT = "investigator_count"
}

/**
 * Response containing histogram data for all available metrics
 */
export interface ClinicalTrialHistogramResponse {
  /**
   * Histogram data for each available metric, keyed by metric name
   */
  metrics: TrialHistogramData;
  /**
   * Total number of trials matching the filters
   */
  total: number;
}

/**
 * Input for generating trial histogram data
 */
export interface ClinicalTrialHistogramInput {
  /**
   * Filters to apply to the trial search
   */
  filters?: ClinicalTrialFilterInput[];
  /**
   * Specific trial IDs to include in the search
   */
  ids?: string[];
  /**
   * Sort configuration for the search
   */
  sort: ClinicalTrialSortInput;
  /**
   * Number of buckets to generate for each histogram metric (default: 10)
   */
  bucketCount?: number;
  /**
   * Project ID for feature flag evaluation
   */
  projectId?: string;
  /**
   * User ID for feature flag evaluation
   */
  userId?: string;
}

/**
 * Country breakdown data for a single country
 */
export interface TrialCountryBreakdownItem {
  /**
   * Country name
   */
  country: string;
  /**
   * Total number of trials in this country
   */
  totalTrials: number;
  /**
   * Number of active trials (excluding "Not yet recruiting" and "Recruiting")
   */
  activeTrials: number;
  /**
   * Number of recruiting trials
   */
  recruitingTrials: number;
  /**
   * Median patient enrollment count
   */
  patientEnrollmentMedian: number | null;
  /**
   * Median enrollment duration in months
   */
  enrollmentDurationMedian: number | null;
  /**
   * Median enrollment rate (patients per site per month)
   */
  enrollmentRateMedian: number | null;
  /**
   * Median trial duration in months
   */
  trialDurationMedian: number | null;
  /**
   * Total number of H1 sites
   */
  totalH1Sites: number;
  /**
   * Total number of H1 investigators
   */
  totalH1Investigators: number;
}

/**
 * Response containing trial country breakdown data
 */
export interface ClinicalTrialCountryBreakdownResponse {
  /**
   * Array of country breakdown data, sorted by total trials descending
   */
  countries: TrialCountryBreakdownItem[];
  /**
   * Total number of countries matching the filters
   */
  total: number;
}

/**
 * Input for generating trial country breakdown data
 */
export interface ClinicalTrialCountryBreakdownInput {
  /**
   * Filters to apply to the trial search
   */
  filters?: ClinicalTrialFilterInput[];
  /**
   * Specific trial IDs to include in the search
   */
  ids?: string[];
  /**
   * Project ID for feature flag evaluation
   */
  projectId?: string;
  /**
   * User ID for feature flag evaluation
   */
  userId?: string;
}

/**
 * Region breakdown data for a single region/state
 */
export interface TrialRegionBreakdownItem {
  /**
   * Region/state name
   */
  region: string;
  /**
   * Total number of trials in this region
   */
  totalTrials: number;
  /**
   * Number of active trials (excluding "Not yet recruiting" and "Recruiting")
   */
  activeTrials: number;
  /**
   * Number of recruiting trials
   */
  recruitingTrials: number;
  /**
   * Median patient enrollment count
   */
  patientEnrollmentMedian: number | null;
  /**
   * Median enrollment duration in months
   */
  enrollmentDurationMedian: number | null;
  /**
   * Median enrollment rate (patients per site per month)
   */
  enrollmentRateMedian: number | null;
  /**
   * Median trial duration in months
   */
  trialDurationMedian: number | null;
  /**
   * Total number of H1 sites
   */
  totalH1Sites: number;
  /**
   * Total number of H1 investigators
   */
  totalH1Investigators: number;
}

/**
 * Response containing trial region breakdown data
 */
export interface ClinicalTrialRegionBreakdownResponse {
  /**
   * Array of region breakdown data, sorted by total trials descending
   */
  regions: TrialRegionBreakdownItem[];
  /**
   * Total number of regions matching the filters
   */
  total: number;
}

/**
 * Input for generating trial region breakdown data
 */
export interface ClinicalTrialRegionBreakdownInput {
  /**
   * Country to filter facilities by
   */
  country: string;
  /**
   * Filters to apply to the trial search
   */
  filters?: ClinicalTrialFilterInput[];
  /**
   * Specific trial IDs to include in the search
   */
  ids?: string[];
  /**
   * Project ID for feature flag evaluation
   */
  projectId?: string;
  /**
   * User ID for feature flag evaluation
   */
  userId?: string;
}
