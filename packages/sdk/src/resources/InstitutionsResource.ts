import { Entity } from "@h1nyc/account-sdk";
import {
  InstitutionsResponse,
  InstitutionsSearchInput,
  InstitutionFilterAggregation,
  InstitutionsAutocompleteInput,
  TrialsSearchByInstitutionInput,
  TrialsSearchByInstitutionResponse,
  IolClaimsInput,
  IolClaimsResponse,
  TaggedInstitutionsSearchInput,
  TaggedInstitutionsSearchResponse,
  PatientDiversityStatsResponse,
  InstitutionDiversityStatsInput,
  InstitutionLocationFilterAggregation
} from "../interfaces";

export interface InstitutionsResource {
  isReady(): Promise<boolean>;

  search(input: InstitutionsSearchInput): Promise<InstitutionsResponse>;

  patientCount(input: InstitutionsSearchInput): Promise<number>;

  bulkInstitutionSearch(
    input: Readonly<InstitutionsSearchInput>
  ): Promise<Array<Entity>>;

  searchTrialsByInstitution(
    input: TrialsSearchByInstitutionInput
  ): Promise<TrialsSearchByInstitutionResponse>;

  searchClaimsForInstitute(input: IolClaimsInput): Promise<IolClaimsResponse>;

  getTaggedInstitutions(
    input: TaggedInstitutionsSearchInput
  ): Promise<TaggedInstitutionsSearchResponse>;

  autocompleteCountries(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionLocationFilterAggregation[]>;

  autocompleteRegions(
    count: InstitutionsAutocompleteInput
  ): Promise<InstitutionLocationFilterAggregation[]>;

  autocompleteCities(
    count: InstitutionsAutocompleteInput
  ): Promise<InstitutionLocationFilterAggregation[]>;

  autocompletePostalCodes(
    count: InstitutionsAutocompleteInput
  ): Promise<InstitutionLocationFilterAggregation[]>;

  autocompleteAdminLevel1(
    input: string,
    country: string
  ): Promise<InstitutionLocationFilterAggregation[]>;

  autocompleteAdminLevel2(
    input: string,
    country: string
  ): Promise<InstitutionLocationFilterAggregation[]>;

  autocompleteTrialsSponsor(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteTrialsSponsorType(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteTrialsStatus(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteTrialsPhase(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteTrialsStudyType(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteTrialsId(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteTrialsBiomarker(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteOrgTypes(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteDiagnoses(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteProcedures(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteGenericNames(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompletePatientAgeRange(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompletePatientRace(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompletePatientSex(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  institutionDiversityStats(
    input: InstitutionDiversityStatsInput
  ): Promise<PatientDiversityStatsResponse>;

  autocompleteCcsr(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteCcsrPx(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteCcsrDiagnoses(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;

  autocompleteCcsrProcedures(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]>;
}
