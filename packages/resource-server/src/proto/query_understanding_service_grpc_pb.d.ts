// package: 
// file: query_understanding_service.proto

/* tslint:disable */
/* eslint-disable */

import * as grpc from "@grpc/grpc-js";
import * as query_understanding_service_pb from "./query_understanding_service_pb";
import * as synonym_pb from "./synonym_pb";
import * as query_intent_pb from "./query_intent_pb";

interface IQueryUnderstandingService extends grpc.ServiceDefinition<grpc.UntypedServiceImplementation> {
    getQueryUnderstanding: IQueryUnderstandingService_IGetQueryUnderstanding;
    getSynonyms: IQueryUnderstandingService_IGetSynonyms;
    getIndicationSynonymsAndIcdCodes: IQueryUnderstandingService_IGetIndicationSynonymsAndIcdCodes;
    getEntities: IQueryUnderstandingService_IGetEntities;
}

interface IQueryUnderstandingService_IGetQueryUnderstanding extends grpc.MethodDefinition<query_understanding_service_pb.QueryUnderstandingServiceRequest, query_understanding_service_pb.QueryUnderstandingServiceResponse> {
    path: "/QueryUnderstanding/GetQueryUnderstanding";
    requestStream: false;
    responseStream: false;
    requestSerialize: grpc.serialize<query_understanding_service_pb.QueryUnderstandingServiceRequest>;
    requestDeserialize: grpc.deserialize<query_understanding_service_pb.QueryUnderstandingServiceRequest>;
    responseSerialize: grpc.serialize<query_understanding_service_pb.QueryUnderstandingServiceResponse>;
    responseDeserialize: grpc.deserialize<query_understanding_service_pb.QueryUnderstandingServiceResponse>;
}
interface IQueryUnderstandingService_IGetSynonyms extends grpc.MethodDefinition<query_understanding_service_pb.QueryUnderstandingServiceRequest, query_understanding_service_pb.QueryUnderstandingServiceResponse> {
    path: "/QueryUnderstanding/GetSynonyms";
    requestStream: false;
    responseStream: false;
    requestSerialize: grpc.serialize<query_understanding_service_pb.QueryUnderstandingServiceRequest>;
    requestDeserialize: grpc.deserialize<query_understanding_service_pb.QueryUnderstandingServiceRequest>;
    responseSerialize: grpc.serialize<query_understanding_service_pb.QueryUnderstandingServiceResponse>;
    responseDeserialize: grpc.deserialize<query_understanding_service_pb.QueryUnderstandingServiceResponse>;
}
interface IQueryUnderstandingService_IGetIndicationSynonymsAndIcdCodes extends grpc.MethodDefinition<query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications, query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications> {
    path: "/QueryUnderstanding/GetIndicationSynonymsAndIcdCodes";
    requestStream: false;
    responseStream: false;
    requestSerialize: grpc.serialize<query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications>;
    requestDeserialize: grpc.deserialize<query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications>;
    responseSerialize: grpc.serialize<query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications>;
    responseDeserialize: grpc.deserialize<query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications>;
}
interface IQueryUnderstandingService_IGetEntities extends grpc.MethodDefinition<query_understanding_service_pb.QueryUnderstandingServiceRequest, query_understanding_service_pb.QueryUnderstandingServiceResponse> {
    path: "/QueryUnderstanding/GetEntities";
    requestStream: false;
    responseStream: false;
    requestSerialize: grpc.serialize<query_understanding_service_pb.QueryUnderstandingServiceRequest>;
    requestDeserialize: grpc.deserialize<query_understanding_service_pb.QueryUnderstandingServiceRequest>;
    responseSerialize: grpc.serialize<query_understanding_service_pb.QueryUnderstandingServiceResponse>;
    responseDeserialize: grpc.deserialize<query_understanding_service_pb.QueryUnderstandingServiceResponse>;
}

export const QueryUnderstandingService: IQueryUnderstandingService;

export interface IQueryUnderstandingServer extends grpc.UntypedServiceImplementation {
    getQueryUnderstanding: grpc.handleUnaryCall<query_understanding_service_pb.QueryUnderstandingServiceRequest, query_understanding_service_pb.QueryUnderstandingServiceResponse>;
    getSynonyms: grpc.handleUnaryCall<query_understanding_service_pb.QueryUnderstandingServiceRequest, query_understanding_service_pb.QueryUnderstandingServiceResponse>;
    getIndicationSynonymsAndIcdCodes: grpc.handleUnaryCall<query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications, query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications>;
    getEntities: grpc.handleUnaryCall<query_understanding_service_pb.QueryUnderstandingServiceRequest, query_understanding_service_pb.QueryUnderstandingServiceResponse>;
}

export interface IQueryUnderstandingClient {
    getQueryUnderstanding(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    getQueryUnderstanding(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    getQueryUnderstanding(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, options: Partial<grpc.CallOptions>, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    getSynonyms(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    getSynonyms(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    getSynonyms(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, options: Partial<grpc.CallOptions>, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    getIndicationSynonymsAndIcdCodes(request: query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications) => void): grpc.ClientUnaryCall;
    getIndicationSynonymsAndIcdCodes(request: query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications, metadata: grpc.Metadata, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications) => void): grpc.ClientUnaryCall;
    getIndicationSynonymsAndIcdCodes(request: query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications, metadata: grpc.Metadata, options: Partial<grpc.CallOptions>, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications) => void): grpc.ClientUnaryCall;
    getEntities(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    getEntities(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    getEntities(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, options: Partial<grpc.CallOptions>, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
}

export class QueryUnderstandingClient extends grpc.Client implements IQueryUnderstandingClient {
    constructor(address: string, credentials: grpc.ChannelCredentials, options?: Partial<grpc.ClientOptions>);
    public getQueryUnderstanding(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    public getQueryUnderstanding(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    public getQueryUnderstanding(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, options: Partial<grpc.CallOptions>, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    public getSynonyms(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    public getSynonyms(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    public getSynonyms(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, options: Partial<grpc.CallOptions>, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    public getIndicationSynonymsAndIcdCodes(request: query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications) => void): grpc.ClientUnaryCall;
    public getIndicationSynonymsAndIcdCodes(request: query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications, metadata: grpc.Metadata, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications) => void): grpc.ClientUnaryCall;
    public getIndicationSynonymsAndIcdCodes(request: query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications, metadata: grpc.Metadata, options: Partial<grpc.CallOptions>, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications) => void): grpc.ClientUnaryCall;
    public getEntities(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    public getEntities(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
    public getEntities(request: query_understanding_service_pb.QueryUnderstandingServiceRequest, metadata: grpc.Metadata, options: Partial<grpc.CallOptions>, callback: (error: grpc.ServiceError | null, response: query_understanding_service_pb.QueryUnderstandingServiceResponse) => void): grpc.ClientUnaryCall;
}
