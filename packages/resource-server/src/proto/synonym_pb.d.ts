// package: 
// file: synonym.proto

/* tslint:disable */
/* eslint-disable */

import * as jspb from "google-protobuf";

export class Synonym extends jspb.Message { 
    getOrig(): string;
    setOrig(value: string): Synonym;
    clearContextSynList(): void;
    getContextSynList(): Array<Synonym.ContextSyn>;
    setContextSynList(value: Array<Synonym.ContextSyn>): Synonym;
    addContextSyn(value?: Synonym.ContextSyn, index?: number): Synonym.ContextSyn;
    clearContextFreeSynList(): void;
    getContextFreeSynList(): Array<Synonym.SynScore>;
    setContextFreeSynList(value: Array<Synonym.SynScore>): Synonym;
    addContextFreeSyn(value?: Synonym.SynScore, index?: number): Synonym.SynScore;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): Synonym.AsObject;
    static toObject(includeInstance: boolean, msg: Synonym): Synonym.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: Synonym, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): Synonym;
    static deserializeBinaryFromReader(message: Synonym, reader: jspb.BinaryReader): Synonym;
}

export namespace Synonym {
    export type AsObject = {
        orig: string,
        contextSynList: Array<Synonym.ContextSyn.AsObject>,
        contextFreeSynList: Array<Synonym.SynScore.AsObject>,
    }


    export class ContextSyn extends jspb.Message { 

        hasLeftContext(): boolean;
        clearLeftContext(): void;
        getLeftContext(): string | undefined;
        setLeftContext(value: string): ContextSyn;

        hasRightContext(): boolean;
        clearRightContext(): void;
        getRightContext(): string | undefined;
        setRightContext(value: string): ContextSyn;

        hasSyn(): boolean;
        clearSyn(): void;
        getSyn(): string | undefined;
        setSyn(value: string): ContextSyn;

        hasScore(): boolean;
        clearScore(): void;
        getScore(): number | undefined;
        setScore(value: number): ContextSyn;

        hasDebug(): boolean;
        clearDebug(): void;
        getDebug(): string | undefined;
        setDebug(value: string): ContextSyn;

        serializeBinary(): Uint8Array;
        toObject(includeInstance?: boolean): ContextSyn.AsObject;
        static toObject(includeInstance: boolean, msg: ContextSyn): ContextSyn.AsObject;
        static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
        static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
        static serializeBinaryToWriter(message: ContextSyn, writer: jspb.BinaryWriter): void;
        static deserializeBinary(bytes: Uint8Array): ContextSyn;
        static deserializeBinaryFromReader(message: ContextSyn, reader: jspb.BinaryReader): ContextSyn;
    }

    export namespace ContextSyn {
        export type AsObject = {
            leftContext?: string,
            rightContext?: string,
            syn?: string,
            score?: number,
            debug?: string,
        }
    }

    export class SynScore extends jspb.Message { 

        hasSyn(): boolean;
        clearSyn(): void;
        getSyn(): string | undefined;
        setSyn(value: string): SynScore;

        hasScore(): boolean;
        clearScore(): void;
        getScore(): number | undefined;
        setScore(value: number): SynScore;

        hasDebug(): boolean;
        clearDebug(): void;
        getDebug(): string | undefined;
        setDebug(value: string): SynScore;

        serializeBinary(): Uint8Array;
        toObject(includeInstance?: boolean): SynScore.AsObject;
        static toObject(includeInstance: boolean, msg: SynScore): SynScore.AsObject;
        static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
        static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
        static serializeBinaryToWriter(message: SynScore, writer: jspb.BinaryWriter): void;
        static deserializeBinary(bytes: Uint8Array): SynScore;
        static deserializeBinaryFromReader(message: SynScore, reader: jspb.BinaryReader): SynScore;
    }

    export namespace SynScore {
        export type AsObject = {
            syn?: string,
            score?: number,
            debug?: string,
        }
    }

}

export class Synonyms extends jspb.Message { 
    clearSynonymInfoList(): void;
    getSynonymInfoList(): Array<Synonym>;
    setSynonymInfoList(value: Array<Synonym>): Synonyms;
    addSynonymInfo(value?: Synonym, index?: number): Synonym;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): Synonyms.AsObject;
    static toObject(includeInstance: boolean, msg: Synonyms): Synonyms.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: Synonyms, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): Synonyms;
    static deserializeBinaryFromReader(message: Synonyms, reader: jspb.BinaryReader): Synonyms;
}

export namespace Synonyms {
    export type AsObject = {
        synonymInfoList: Array<Synonym.AsObject>,
    }
}

export class TokenToPhrasePairInvertedIndex extends jspb.Message { 
    clearIndexListList(): void;
    getIndexListList(): Array<TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex>;
    setIndexListList(value: Array<TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex>): TokenToPhrasePairInvertedIndex;
    addIndexList(value?: TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex, index?: number): TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): TokenToPhrasePairInvertedIndex.AsObject;
    static toObject(includeInstance: boolean, msg: TokenToPhrasePairInvertedIndex): TokenToPhrasePairInvertedIndex.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: TokenToPhrasePairInvertedIndex, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): TokenToPhrasePairInvertedIndex;
    static deserializeBinaryFromReader(message: TokenToPhrasePairInvertedIndex, reader: jspb.BinaryReader): TokenToPhrasePairInvertedIndex;
}

export namespace TokenToPhrasePairInvertedIndex {
    export type AsObject = {
        indexListList: Array<TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.AsObject>,
    }


    export class TokenToPhrasePairIndex extends jspb.Message { 
        getToken(): string;
        setToken(value: string): TokenToPhrasePairIndex;
        clearIndexList(): void;
        getIndexList(): Array<number>;
        setIndexList(value: Array<number>): TokenToPhrasePairIndex;
        addIndex(value: number, index?: number): number;

        serializeBinary(): Uint8Array;
        toObject(includeInstance?: boolean): TokenToPhrasePairIndex.AsObject;
        static toObject(includeInstance: boolean, msg: TokenToPhrasePairIndex): TokenToPhrasePairIndex.AsObject;
        static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
        static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
        static serializeBinaryToWriter(message: TokenToPhrasePairIndex, writer: jspb.BinaryWriter): void;
        static deserializeBinary(bytes: Uint8Array): TokenToPhrasePairIndex;
        static deserializeBinaryFromReader(message: TokenToPhrasePairIndex, reader: jspb.BinaryReader): TokenToPhrasePairIndex;
    }

    export namespace TokenToPhrasePairIndex {
        export type AsObject = {
            token: string,
            indexList: Array<number>,
        }
    }

}

export class PhrasalSynonyms extends jspb.Message { 
    clearPhrasePairListList(): void;
    getPhrasePairListList(): Array<PhrasalSynonyms.TokenizedPhrasePair>;
    setPhrasePairListList(value: Array<PhrasalSynonyms.TokenizedPhrasePair>): PhrasalSynonyms;
    addPhrasePairList(value?: PhrasalSynonyms.TokenizedPhrasePair, index?: number): PhrasalSynonyms.TokenizedPhrasePair;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): PhrasalSynonyms.AsObject;
    static toObject(includeInstance: boolean, msg: PhrasalSynonyms): PhrasalSynonyms.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: PhrasalSynonyms, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): PhrasalSynonyms;
    static deserializeBinaryFromReader(message: PhrasalSynonyms, reader: jspb.BinaryReader): PhrasalSynonyms;
}

export namespace PhrasalSynonyms {
    export type AsObject = {
        phrasePairListList: Array<PhrasalSynonyms.TokenizedPhrasePair.AsObject>,
    }


    export class TokenizedPhrasePair extends jspb.Message { 
        clearOriginalWordTokensList(): void;
        getOriginalWordTokensList(): Array<string>;
        setOriginalWordTokensList(value: Array<string>): TokenizedPhrasePair;
        addOriginalWordTokens(value: string, index?: number): string;
        clearSynonymWordTokensList(): void;
        getSynonymWordTokensList(): Array<string>;
        setSynonymWordTokensList(value: Array<string>): TokenizedPhrasePair;
        addSynonymWordTokens(value: string, index?: number): string;

        serializeBinary(): Uint8Array;
        toObject(includeInstance?: boolean): TokenizedPhrasePair.AsObject;
        static toObject(includeInstance: boolean, msg: TokenizedPhrasePair): TokenizedPhrasePair.AsObject;
        static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
        static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
        static serializeBinaryToWriter(message: TokenizedPhrasePair, writer: jspb.BinaryWriter): void;
        static deserializeBinary(bytes: Uint8Array): TokenizedPhrasePair;
        static deserializeBinaryFromReader(message: TokenizedPhrasePair, reader: jspb.BinaryReader): TokenizedPhrasePair;
    }

    export namespace TokenizedPhrasePair {
        export type AsObject = {
            originalWordTokensList: Array<string>,
            synonymWordTokensList: Array<string>,
        }
    }

}
