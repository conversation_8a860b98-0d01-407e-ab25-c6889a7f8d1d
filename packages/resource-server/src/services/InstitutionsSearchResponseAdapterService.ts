/* eslint-disable  @typescript-eslint/no-non-null-assertion */
import { Service } from "typedi";
import { createLogger } from "../lib/Logger";
import _, { Dictionary, isEmpty } from "lodash";
import { estypes } from "@elastic/elasticsearch";
import {
  Institution,
  InstitutionsResponse,
  InstitutionsSearchInput,
  InstitutionFilterAggregation,
  GeoClusteringAggregation,
  ClaimFiltersInput,
  PatientsDiversityRaceInfo,
  PatientsDiversityAgeInfo,
  PatientsDiversitySexInfo,
  PeopleSearchInclusionExclusionAggregationResponse,
  AggregationBucket,
  TaggedInstitutionsSearchResponse,
  DiversityHeatmapAggregationResponse,
  HeatmapAggregation,
  RaceEncounterCountAggregation,
  InstitutionSortOptions,
  EnrollmentRatesByIndication,
  ESEnrollmentRatesByIndication,
  ChildrenMatchedCountAggregation,
  DiversityDashboardAggregation,
  DiversityCategoryAggregation,
  DiversityDashboardAggregationBucket,
  DiversityDashboardAggregationResponse,
  FranceNationalityEncounterCountAggregation,
  HeatmapLocationAggregation,
  SpainNationalityEncounterCountAggregation,
  NamedLocationDiversityHeatmapAggregationResponse
} from "@h1nyc/search-sdk";
import {
  AggregationsTermsAggregateBase,
  ErrorResponseBase,
  SearchResponse,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { getQuerySynonymList } from "../lib/query_understanding/QueryUnderstandingResponseAnalyzer";
import {
  ClaimType,
  ClaimsAggregationDocCountBucket,
  DiagnosesCount,
  DocCountBucket,
  ElasticsearchInstitutionDoc,
  ElasticsearchTaggedInstitutionDoc,
  InstitutionClaimsMapForExports,
  InstitutionSearchFeatureFlags,
  PrescriptionsCount,
  ProceduresCount,
  RequestType,
  SearchDirectlyResponse,
  ValueAggregateBucket,
  mapRaceFilterValueToUkPatientsDiversityField,
  mapNationalityFilterValueToFrancePatientsDiversityField,
  mapNationalityFilterValueToSpainPatientsDiversityField
} from "./InstitutionsResourceService";

interface NestedFilteredMatchingCardinalityAggregate {
  doc_count: number;
  matching: ValueAggregateBucket;
}

const ELASTIC_EMPTY_RESPONSE = {
  took: 0,
  timed_out: false,
  _shards: {
    failed: 0,
    successful: 0,
    total: 0
  },
  hits: {
    total: 0,
    hits: []
  }
};

const CLAIM_COUNT_FIELDS = [
  "internalCount_1_year",
  "internalCount_2_year",
  "internalCount_5_year",
  "internalUniqueCount_1_year",
  "internalUniqueCount_2_year",
  "internalUniqueCount_5_year",
  "count",
  "internalUniqueCount",
  "num_prescriptions",
  "num_prescriptions_1_year",
  "num_prescriptions_2_year",
  "num_prescriptions_5_year"
];

@Service()
export class InstitutionsSearchResponseAdapterService {
  private readonly logger = createLogger(this);

  public adaptToInstitutionSearchResponse(
    input: Readonly<InstitutionsSearchInput>,
    data: Readonly<estypes.SearchResponse<ElasticsearchInstitutionDoc>>,
    peopleData: Readonly<Array<estypes.MsearchResponseItem>>,
    isQueryAnInstitution: boolean,
    featureFlags: InstitutionSearchFeatureFlags,
    keywordIcdCodeSynonyms: string[],
    indicationSynonyms: string[],
    resolvedIndication: string[],
    indicationIcdCodeSynonyms: string[],
    totalInstitutionsInArea?: number,
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    peopleDataForIE?: PeopleSearchInclusionExclusionAggregationResponse | null,
    matchedPatientCountMap?: Dictionary<number>,
    matchedClaimsForInstitutionsMap?: InstitutionClaimsMapForExports,
    patientDiversityDistributionMap?: Dictionary<{
      race: DocCountBucket[];
      gender: DocCountBucket[];
      age: DocCountBucket[];
    }>,
    trialsOngoingCountMap?: Dictionary<number>,
    trialsActivelyRecruitingCountMap?: Dictionary<number>,
    sharedPeopleFromAffiliationCounts?: Dictionary<number>
  ): Readonly<InstitutionsResponse> {
    if (data.hits.total === 0) {
      return this.buildEmptyInstitutionSearchResponse(
        input,
        isQueryAnInstitution,
        keywordIcdCodeSynonyms,
        indicationSynonyms,
        indicationIcdCodeSynonyms,
        queryUnderstandingServiceResponse
      );
    }
    const geoClusteringAggs = data.aggregations
      ?.geo_clustering as AggregationsTermsAggregateBase<GeoClusteringAggregation>;

    let diversityHeatmap, namedLocationDiversityHeatmap, diversityDashboard;
    if (data.aggregations) {
      diversityHeatmap = this.adaptDiversityHeatmapAggregation(
        data.aggregations as unknown as DiversityHeatmapAggregationResponse,
        input
      );

      namedLocationDiversityHeatmap =
        this.adaptNamedLocationDiversityHeatmapAggregation(
          data.aggregations as unknown as NamedLocationDiversityHeatmapAggregationResponse,
          input
        );

      diversityDashboard = this.adaptDiversityDashboardAggregation(
        data.aggregations as unknown as DiversityDashboardAggregationResponse
      );
    }

    const geoClaimsZoomLevel = `claims_${input.filters?.geoStatsRegionLevel?.claims?.zoomLevel}`;
    const showUniquePatients =
      input.filters?.claimsFilters?.showUniquePatients ?? false;
    return {
      total: this.extractTotalValue(data.hits.total ?? 0),
      isNavigationalQuery: isQueryAnInstitution,
      institutions: this.buildInstitutionResultFromElasticsearchHit(
        input,
        data,
        peopleData,
        featureFlags,
        showUniquePatients,
        resolvedIndication,
        peopleDataForIE,
        matchedPatientCountMap,
        matchedClaimsForInstitutionsMap,
        patientDiversityDistributionMap,
        trialsOngoingCountMap,
        trialsActivelyRecruitingCountMap,
        sharedPeopleFromAffiliationCounts
      ),
      totalInstitutionsInArea,
      countryAggregations: this.docCountBucketsToAggregations(
        data.aggregations
          ?.country as AggregationsTermsAggregateBase<DocCountBucket>
      ),
      regionAggregations: this.docCountBucketsToAggregations(
        data.aggregations
          ?.region as AggregationsTermsAggregateBase<DocCountBucket>
      ),
      cityAggregations: this.docCountBucketsToAggregations(
        data.aggregations
          ?.city as AggregationsTermsAggregateBase<DocCountBucket>
      ),
      postalCodeAggregations: this.docCountBucketsToAggregations(
        data.aggregations
          ?.postal_code as AggregationsTermsAggregateBase<DocCountBucket>
      ),
      geoLocationAggregations: this.docCountBucketsToAggregations(
        data.aggregations
          ?.geo_hash as AggregationsTermsAggregateBase<DocCountBucket>
      ),
      geoDistanceAggregations: this.docCountBucketsToAggregations(
        data.aggregations
          ?.geoDistance as AggregationsTermsAggregateBase<DocCountBucket>
      ),

      geoClusteringAggregations:
        geoClusteringAggs?.buckets as GeoClusteringAggregation[],
      geoClaimsAggregations: this.docCountNestedClaimsBucketsToAggregations(
        data.aggregations?.[
          geoClaimsZoomLevel
        ] as AggregationsTermsAggregateBase<ClaimsAggregationDocCountBucket>
      ),
      childrenMatchedCount: data.aggregations?.children_matched_count
        ? (
            data.aggregations
              .children_matched_count as ChildrenMatchedCountAggregation
          ).matching_children_count.value
        : undefined,
      synonyms: [
        ...getQuerySynonymList(input.query, queryUnderstandingServiceResponse),
        ...indicationSynonyms
      ],
      icdCodeSynonyms: [
        ...keywordIcdCodeSynonyms,
        ...indicationIcdCodeSynonyms
      ],
      orgTypesAggregations: this.docCountBucketsToAggregations(
        data.aggregations
          ?.orgTypes as AggregationsTermsAggregateBase<DocCountBucket>
      ),
      diversityHeatmap,
      namedLocationDiversityHeatmap,
      diversityDashboard
    };
  }

  public adaptToEmptyResponse(
    requestType: RequestType,
    isQueryAnInstitution = false
  ): SearchDirectlyResponse | number {
    const data: SearchResponse<ElasticsearchInstitutionDoc> =
      ELASTIC_EMPTY_RESPONSE;
    const peopleData: Array<estypes.MsearchResponseItem> = [];

    if (requestType === "count") {
      return 0;
    }

    return { data, peopleData, isQueryAnInstitution, resolvedIndications: [] };
  }

  public adaptToTaggedInstitutionsSearchResponse(
    taggedInstitutionsData: SearchResponse<ElasticsearchTaggedInstitutionDoc>
  ): TaggedInstitutionsSearchResponse {
    return {
      total: this.extractTotalValue(taggedInstitutionsData.hits.total ?? 0),
      institutions: taggedInstitutionsData.hits.hits.map((hit) => ({
        id: hit._source!.id,
        name: hit._source!.name,
        masterOrganizationId: hit._source!.masterOrganizationId,
        groupH1dnOrganizationId: hit._source!.groupH1dnOrganizationId,
        location: hit._source!.location,
        lat_long_precision: hit._source!.lat_long_precision,
        tagIds: hit._source!.tagIds,
        address: {
          street1: hit._source!["address.street1"],
          street2: hit._source!["address.street2"],
          street3: hit._source!["address.street3"],
          city: hit._source!["address.city"],
          postalCode: hit._source!["address.postal_code"],
          region: hit._source!["address.region"],
          regionCode: hit._source!["address.region_code"],
          country: hit._source!["address.country"],
          countryCode: hit._source!["address.country_code"]
        },
        region: hit._source!.region,
        patientsDiversity: hit._source!.patientsDiversityRatio,
        patientsAge: hit._source!.patientDiversity?.age,
        patientsSex: hit._source!.patientDiversity?.sex
      }))
    };
  }

  private buildEmptyInstitutionSearchResponse(
    input: Readonly<InstitutionsSearchInput>,
    isQueryAnInstitution: boolean,
    keywordIcdCodeSynonyms: string[],
    indicationSynonyms: string[],
    indicationIcdCodeSynonyms: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>
  ): Readonly<InstitutionsResponse> {
    return {
      total: 0,
      isNavigationalQuery: isQueryAnInstitution,
      institutions: [],
      countryAggregations: [],
      regionAggregations: [],
      cityAggregations: [],
      postalCodeAggregations: [],
      geoLocationAggregations: [],
      geoDistanceAggregations: [],
      geoClusteringAggregations: [],
      geoClaimsAggregations: [],
      synonyms: [
        ...getQuerySynonymList(input.query, queryUnderstandingServiceResponse),
        ...indicationSynonyms
      ],
      icdCodeSynonyms: [
        ...keywordIcdCodeSynonyms,
        ...indicationIcdCodeSynonyms
      ],
      orgTypesAggregations: []
    };
  }

  private buildInstitutionResultFromElasticsearchHit(
    input: Readonly<InstitutionsSearchInput>,
    data: Readonly<estypes.SearchResponse<ElasticsearchInstitutionDoc>>,
    peopleData: Readonly<Array<estypes.MsearchResponseItem>>,
    featureFlags: InstitutionSearchFeatureFlags,
    showUniquePatients: boolean,
    resolvedIndication: string[],
    peopleDataForIE?: PeopleSearchInclusionExclusionAggregationResponse | null,
    matchedPatientCountMap?: Dictionary<number>,
    matchedClaimsForInstitutionsMap?: InstitutionClaimsMapForExports,
    patientDiversityDistributionMap?: Dictionary<{
      race: DocCountBucket[];
      gender: DocCountBucket[];
      age: DocCountBucket[];
    }>,
    trialsOngoingCountMap?: Dictionary<number>,
    trialsActivelyRecruitingCountMap?: Dictionary<number>,
    sharedPeopleFromAffiliationCounts?: Dictionary<number>
  ): Institution[] {
    const institutions = data.hits.hits
      .map(
        this.esHitToInstitution(
          input,
          peopleData,
          featureFlags,
          showUniquePatients,
          resolvedIndication,
          peopleDataForIE,
          matchedPatientCountMap,
          matchedClaimsForInstitutionsMap,
          patientDiversityDistributionMap,
          trialsOngoingCountMap,
          trialsActivelyRecruitingCountMap,
          sharedPeopleFromAffiliationCounts
        )
      )
      .filter(this.isDefined);

    return institutions;
  }

  private esHitToInstitution(
    input: Readonly<InstitutionsSearchInput>,
    peopleData: Readonly<Array<estypes.MsearchResponseItem>>,
    featureFlags: InstitutionSearchFeatureFlags,
    uniquePatientToggleFromInput: boolean,
    resolvedIndication: string[],
    peopleDataForIE?: PeopleSearchInclusionExclusionAggregationResponse | null,
    matchedPatientCountMap?: Dictionary<number>,
    matchedClaimsForInstitutionsMap?: InstitutionClaimsMapForExports,
    patientDiversityDistributionMap?: Dictionary<{
      race: DocCountBucket[];
      gender: DocCountBucket[];
      age: DocCountBucket[];
    }>,
    trialsOngoingCountMap?: Dictionary<number>,
    trialsActivelyRecruitingCountMap?: Dictionary<number>,
    sharedPeopleFromAffiliationCounts?: Dictionary<number>
  ) {
    return (
      hit: estypes.SearchHit<ElasticsearchInstitutionDoc>,
      index: number
    ): Institution | undefined => {
      const _source = hit._source;

      if (!_source) {
        return undefined;
      }
      const {
        enableUniquePatientCountForClaims,
        disableUniquePatientCountForOnlyProcedures
      } = featureFlags;
      let matchedProcedures = this.calculateMatchedProcedures(hit);

      let matchedPrescriptions = this.calculateMatchedPrescriptions(hit);

      let totalDiagnoses;
      let matchedDiagnoses;
      let matchedCcsr;
      if (input.sortBy === InstitutionSortOptions.UK_DIVERSITY) {
        totalDiagnoses = _source["totalClaimCountUk"] ?? 0;
        matchedDiagnoses = this.calculateMatchedUkDiversityClaims(hit);
      } else if (input.sortBy === InstitutionSortOptions.FRANCE_DIVERSITY) {
        totalDiagnoses = _source["totalClaimCountFrance"] ?? 0;
        matchedDiagnoses = this.calculateMatchedFranceDiversityClaims(hit);
      } else if (input.sortBy === InstitutionSortOptions.SPAIN_DIVERSITY) {
        totalDiagnoses = _source["totalClaimCountSpain"] ?? 0;
        matchedDiagnoses = this.calculateMatchedSpainDiversityClaims(hit);
      } else if (uniquePatientToggleFromInput) {
        totalDiagnoses = _source.totalPatientDocs ?? 0;
        matchedDiagnoses = this.getHitsTotal(
          hit.inner_hits?.patient_claims_matching_count?.hits?.total ?? 0
        );
      } else {
        matchedCcsr = this.calculateMatchedCcsr(hit);
        totalDiagnoses = _source.diagnosis_count ?? 0;
        matchedDiagnoses = this.calculateMatchedDiagnoses(hit);
      }

      const {
        diagnosisTimeframeCountOrZero,
        proceduresTimeframeCountOrZero,
        prescriptionsTimeframeCountOrZero
      } = this.getDefaultClaimsCount(input, featureFlags, hit);

      if (matchedDiagnoses === undefined && matchedCcsr === undefined) {
        matchedDiagnoses = diagnosisTimeframeCountOrZero;
      } else if (!_.isUndefined(matchedCcsr)) {
        matchedDiagnoses =
          (matchedDiagnoses ?? diagnosisTimeframeCountOrZero ?? 0) +
          matchedCcsr;
      }
      if (matchedProcedures === undefined) {
        matchedProcedures = proceduresTimeframeCountOrZero;
      }
      if (matchedPrescriptions === undefined) {
        matchedPrescriptions = prescriptionsTimeframeCountOrZero;
      }
      let trialOngoingCount = undefined;
      let trialActivelyRecruitingCount = undefined;

      if (!isEmpty(trialsOngoingCountMap)) {
        trialOngoingCount = trialsOngoingCountMap[hit._id] ?? 0;
      }

      if (!isEmpty(trialsActivelyRecruitingCountMap)) {
        trialActivelyRecruitingCount =
          trialsActivelyRecruitingCountMap[hit._id] ?? 0;
      }

      const matchedPayments = this.calculateMatchedPayments(hit);
      const matchedCongresses = this.calculateMatchedCongresses(hit);
      const matchedTrials = this.calculateMatchedTrials(hit);
      const diagnosisCountField = this.getNestedInnerHitsForClaims(
        "diagnoses",
        featureFlags,
        input.sortBy,
        input.filters?.claimsFilters ?? undefined
      );
      const matchedDiagnosesDetails = this.extractMatchedClaims(
        "diagnoses_collection",
        hit,
        enableUniquePatientCountForClaims && uniquePatientToggleFromInput,
        matchedClaimsForInstitutionsMap,
        diagnosisCountField
      );
      const procedureCountField = this.getNestedInnerHitsForClaims(
        "procedures",
        featureFlags,
        input.sortBy,
        input.filters?.claimsFilters ?? undefined
      );
      const matchedProceduresDetails = this.extractMatchedClaims(
        "procedures_collection",
        hit,
        undefined,
        matchedClaimsForInstitutionsMap,
        procedureCountField
      );

      let matchedPeople: number | undefined;
      let matchedPublications: number | undefined;
      if (peopleDataForIE?.aggregations?.filtered_institutions?.doc_count) {
        const aggregationBuckets: AggregationBucket[] =
          peopleDataForIE.aggregations.filtered_institutions.filtered_ids
            .by_institution.buckets;
        const matchedBucket = aggregationBuckets.find(
          (bucket) => bucket.key === _source.masterOrganizationId?.toString()
        );
        if (matchedBucket) {
          matchedPeople = matchedBucket.to_parent.doc_count;
        }
      } else if (peopleData.length && peopleData[index]) {
        const peopleResponse = peopleData[index];

        if (this.isErrorResponse(peopleResponse)) {
          this.logger.error(peopleResponse.error);
        } else {
          matchedPeople =
            this.calculateMatchedHCPsFromPeopleSearchResults(peopleResponse);
          matchedPublications =
            this.calculateMatchedPublications(peopleResponse);
        }
      }

      let matchedAffiliatedCount: number | undefined;

      if (
        sharedPeopleFromAffiliationCounts &&
        sharedPeopleFromAffiliationCounts[hit._id]
      ) {
        matchedAffiliatedCount = sharedPeopleFromAffiliationCounts[hit._id];
      }

      let hasCTMSData: boolean | undefined;
      if (featureFlags.enableCTMSV2) {
        hasCTMSData = _source.inCtmsNetwork ?? _source.hasCtms;
      }

      const patientDiversityDistributionBuckets =
        patientDiversityDistributionMap &&
        _.has(patientDiversityDistributionMap, hit._id)
          ? patientDiversityDistributionMap[hit._id]
          : { race: [], gender: [], age: [] };
      let patientsDiversity: PatientsDiversityRaceInfo | undefined =
        this.getRaceDistribution(
          patientDiversityDistributionBuckets.race,
          _source.patientsDiversityRatio
        );

      if (input.sortBy === InstitutionSortOptions.UK_DIVERSITY) {
        patientsDiversity = _source.patientsDiversityRatioUk;
      } else if (input.sortBy === InstitutionSortOptions.FRANCE_DIVERSITY) {
        patientsDiversity = _source.patientsDiversityRatioFrance;
      } else if (input.sortBy === InstitutionSortOptions.SPAIN_DIVERSITY) {
        patientsDiversity = _source.patientsDiversityRatioSpain;
      }

      const enrollmentRatesByIndications = this.getEnrollmentRatesByIndication(
        _source.enrollment_rates_by_indication
      );

      const matchedEnrollmentRatesByIndications =
        this.matchEnrollmentRateIndicationWithKeywordOrIndication(
          this.getEnrollmentRatesByIndication(
            hit.inner_hits?.enrollment_rates_by_indication?.hits.hits.map(
              (innerHit: any) => innerHit._source
            )
          ),
          enrollmentRatesByIndications,
          resolvedIndication,
          input.filters?.indications
        );

      return {
        id: _source.masterOrganizationId ?? _source.id,
        name: _source.name ?? "",
        nameTranslations: _source.nameTranslations,
        institutionType: _source.institution_type,
        website: _source.website,
        beds: _source.beds,
        region: _source.region ?? "",
        linkedinUrl: _source.linkedin_url,
        twitterUrl: _source.twitter_url,
        hasCTMSData,
        people: {
          total: _source.person_count ?? 0,
          matched: matchedPeople,
          affiliatedMatched: matchedAffiliatedCount ?? 0
        },
        publications: {
          total: _source.publication_count ?? 0,
          matched: matchedPublications
        },
        congresses: {
          total: _source.conference_count ?? 0,
          matched: matchedCongresses
        },
        trials: {
          total: _source.trials_count ? _source.trials_count : 0, // TODO: remove this hack after institution index is fixed to always have trials_count for every institution.
          matched: matchedTrials
        },
        diagnoses: {
          total: totalDiagnoses,
          matched: matchedDiagnoses
        },
        procedures: {
          total:
            !disableUniquePatientCountForOnlyProcedures &&
            enableUniquePatientCountForClaims &&
            uniquePatientToggleFromInput
              ? _source.proceduresUniqueCount ?? 0
              : _source.procedure_count ?? 0,
          matched: matchedProcedures
        },
        prescriptions: {
          total: _source.num_prescriptions ?? 0,
          matched: matchedPrescriptions
        },
        payments: {
          total:
            (_source.general_institution_payment_total ?? 0) +
            (_source.research_institution_payment_total ?? 0),
          matched: matchedPayments
        },
        trialOngoingCount,
        trialActivelyRecruitingCount,
        address: {
          street1: _source["address.street1"],
          street2: _source["address.street2"],
          street3: _source["address.street3"],
          city: _source["address.city"],
          postalCode: _source["address.postal_code"],
          region: _source["address.region"],
          regionCode: _source["address.region_code"],
          country: _source["address.country"],
          countryCode: _source["address.country_code"]
        },
        addressTranslations: _source.addressTranslations,
        location:
          _source.location?.lat && _source.location?.lon
            ? {
                lat: parseFloat(_source.location.lat),
                lon: parseFloat(_source.location.lon)
              }
            : undefined,
        lat_long_precision: _source.lat_long_precision,
        matchedDiagnosesDetails,
        matchedProceduresDetails,
        patientsDiversity,
        patientsAge: this.getAgeDistribution(
          patientDiversityDistributionBuckets.age,
          _source.patientDiversity?.age
        ),
        patientsSex: this.getGenderDistribution(
          patientDiversityDistributionBuckets.gender,
          _source.patientDiversity?.sex
        ),
        nciDesignatedCancerCenter: _source.nci_designated_cancer_center,
        nationalComprehensiveCancerNetworkMember:
          _source.national_comprehensive_cancer_network_member,
        teachingHospital: _source.teaching_hospital,
        communityHospital: _source.community_hospital,
        researchFacility: _source.research_facility,
        cro: _source.cro,
        flags: _source.flags
          ? {
              nciDesignatedCancerCenter:
                _source.flags?.nci_designated_cancer_center,
              nationalComprehensiveCancerNetworkMember:
                _source.flags?.national_comprehensive_cancer_network_member,
              teachingHospital: _source.flags?.teaching_hospital,
              nihCommunitySite: _source.flags?.nih_community_site,
              minorityCommunitySite: _source.flags?.minority_community_site,
              researchFacility: _source.flags?.research_facility,
              cro: _source.flags?.cro,
              partOfSystem: {
                partOfSystem: _source.flags?.part_of_system.part_of_system,
                healthSystem: _source.flags?.part_of_system.health_system,
                universitySystem:
                  _source.flags?.part_of_system.university_system
              },
              privatePractice: {
                privatePractice:
                  _source.flags?.private_practice.private_practice,
                large: _source.flags?.private_practice.large,
                small: _source.flags?.private_practice.small
              },
              government: {
                government: _source.flags?.government.government,
                military: _source.flags?.government.military,
                veteransAffairs: _source.flags?.government.veterans_affairs
              }
            }
          : undefined,
        orgTypes: _source.orgTypes,
        orgTypesLevel2: _source.orgTypesLevel2,
        orgTypesLevel3: _source.orgTypesLevel3,
        trialEnrollmentRate: _source.trialEnrollmentRate,
        enrollmentRatesByIndications: enrollmentRatesByIndications,
        matchedEnrollmentRatesByIndications:
          matchedEnrollmentRatesByIndications,
        totalTags: _source.tagIds?.length ?? 0,
        ultimateParentId: _source.ultimate_parent_id,
        ultimateParentName: _source.ultimate_parent_name,
        childAffiliationCount: _source.child_affiliation_count
      };
    };
  }

  private getAgeDistribution(
    ageDistributionFromPatientDocs: DocCountBucket[],
    ageDistributionFromParentSource: PatientsDiversityAgeInfo[] | undefined
  ): PatientsDiversityAgeInfo[] {
    if ((ageDistributionFromPatientDocs ?? []).length > 0) {
      return ageDistributionFromPatientDocs.map((bucket) => {
        return { range: bucket.key, count: bucket.doc_count };
      });
    } else if (ageDistributionFromParentSource) {
      return ageDistributionFromParentSource;
    } else {
      return [];
    }
  }

  private getEnrollmentRatesByIndication(
    esenrollmentRatesByIndications: ESEnrollmentRatesByIndication[] | undefined
  ): EnrollmentRatesByIndication[] {
    const enrollmentRatesByIndications: EnrollmentRatesByIndication[] = [];
    if (esenrollmentRatesByIndications) {
      esenrollmentRatesByIndications.forEach(
        (esenrollmentRatesByIndication) => {
          enrollmentRatesByIndications.push({
            enrollmentRate: esenrollmentRatesByIndication.enrollment_rate,
            indication: esenrollmentRatesByIndication.indication,
            trialCount: esenrollmentRatesByIndication.total_trials
          });
        }
      );
    }

    return enrollmentRatesByIndications;
  }

  private matchEnrollmentRateIndicationWithKeywordOrIndication(
    matchedEnIndications: EnrollmentRatesByIndication[] | undefined,
    enIndications: EnrollmentRatesByIndication[] | undefined,
    resolvedIndications: string[] | undefined,
    indications: string[] | undefined | null
  ): EnrollmentRatesByIndication[] {
    const enrollmentRatesByIndications: EnrollmentRatesByIndication[] = [];
    // if matched indications are returned from elastic search then there is no need to do manual mapping
    if (matchedEnIndications && matchedEnIndications.length > 0) {
      return matchedEnIndications;
    }

    // merge indications -> keyword resolved or user selected indication list
    const mergedIndicationsArray: string[] = [
      ...new Set([...(resolvedIndications ?? []), ...(indications ?? [])])
    ];

    if (
      enIndications &&
      enIndications.length > 0 &&
      mergedIndicationsArray.length > 0
    ) {
      enIndications.forEach((enIndication) => {
        if (
          enIndication.indication &&
          mergedIndicationsArray.includes(enIndication.indication)
        ) {
          enrollmentRatesByIndications.push(enIndication);
        }
      });
    }

    return enrollmentRatesByIndications;
  }

  private getGenderDistribution(
    genderDistributionFromPatientDocs: DocCountBucket[],
    genderDistributionFromParentSource: PatientsDiversitySexInfo[] | undefined
  ): PatientsDiversityAgeInfo[] {
    if ((genderDistributionFromPatientDocs ?? []).length > 0) {
      return genderDistributionFromPatientDocs.map((bucket) => {
        return { sex: bucket.key, count: bucket.doc_count };
      });
    } else if (genderDistributionFromParentSource) {
      return genderDistributionFromParentSource;
    } else {
      return [];
    }
  }

  private breakoutAsianPacificIslanderDistribution(
    asianPacificIslanderDistribution: number,
    raceDistributionFromParentSource: PatientsDiversityRaceInfo | undefined
  ) {
    if (!raceDistributionFromParentSource) {
      return {
        asian: undefined,
        pacificIslander: undefined
      };
    }

    const { asian, pacificIslander, asianPacificIslander } =
      raceDistributionFromParentSource;

    if (
      !asianPacificIslander ||
      asian === undefined ||
      pacificIslander === undefined
    ) {
      return {
        asian: undefined,
        pacificIslander: undefined
      };
    }

    const percentOfAsianOverCombined = asian / asianPacificIslander;
    const percentOfPacificIslanderOverCombined =
      pacificIslander / asianPacificIslander;

    return {
      asian: asianPacificIslanderDistribution * percentOfAsianOverCombined,
      pacificIslander:
        asianPacificIslanderDistribution * percentOfPacificIslanderOverCombined
    };
  }

  private getRaceDistribution(
    raceDistributionFromPatientDocs: DocCountBucket[],
    raceDistributionFromParentSource: PatientsDiversityRaceInfo | undefined
  ): PatientsDiversityRaceInfo | undefined {
    if ((raceDistributionFromPatientDocs ?? []).length > 0) {
      // 'Other' race patient count should be removed from calculation
      const totalPatients = raceDistributionFromPatientDocs
        .filter(({ key }) => key !== "Other" && key !== "Not Disclosed")
        .reduce((sum, raceInfoBucket) => sum + raceInfoBucket.doc_count, 0);

      const asianPacificIslander =
        (raceDistributionFromPatientDocs.find(
          ({ key }) => key === "Asian Pacific Islander"
        )?.doc_count ?? 0) / totalPatients;
      const nonWhite =
        1 -
        (raceDistributionFromPatientDocs.find(
          ({ key }) => key === "White Non-Hispanic"
        )?.doc_count ?? 0) /
          totalPatients;

      const { asian, pacificIslander } =
        this.breakoutAsianPacificIslanderDistribution(
          asianPacificIslander,
          raceDistributionFromParentSource
        ) ?? {};

      return {
        asianPacificIslander,
        asian,
        nonWhite,
        pacificIslander,
        americanIndianOrAlaskaNative:
          (raceDistributionFromPatientDocs.find(
            ({ key }) => key === "American Indian Or Alaska Native"
          )?.doc_count ?? 0) / totalPatients,
        blackNonHispanic:
          (raceDistributionFromPatientDocs.find(
            ({ key }) => key === "Black Non-Hispanic"
          )?.doc_count ?? 0) / totalPatients,
        hispanic:
          (raceDistributionFromPatientDocs.find(({ key }) => key === "Hispanic")
            ?.doc_count ?? 0) / totalPatients,
        mixedBrazil:
          (raceDistributionFromPatientDocs.find(
            ({ key }) => key === "Mixed (Brazil Only)"
          )?.doc_count ?? 0) / totalPatients,
        indigenousBrazil:
          (raceDistributionFromPatientDocs.find(
            ({ key }) => key === "Indigenous (Brazil Only)"
          )?.doc_count ?? 0) / totalPatients,
        whiteNonHispanic:
          (raceDistributionFromPatientDocs.find(
            ({ key }) => key === "White Non-Hispanic"
          )?.doc_count ?? 0) / totalPatients
      };
    } else if (raceDistributionFromParentSource) {
      return raceDistributionFromParentSource;
    } else {
      return undefined;
    }
  }

  private extractMatchedClaims(
    field: "procedures_collection" | "diagnoses_collection",
    hit: Readonly<estypes.SearchHit<ElasticsearchInstitutionDoc>>,
    showUniquePatients?: boolean,
    matchedClaimsForInstitutionsMap?: InstitutionClaimsMapForExports,
    claimCountField?: string
  ) {
    const IEClaimsExists =
      Object.keys(matchedClaimsForInstitutionsMap?.diagnosesCountMap ?? {})
        .length > 0 ||
      Object.keys(matchedClaimsForInstitutionsMap?.proceduresCountMap ?? {})
        .length > 0;
    if (IEClaimsExists) {
      const claimsDictionary =
        field == "diagnoses_collection"
          ? matchedClaimsForInstitutionsMap!.diagnosesCountMap[hit._id] ?? {}
          : matchedClaimsForInstitutionsMap!.proceduresCountMap[hit._id] ?? {};
      const matchedClaimsPatientDetails = [];
      for (const [key, value] of Object.entries(claimsDictionary)) {
        const details = {
          code: key,
          description: value.description,
          count: value.count
        };
        matchedClaimsPatientDetails.push(details);
      }
      return matchedClaimsPatientDetails;
    } else if (hit.inner_hits && hit.inner_hits[field]) {
      return hit.inner_hits[field].hits.hits
        .filter((hit) => hit?._source)
        .map((hit) => {
          const claimCountValue = claimCountField
            ? _.get(hit!._source, claimCountField)
            : 0;
          return {
            code: hit!._source!.code_eng,
            description: hit!._source!.description_eng,
            count: claimCountValue
          };
        });
    }

    return [];
  }

  private calculateMatchedHCPsFromPeopleSearchResults(
    peopleData: Readonly<estypes.MsearchMultiSearchItem>
  ) {
    return this.extractTotalValue(peopleData.hits.total ?? 0);
  }

  private calculateMatchedCongresses =
    this.calculateMatchedNestedDocuments.bind(this, "congresses");
  private calculateMatchedTrials = this.calculateMatchedNestedDocuments.bind(
    this,
    "trials"
  );

  private calculateMatchedNestedDocuments(
    field: "congresses" | "trials",
    hit: Readonly<estypes.SearchHit<ElasticsearchInstitutionDoc>>
  ) {
    if (hit.inner_hits && hit.inner_hits[field]) {
      return this.extractTotalValue(hit.inner_hits[field].hits.total ?? 0);
    }

    return undefined;
  }

  private calculateMatchedProcedures = this.calculateMatchedClaims.bind(
    this,
    "procedures"
  );
  private calculateMatchedDiagnoses = this.calculateMatchedClaims.bind(
    this,
    "diagnoses"
  );
  private calculateMatchedPrescriptions = this.calculateMatchedClaims.bind(
    this,
    "prescriptions"
  );
  private calculateMatchedUkDiversityClaims = this.calculateMatchedClaims.bind(
    this,
    "patientClaimsUk"
  );
  private calculateMatchedCcsr = this.calculateMatchedClaims.bind(this, "ccsr");
  private calculateMatchedFranceDiversityClaims =
    this.calculateMatchedClaims.bind(this, "patientClaimsFrance");
  private calculateMatchedSpainDiversityClaims =
    this.calculateMatchedClaims.bind(this, "patientClaimsSpain");

  private calculateMatchedClaims(
    field:
      | "procedures"
      | "diagnoses"
      | "prescriptions"
      | "patientClaimsUk"
      | "patientClaimsFrance"
      | "patientClaimsSpain"
      | "ccsr",
    hit: Readonly<estypes.SearchHit<ElasticsearchInstitutionDoc>>
  ) {
    if (hit.inner_hits && hit.inner_hits[field]) {
      return hit.inner_hits[field].hits.hits
        .filter((hit) => hit?.fields)
        .map((hit) => {
          let count = "count";

          for (const countField of CLAIM_COUNT_FIELDS) {
            if (`${field}.${countField}` in hit!.fields!) {
              count = countField;
              break;
            }
          }

          return +hit!.fields![`${field}.${count}`];
        })
        .reduce((a, b) => a + b, 0);
    }

    return undefined;
  }

  private calculateMatchedPublications(
    peopleData: Readonly<estypes.MsearchMultiSearchItem>
  ) {
    if (peopleData.aggregations) {
      const cardinalityAggregation = peopleData.aggregations
        .publications as NestedFilteredMatchingCardinalityAggregate;

      return cardinalityAggregation.matching.total.value ?? 0;
    }

    return undefined;
  }

  private calculateMatchedPayments(
    hit: Readonly<estypes.SearchHit<ElasticsearchInstitutionDoc>>
  ) {
    if (hit.inner_hits?.payments) {
      return Math.round(
        hit
          .inner_hits!.payments.hits.hits.filter((hit) => hit?.fields)
          .map((hit) => +hit!.fields!["payments.amount"])
          .reduce((a, b) => a + b, 0)
      );
    }

    return undefined;
  }

  private getDefaultClaimsCount(
    input: Readonly<InstitutionsSearchInput>,
    featureFlags: InstitutionSearchFeatureFlags,
    hit: estypes.SearchHit<ElasticsearchInstitutionDoc>
  ) {
    const {
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures
    } = featureFlags;

    let claimsCountField = "";
    const isTimeFrameApplied = _.isNumber(
      input.filters?.claimsFilters?.timeFrame
    );
    if (isTimeFrameApplied) {
      claimsCountField =
        claimsCountField + `_${input.filters?.claimsFilters?.timeFrame}_year`;
    } else {
      return {
        diagnosisTimeframeCountOrZero: undefined,
        proceduresTimeframeCountOrZero: undefined,
        prescriptionsTimeframeCountOrZero: undefined
      };
    }

    const uniquePatientToggleFromInput =
      input.filters?.claimsFilters?.showUniquePatients ?? false;
    const claimsCountTotalFieldForDiagnoses =
      enableUniquePatientCountForClaims && uniquePatientToggleFromInput
        ? "diagnosesUniqueCount"
        : "diagnosesCount";
    const claimsCountTotalFieldForProcedures =
      !disableUniquePatientCountForOnlyProcedures &&
      enableUniquePatientCountForClaims &&
      uniquePatientToggleFromInput
        ? "proceduresUniqueCount"
        : "proceduresCount";
    const diagnosisTimeframe =
      `${claimsCountTotalFieldForDiagnoses}${claimsCountField}` as keyof DiagnosesCount;
    const proceduresTimeframe =
      `${claimsCountTotalFieldForProcedures}${claimsCountField}` as keyof ProceduresCount;

    const isCcsrApplied = !!input.filters?.claimsFilters?.ccsr?.length;
    const diagnosisTimeframeCountOrZero =
      isTimeFrameApplied && !isCcsrApplied
        ? hit._source![diagnosisTimeframe]!
        : 0;
    const proceduresTimeframeCountOrZero = isTimeFrameApplied
      ? hit._source![proceduresTimeframe]!
      : 0;
    const prescriptionsTimeframe =
      `num_prescriptions${claimsCountField}` as keyof PrescriptionsCount;
    const prescriptionsTimeframeCountOrZero = isTimeFrameApplied
      ? hit._source![prescriptionsTimeframe]!
      : 0;
    return {
      diagnosisTimeframeCountOrZero,
      proceduresTimeframeCountOrZero,
      prescriptionsTimeframeCountOrZero
    };
  }

  private isErrorResponse(
    response: Readonly<estypes.MsearchResponseItem>
  ): response is ErrorResponseBase {
    return !!(response as ErrorResponseBase).error;
  }

  private extractTotalValue(total: number | estypes.SearchTotalHits): number {
    if (typeof total === "number") {
      return total;
    }
    return total.value;
  }

  private docCountBucketsToAggregations(
    agg: AggregationsTermsAggregateBase<DocCountBucket> | undefined
  ): InstitutionFilterAggregation[] {
    if (!agg || !agg.buckets) {
      return [];
    }

    return (agg.buckets as DocCountBucket[]).map((bucket) => {
      return {
        id: bucket.key,
        count: bucket.doc_count
      };
    });
  }

  private docCountNestedClaimsBucketsToAggregations(
    agg:
      | AggregationsTermsAggregateBase<ClaimsAggregationDocCountBucket>
      | undefined
  ): InstitutionFilterAggregation[] {
    if (!agg || !agg.buckets) {
      return [];
    }

    return (agg.buckets as ClaimsAggregationDocCountBucket[]).map((bucket) => {
      const procedureCount = bucket.procedures.filteredClaims.counts.value;
      const diagnosesCount = bucket.diagnoses.filteredClaims.counts.value;
      return {
        id: bucket.key,
        count: procedureCount + diagnosesCount
      };
    });
  }

  private isDefined(institution: unknown): institution is Institution {
    return Boolean(institution);
  }

  private getNestedInnerHitsForClaims(
    claimType: ClaimType,
    featureFlags: InstitutionSearchFeatureFlags,
    sortBy: InstitutionSortOptions | undefined,
    claimsFilters?: ClaimFiltersInput
  ) {
    if (claimType === "prescriptions") {
      return claimsFilters?.timeFrame
        ? `num_prescriptions_${claimsFilters!.timeFrame}_year`
        : "num_prescriptions";
    } else if (
      claimType === "diagnoses" &&
      sortBy === InstitutionSortOptions.UK_DIVERSITY
    ) {
      return "patientClaimsUk.count";
    }
    const {
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures,
      enableClaimsFilteringMatchedCountsUpdate
    } = featureFlags;
    const uniquePatientToggleFromInput =
      claimsFilters?.showUniquePatients ?? false;
    const isProcedureWithUniqueCount =
      claimType === "procedures" && !disableUniquePatientCountForOnlyProcedures;
    const isDiagnosis = claimType === "diagnoses";
    const shouldUseUniquePatientCount =
      (isProcedureWithUniqueCount || isDiagnosis) &&
      enableUniquePatientCountForClaims;

    const claimsCountFieldForTimeFrame =
      shouldUseUniquePatientCount && uniquePatientToggleFromInput
        ? "internalUniqueCount"
        : "internalCount";
    const claimsCountFieldWithoutTimeFrame =
      shouldUseUniquePatientCount && uniquePatientToggleFromInput
        ? "internalUniqueCount"
        : "count";

    const innerHitsCountForClaims =
      enableClaimsFilteringMatchedCountsUpdate && claimsFilters?.timeFrame
        ? `${claimsCountFieldForTimeFrame}_${claimsFilters!.timeFrame}_year`
        : claimsCountFieldWithoutTimeFrame;

    return innerHitsCountForClaims;
  }

  private getHitsTotal(total: number | SearchTotalHits) {
    if (typeof total === "number") {
      return total;
    }

    return total.value;
  }

  private computeTotalClaimsBasedOnRaceFilter(
    input: Readonly<InstitutionsSearchInput>,
    filtered: RaceEncounterCountAggregation
  ) {
    if (!input.filters?.diversityFilters?.race?.length) {
      return (
        filtered.patientCountAsian.value +
        filtered.patientCountBlack.value +
        filtered.patientCountMixed.value +
        filtered.patientCountOther.value +
        filtered.patientCountUnknown.value
      );
    }

    return input.filters.diversityFilters.race.reduce((count, race) => {
      count +=
        filtered[mapRaceFilterValueToUkPatientsDiversityField(race)].value;
      return count;
    }, 0);
  }

  private adaptDiversityHeatmapAggregation(
    { heatmap }: DiversityHeatmapAggregationResponse,
    input: Readonly<InstitutionsSearchInput>
  ): HeatmapAggregation[] | undefined {
    if (!heatmap) {
      return undefined;
    }

    return heatmap.buckets.map((bucket) => {
      return {
        subIcbCode: bucket.key,
        count: this.computeTotalClaimsBasedOnRaceFilter(
          input,
          bucket.nested_agg.filtered_matching
        )
      };
    });
  }

  private computeTotalClaimsBasedOnNationalityFilterForFrance(
    input: Readonly<InstitutionsSearchInput>,
    filtered: FranceNationalityEncounterCountAggregation
  ) {
    if (!input.filters?.diversityFilters?.nationality?.length) {
      return (
        filtered.patientCountFranceNative.value +
        filtered.patientCountEuropeanUnion.value +
        filtered.patientCountOtherEuropean.value +
        filtered.patientCountCentralAmerica.value +
        filtered.patientCountCentralAndSouthAfrica.value +
        filtered.patientCountSoutheastAsia.value +
        filtered.patientCountEastAsia.value +
        filtered.patientCountMiddleEastAndNorthAfrica.value +
        filtered.patientCountNorthAmerica.value +
        filtered.patientCountOceania.value +
        filtered.patientCountSouthAmerica.value +
        filtered.patientCountSouthAsia.value
      );
    }

    return input.filters.diversityFilters.nationality.reduce(
      (count, nationality) => {
        count +=
          filtered[
            mapNationalityFilterValueToFrancePatientsDiversityField(nationality)
          ].value;
        return count;
      },
      0
    );
  }

  private computeTotalClaimsBasedOnNationalityFilterForSpain(
    input: Readonly<InstitutionsSearchInput>,
    filtered: SpainNationalityEncounterCountAggregation
  ) {
    if (!input.filters?.diversityFilters?.nationality?.length) {
      return (
        filtered.patientCountSpain.value +
        filtered.patientCountEuropeanUnion.value +
        filtered.patientCountOtherEuropean.value +
        filtered.patientCountCentralAmerica.value +
        filtered.patientCountCentralAndSouthAfrica.value +
        filtered.patientCountSoutheastAsia.value +
        filtered.patientCountEastAsia.value +
        filtered.patientCountMiddleEastAndNorthAfrica.value +
        filtered.patientCountNorthAmerica.value +
        filtered.patientCountOceania.value +
        filtered.patientCountSouthAmerica.value +
        filtered.patientCountSouthAsia.value +
        filtered.patientCountCentralAsia.value +
        filtered.patientCountOther.value +
        filtered.patientCountUnknown.value
      );
    }

    return input.filters.diversityFilters.nationality.reduce(
      (count, nationality) => {
        count +=
          filtered[
            mapNationalityFilterValueToSpainPatientsDiversityField(nationality)
          ].value;
        return count;
      },
      0
    );
  }

  private adaptNamedLocationDiversityHeatmapAggregation(
    {
      france_heatmap,
      spain_heatmap
    }: NamedLocationDiversityHeatmapAggregationResponse,
    input: Readonly<InstitutionsSearchInput>
  ): HeatmapLocationAggregation[] | undefined {
    if (france_heatmap) {
      return france_heatmap.buckets.map((bucket) => {
        return {
          namedLocation: bucket.key,
          count: this.computeTotalClaimsBasedOnNationalityFilterForFrance(
            input,
            bucket.nested_agg.filtered_matching
          )
        };
      });
    } else if (spain_heatmap) {
      return spain_heatmap.buckets.map((bucket) => {
        return {
          namedLocation: bucket.key,
          count: this.computeTotalClaimsBasedOnNationalityFilterForSpain(
            input,
            bucket.nested_agg.filtered_matching
          )
        };
      });
    }

    return undefined;
  }

  private adaptDiversityDashboardAggregation({
    france_diversity_dashboard,
    spain_diversity_dashboard
  }: DiversityDashboardAggregationResponse):
    | DiversityDashboardAggregation
    | undefined {
    const dashboard = france_diversity_dashboard ?? spain_diversity_dashboard;
    if (!dashboard) {
      return undefined;
    }

    const totalNationalityCount =
      dashboard.location_filtered.nationality_counts.total_nationality_count
        .value;
    const nationality =
      dashboard.location_filtered.nationality_counts.by_nationality.buckets.map(
        (bucket) =>
          this.toDiversityCategoryAggregation(bucket, totalNationalityCount)
      );

    const totalAgeCount =
      dashboard.location_filtered.age_counts.total_age_count.value;
    const age = dashboard.location_filtered.age_counts.by_age.buckets.map(
      (bucket) => this.toDiversityCategoryAggregation(bucket, totalAgeCount)
    );

    const totalGenderCount =
      dashboard.location_filtered.gender_counts.total_gender_count.value;
    const gender =
      dashboard.location_filtered.gender_counts.by_gender.buckets.map(
        (bucket) =>
          this.toDiversityCategoryAggregation(bucket, totalGenderCount)
      );

    const totalEducationCount =
      dashboard.location_filtered.education_counts.total_education_count.value;
    const education =
      dashboard.location_filtered.education_counts.by_education.buckets.map(
        (bucket) =>
          this.toDiversityCategoryAggregation(bucket, totalEducationCount)
      );

    return {
      age,
      gender,
      education,
      nationality
    };
  }

  private toDiversityCategoryAggregation(
    bucket: DiversityDashboardAggregationBucket,
    totalNationalityCount: number
  ): DiversityCategoryAggregation {
    const count = bucket.count.value ?? 0;
    const percentage = totalNationalityCount
      ? count / totalNationalityCount
      : 0;
    return {
      category: bucket.key,
      count,
      percentage
    };
  }
}
