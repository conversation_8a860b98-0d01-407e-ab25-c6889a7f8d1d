import { Service } from "typedi";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { ExtractedEntitiesV2, KeywordSearchInput } from "@h1nyc/search-sdk";
import { createLogger } from "../lib/Logger";
import {
  getLanguageDetector,
  KeywordSearchFeatureFlags
} from "./KeywordSearchResourceServiceRewrite";
import { estypes } from "@elastic/elasticsearch";
import { ElasticSearchService } from "./ElasticSearchService";
import _ from "lodash";
import { ConfigService } from "./ConfigService";
import { ErrorResponseBase } from "@elastic/elasticsearch/lib/api/types";
import { getEditDistanceMatrix } from "../util/editDistanceUtil";

type MSearchRequest = [
  estypes.MsearchMultisearchHeader,
  estypes.MsearchMultisearchBody
];

const HEADER: Readonly<estypes.MsearchMultisearchHeader> = {};

export const ELASTICSEARCH_FIELD_MAPPING: Record<
  string,
  { path?: string; field: string; nested_source_path?: string; isNested: boolean }
> = {
  "locations.country": {
    path: "addressesForHCPU",
    field: "addressesForHCPU.country",
    nested_source_path: "addressesForHCPU.filters.country",
    isNested: true
  },
  "locations.state": {
    path: "addressesForHCPU",
    field: "addressesForHCPU.region",
    nested_source_path: "addressesForHCPU.filters.region",
    isNested: true
  },
  "locations.city": {
    path: "addressesForHCPU",
    field: "addressesForHCPU.city",
    nested_source_path: "addressesForHCPU.filters.city",
    isNested: true
  },
  "claims.diagnoses": {
    path: "DRG_diagnoses",
    field: "DRG_diganoses.codeAndDescription_eng",
    nested_source_path: "DRG_diganoses.codeAndDescription_eng",
    isNested: true
  },
  "claims.procedures": {
    path: "DRG_procedures",
    field: "DRG_procedures.codeAndDescription_eng",
    nested_source_path: "DRG_procedures.codeAndDescription_eng",
    isNested: true
  },
  specialty: {
    field: "specialty_eng",
    isNested: false
  },
  work_institution: {
    field: "presentWorkInstitutionNames_eng",
    isNested: false
  }
};

const FIELD_MAP_KEY_SEPERATOR = "-";

// Similarity threshold for filtering values (0.0 = no similarity, 1.0 = exact match)
// Set to 0.85 to allow minor variations like "Hematology" vs "Haematology" but filter out unrelated terms
const SIMILARITY_THRESHOLD = 0.85;

/**
 * Calculate similarity between two strings using normalized edit distance
 * Returns a value between 0 and 1, where 1 is exact match and 0 is completely different
 */
function calculateStringSimilarity(str1: string, str2: string): number {
  if (!str1 || !str2) return 0;

  const normalizedStr1 = str1.toLowerCase().trim();
  const normalizedStr2 = str2.toLowerCase().trim();

  if (normalizedStr1 === normalizedStr2) return 1;

  const maxLength = Math.max(normalizedStr1.length, normalizedStr2.length);
  if (maxLength === 0) return 1;

  const editMatrix = getEditDistanceMatrix(normalizedStr1, normalizedStr2);
  const editDistance = editMatrix[normalizedStr1.length][normalizedStr2.length];

  // Normalize the edit distance to get similarity score
  return 1 - (editDistance / maxLength);
}

/**
 * Filter values based on similarity to the original search query.
 * If an exact match is found, return only the exact match and ignore other results.
 * Otherwise, return values that meet the similarity threshold.
 */
function filterSimilarValues(originalQuery: string, values: string[]): string[] {
  if (!originalQuery || !values || values.length === 0) return values;

  const normalizedQuery = originalQuery.toLowerCase().trim();

  // Check for exact matches first
  const exactMatches = values.filter(value =>
    value && value.toLowerCase().trim() === normalizedQuery
  );

  // If we have exact matches, return only those and ignore everything else
  if (exactMatches.length > 0) {
    return exactMatches;
  }

  // Otherwise, filter by similarity threshold
  return values.filter(value => {
    const similarity = calculateStringSimilarity(originalQuery, value);
    return similarity >= SIMILARITY_THRESHOLD;
  });
}

@Service()
export class EntitySearchProcessorService {
  private readonly logger = createLogger(this);
  private peopleIndexName: string;

  constructor(
    config: ConfigService,
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient,
    private elasticService: ElasticSearchService
  ) {
    this.peopleIndexName = config.elasticPeopleIndex;
  }

  /**
   * Process a search query through entity detection and modify the input accordingly
   * - If searchFor is "people", execute the search
   * - Any detected indication will be added to query
   * - rankBy columns will be weighted in sortBy field
   * - Institution names will be applied as institution filters
   * - Location filters will be applied as is
   */
  async processSearchWithEntityDetection(
    input: KeywordSearchInput,
    keywordSearchFeatureFlags: KeywordSearchFeatureFlags
  ): Promise<{
    shouldExecuteSearch: boolean;
    modifiedInput: KeywordSearchInput;
    detectedEntities?: ExtractedEntitiesV2;
  }> {
    const advancedOperatorExists =
      input.query?.includes("OR") || input.query?.includes("AND");
    const bothOperatorExists = input.query?.includes("BOTH");
    const lesserThan5Words = !!input.query && input.query.split(" ").length < 5;
    if (
      !keywordSearchFeatureFlags.enableLlmQueryParserForSearch ||
      !input.query ||
      advancedOperatorExists ||
      bothOperatorExists ||
      lesserThan5Words
    ) {
      return {
        shouldExecuteSearch: true,
        modifiedInput: input,
        detectedEntities: undefined
      };
    }
    try {
      const detectedEntitiesV2 = await this.extractEntitiesFromQueryV2(
        input.query,
        input.language || "EN"
      );

      this.logger.info({ detectedEntitiesV2 }, "Entities from LLM");

      const entitiesFieldMap =
        this.createFieldMapFromEntities(detectedEntitiesV2);
      const msearchPayload = this.buildMsearchPayload(entitiesFieldMap);

      const { took, responses }: estypes.MsearchResponse =
        await this.elasticService.msearch(msearchPayload);

      this.logger.info({ took: took }, "msearch execution time");

      const newEntitiesV2 = this.applyMSearchResults(
        detectedEntitiesV2,
        entitiesFieldMap,
        responses
      );

      this.logger.info({ newEntitiesV2 }, "Entities after msearch");

      const modifiedInput: KeywordSearchInput = {
        ...input,
        suppliedFilters: { ...input.suppliedFilters },
        sortBy: { ...input.sortBy }
      };

      // Apply detected entities v2 to the search input
      this.applyDetectedEntitiesV2(modifiedInput, newEntitiesV2);

      // Only execute search if searchFor has value 'people'
      const shouldExecuteSearch =
        newEntitiesV2.search_for.toLowerCase() === "people";
      if (shouldExecuteSearch && !newEntitiesV2.indication.length) {
        modifiedInput.query = "";
      }
      return {
        shouldExecuteSearch,
        modifiedInput,
        detectedEntities: newEntitiesV2
      };
    } catch (error) {
      this.logger.error(`Error in processSearchWithEntityDetection: ${error}`);

      // If there's an error, return the original input but still execute the search
      return {
        shouldExecuteSearch: true,
        modifiedInput: input,
        detectedEntities: this.buildDefaultEntitiesV2()
      };
    }
  }

  private buildDefaultEntitiesV2(): ExtractedEntitiesV2 {
    const defaultResult: ExtractedEntitiesV2 = {
      search_for: "",
      indication: [],
      locations: {
        country: [],
        state: [],
        city: []
      },
      claims: {
        diagnoses: [],
        min_diagnoses_claim_count: null,
        max_diagnoses_claim_count: null,
        procedures: [],
        min_procedures_claim_count: null,
        max_procedures_claim_count: null,
        timeframe: null
      },
      specialty: [],
      trial: {
        min_count: null,
        max_count: null,
        status: null,
        phase: null
      },
      work_institution: []
    };

    return defaultResult;
  }

  private async extractEntitiesFromQueryV2(
    query: string,
    language: string
  ): Promise<ExtractedEntitiesV2> {
    // Default fallback
    const defaultResult: ExtractedEntitiesV2 = this.buildDefaultEntitiesV2();
    if (!query) {
      return defaultResult;
    }

    const languageDetector = getLanguageDetector(language);
    const languageCode = languageDetector(query);
    const queryUnderstandingResponse =
      await this.queryUnderstandingServiceClient.getEntitiesForQuery(
        query,
        languageCode
      );

    const entities = queryUnderstandingResponse.getEntitiesV2();
    if (!entities) return defaultResult;

    const result: ExtractedEntitiesV2 = {
      search_for: (entities.getSearchFor() ??
        "") as ExtractedEntitiesV2["search_for"],
      indication: entities.getIndicationList() ?? [],
      locations: {
        country: entities.getLocations()?.getCountryList() ?? [],
        state: entities.getLocations()?.getStateList() ?? [],
        city: entities.getLocations()?.getCityList() ?? []
      },
      claims: {
        diagnoses: entities.getClaims()?.getDiagnosesList() ?? [],
        min_diagnoses_claim_count:
          entities.getClaims()?.getMinDiagnosesClaimCount() ?? null,
        max_diagnoses_claim_count:
          entities.getClaims()?.getMaxDiagnosesClaimCount() ?? null,

        procedures: entities.getClaims()?.getProceduresList() ?? [],
        min_procedures_claim_count:
          entities.getClaims()?.getMinProceduresClaimCount() ?? null,
        max_procedures_claim_count:
          entities.getClaims()?.getMaxProceduresClaimCount() ?? null,

        timeframe: (entities.getClaims()?.getTimeframe() ??
          null) as ExtractedEntitiesV2["claims"]["timeframe"]
      },
      specialty: entities.getSpecialtyList() ?? [],
      trial: {
        min_count: entities.getTrial()?.getMinCount() ?? null,
        max_count: entities.getTrial()?.getMaxCount() ?? null,
        status: (entities.getTrial()?.getStatus() ??
          null) as ExtractedEntitiesV2["trial"]["status"],
        phase: (entities.getTrial()?.getPhase() ??
          null) as ExtractedEntitiesV2["trial"]["phase"]
      },
      work_institution: entities.getWorkInstitutionList() ?? []
    };

    return result;
  }

  private buildMsearchPayload(
    fieldMap: Record<
      string,
      { field: string; path?: string; nested_source_path?: string; isNested: boolean; value: string }
    >
  ): estypes.MsearchRequest {
    const queries: MSearchRequest[] = [];

    for (const { field, path, nested_source_path, isNested, value } of Object.values(fieldMap)) {
      if (isNested) {
        queries.push(this.buildNestedQuery(path!, field, nested_source_path || field, value));
      } else {
        queries.push(this.buildNonNestedQuery(field, value));
      }
    }
    const multiSearchRequest: estypes.MsearchRequest = {
      index: this.peopleIndexName,
      searches: queries.flat()
    };

    return multiSearchRequest;
  }

  private createFieldMapFromEntities(
    entities: ExtractedEntitiesV2
  ): Record<
    string,
    { field: string; path?: string; nested_source_path?: string; isNested: boolean; value: string }
  > {
    const fieldsToSearch: Record<
      string,
      { field: string; path?: string; nested_source_path?: string; isNested: boolean; value: string }
    > = {};

    for (const [key, { field, path, nested_source_path, isNested }] of Object.entries(
      ELASTICSEARCH_FIELD_MAPPING
    )) {
      const oldValues = (_.get(entities, key) as string[]) ?? [];
      if (_.isEmpty(oldValues)) continue;

      for (const [idx, value] of oldValues.entries()) {
        fieldsToSearch[`${key}${FIELD_MAP_KEY_SEPERATOR}${idx}`] = {
          field,
          path,
          nested_source_path,
          isNested,
          value
        };
      }
    }

    return fieldsToSearch;
  }

  private buildNestedQuery(
    path: string,
    field: string,
    nested_source_path: string,
    valueToSearch: string
  ): MSearchRequest {
    return [
      HEADER,
      {
        size: 1,
        _source: false,
        query: {
          nested: {
            path: path,
            query: {
              match: {
                [field]: {
                  query: valueToSearch,
                  fuzziness: "AUTO"
                }
              }
            },
            inner_hits: {
              _source: nested_source_path
            }
          }
        }
      }
    ];
  }

  private buildNonNestedQuery(
    field: string,
    valueToSearch: string
  ): MSearchRequest {
    return [
      HEADER,
      {
        _source: field,
        size: 1,
        query: {
          match: {
            [field]: {
              query: valueToSearch,
              fuzziness: "AUTO"
            }
          }
        }
      }
    ];
  }

  private applyMSearchResults(
    originalEntities: ExtractedEntitiesV2,
    fieldMap: Record<
      string,
      { field: string; path?: string; nested_source_path?: string; isNested: boolean; value: string }
    >,
    msearchResponses: estypes.MsearchResponseItem[]
  ): ExtractedEntitiesV2 {
    const newEntities = _.cloneDeep(originalEntities);
    newEntities.locations = {
      country: [],
      state: [],
      city: []
    };
    newEntities.claims.diagnoses = [];
    newEntities.claims.procedures = [];
    newEntities.specialty = [];
    newEntities.work_institution = [];

    let msearchResponsesIdx = 0;
    for (const [key, { field, isNested, path, nested_source_path, value }] of Object.entries(fieldMap)) {
      const keyParts: string[] = key.split(FIELD_MAP_KEY_SEPERATOR);
      const objPath = keyParts[0];
      const msearchResponse = msearchResponses[msearchResponsesIdx++];
      if (this.isErrorResponse(msearchResponse)) {
        this.logger.error(
          `Error in msearch response for ${key}`,
          msearchResponse
        );
        continue;
      }
      if (isNested) {
        const hits = msearchResponse.hits.hits;
        // Use nested_source_path if available, otherwise fall back to extracting from field
        const sourcePath = nested_source_path ? nested_source_path.split('.').slice(1).join('.') : field.split(".").pop();
        if (_.isEmpty(hits)) continue;
        if (!hits[0]?.inner_hits) continue;
        const innerHits = hits[0]?.inner_hits[path!]?.hits.hits;
        if (_.isEmpty(innerHits)) continue;
        const values = innerHits.map((hit) => _.get(hit._source, sourcePath!) as string);

        const existing = _.get(newEntities, objPath) as string[] | undefined;
        const updated = [...(existing ?? []), ...values];
        _.set(newEntities, objPath, _.uniq(updated));
      } else {
        const hits = msearchResponse.hits.hits;
        if (_.isEmpty(hits)) continue;
        const values = hits.map((hit: any) => hit._source[field] as string[]);
        const flattenedValues = values.flat();

        // Filter values based on similarity to the original search query
        const filteredValues = filterSimilarValues(value, flattenedValues);

        const existing = _.get(newEntities, objPath) as string[] | undefined;
        const updated = [...(existing ?? []), ...filteredValues];
        _.set(newEntities, objPath, _.uniq(updated));
      }
    }

    return newEntities;
  }

  private isErrorResponse(
    response: Readonly<estypes.MsearchResponseItem>
  ): response is ErrorResponseBase {
    return !!(response as ErrorResponseBase).error;
  }

  private applyDetectedEntitiesV2(
    input: KeywordSearchInput,
    entities: ExtractedEntitiesV2
  ): void {
    // Apply indication as a filter
    if (!_.isEmpty(entities.indication)) {
      input.query = entities.indication[0];
    }

    // Apply location filters
    if (!_.isEmpty(entities.locations.country)) {
      input.suppliedFilters.country = { values: entities.locations.country };
    }
    if (!_.isEmpty(entities.locations.state)) {
      input.suppliedFilters.state = { values: entities.locations.state };
    }
    if (!_.isEmpty(entities.locations.city)) {
      input.suppliedFilters.city = { values: entities.locations.city };
    }

    //apply claim filters
    if (!_.isEmpty(entities.claims.diagnoses)) {
      input.suppliedFilters.claims.diagnosesICD = {
        values: entities.claims.diagnoses
      };
    }
    if (!_.isEmpty(entities.claims.procedures)) {
      input.suppliedFilters.claims.proceduresCPT = {
        values: entities.claims.procedures
      };
    }
    if (entities.claims.min_diagnoses_claim_count) {
      input.suppliedFilters.claims.diagnosesICDMinCount = {
        value: entities.claims.min_diagnoses_claim_count
      };
    }
    if (entities.claims.min_procedures_claim_count) {
      input.suppliedFilters.claims.proceduresCPTMinCount = {
        value: entities.claims.min_procedures_claim_count
      };
    }
    if (entities.claims.max_diagnoses_claim_count) {
      input.suppliedFilters.claims.diagnosesICDMaxCount = {
        value: entities.claims.max_diagnoses_claim_count
      };
    }
    if (entities.claims.max_procedures_claim_count) {
      input.suppliedFilters.claims.proceduresCPTMaxCount = {
        value: entities.claims.max_procedures_claim_count
      };
    }
    if (entities.claims.timeframe) {
      input.suppliedFilters.claims.timeFrame = {
        value: Number(entities.claims.timeframe)
      };
    }

    //turn off showunique patients as it does not work with entity search
    input.suppliedFilters.claims.showUniquePatients = { value: false };

    //apply specialty filters
    if (!_.isEmpty(entities.specialty)) {
      input.suppliedFilters.specialty = { values: entities.specialty };
    }

    //apply trial filters
    if (entities.trial.min_count) {
      input.suppliedFilters.trials.minCount = {
        value: entities.trial.min_count
      };
    }
    if (entities.trial.max_count) {
      input.suppliedFilters.trials.maxCount = {
        value: entities.trial.max_count
      };
    }
    if (entities.trial.status) {
      input.suppliedFilters.trials.status = { values: [entities.trial.status] };
    }
    if (entities.trial.phase) {
      input.suppliedFilters.trials.phase = { values: [entities.trial.phase] };
    }

    //apply work institution filters
    if (!_.isEmpty(entities.work_institution)) {
      input.suppliedFilters.institution = { values: entities.work_institution };
    }
  }
}
