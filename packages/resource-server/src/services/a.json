{
  "bool": {
    "must": [
      {
        "function_score": {
          "query": {
            "function_score": {
              "query": {
                "bool": {
                  "should": [
                    {
                      "function_score": {
                        "query": {
                          "nested": {
                            "path": "trials",
                            "score_mode": "sum",
                            "query": {
                              "bool": {
                                "must": {
                                  "match_all": {}
                                },
                                "filter": [
                                  {
                                    "simple_query_string": {
                                      "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                                      "default_operator": "AND",
                                      "fields": [
                                        "trials.officialTitle_eng",
                                        "trials.briefTitle_eng",
                                        "trials.conditions_eng",
                                        "trials.interventions_eng",
                                        "trials.keywords_eng",
                                        "trials.summary_eng",
                                        "trials.indication"
                                      ],
                                      "flags": "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                    }
                                  }
                                ],
                                "should": []
                              }
                            },
                            "inner_hits": {
                              "sort": [
                                {
                                  "_score": "desc"
                                },
                                {
                                  "trials.startDate": {
                                    "order": "desc"
                                  }
                                }
                              ],
                              "highlight": {
                                "order": "score",
                                "fields": {
                                  "trials.officialTitle_eng": {},
                                  "trials.briefTitle_eng": {},
                                  "trials.conditions_eng": {},
                                  "trials.interventions_eng": {},
                                  "trials.keywords_eng": {},
                                  "trials.summary_eng": {}
                                }
                              },
                              "size": 0,
                              "_source": false,
                              "docvalue_fields": ["trials.id"]
                            }
                          }
                        },
                        "boost_mode": "replace",
                        "score_mode": undefined,
                        "functions": [
                          {
                            "script_score": {
                              "script": {
                                "source": "Math.log((_score/params.mean) + 1)*params.weight",
                                "params": {
                                  "mean": 0.17,
                                  "weight": 1
                                }
                              }
                            },
                            "weight": undefined
                          }
                        ]
                      }
                    },
                    {
                      "function_score": {
                        "query": {
                          "nested": {
                            "path": "publications",
                            "score_mode": "sum",
                            "query": {
                              "bool": {
                                "should": [],
                                "must": {
                                  "match_all": {}
                                },
                                "filter": [
                                  {
                                    "simple_query_string": {
                                      "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                                      "default_operator": "AND",
                                      "fields": [
                                        "publications.keywords_eng",
                                        "publications.publicationAbstract_eng",
                                        "publications.title_eng",
                                        "publications.keywords_cmn",
                                        "publications.publicationAbstract_cmn",
                                        "publications.title_cmn",
                                        "publications.keywords_jpn",
                                        "publications.publicationAbstract_jpn",
                                        "publications.title_jpn"
                                      ],
                                      "flags": "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                    }
                                  }
                                ]
                              }
                            },
                            "inner_hits": {
                              "sort": [
                                {
                                  "publications.datePublished": {
                                    "order": "desc"
                                  }
                                },
                                {
                                  "publications.citationCount": {
                                    "order": "desc"
                                  }
                                }
                              ],
                              "highlight": {
                                "order": "score",
                                "fields": {
                                  "publications.publicationAbstract_eng": {},
                                  "publications.keywords_eng": {},
                                  "publications.title_eng": {}
                                }
                              },
                              "size": 0,
                              "_source": false,
                              "docvalue_fields": [
                                "publications.id",
                                "publications.citationCount"
                              ],
                              "name": "publications"
                            }
                          }
                        },
                        "boost_mode": "replace",
                        "functions": [
                          {
                            "script_score": {
                              "script": {
                                "source": "Math.log((_score/params.mean) + 1)*params.weight",
                                "params": {
                                  "mean": 6.4,
                                  "weight": 1
                                }
                              }
                            },
                            "weight": undefined
                          }
                        ]
                      }
                    },
                    {
                      "function_score": {
                        "query": {
                          "nested": {
                            "path": "publications",
                            "score_mode": "sum",
                            "query": {
                              "bool": {
                                "should": [],
                                "must": {
                                  "function_score": {
                                    "functions": [
                                      {
                                        "field_value_factor": {
                                          "field": "publications.microBloggingCount",
                                          "missing": 0
                                        }
                                      }
                                    ]
                                  }
                                },
                                "filter": [
                                  {
                                    "simple_query_string": {
                                      "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                                      "default_operator": "AND",
                                      "fields": [
                                        "publications.keywords_eng",
                                        "publications.publicationAbstract_eng",
                                        "publications.title_eng",
                                        "publications.keywords_cmn",
                                        "publications.publicationAbstract_cmn",
                                        "publications.title_cmn",
                                        "publications.keywords_jpn",
                                        "publications.publicationAbstract_jpn",
                                        "publications.title_jpn"
                                      ],
                                      "flags": "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                    }
                                  }
                                ]
                              }
                            },
                            "inner_hits": {
                              "name": "microBlogging",
                              "size": 0,
                              "_source": false,
                              "docvalue_fields": [
                                "publications.microBloggingCount"
                              ]
                            }
                          }
                        },
                        "boost_mode": "replace",
                        "functions": [
                          {
                            "script_score": {
                              "script": {
                                "source": "Math.log((_score/params.mean) + 1)*params.weight",
                                "params": {
                                  "mean": 19.63,
                                  "weight": 0.33
                                }
                              }
                            },
                            "weight": undefined
                          }
                        ]
                      }
                    },
                    {
                      "function_score": {
                        "query": {
                          "nested": {
                            "path": "publications",
                            "score_mode": "sum",
                            "query": {
                              "bool": {
                                "should": [],
                                "must": {
                                  "function_score": {
                                    "functions": [
                                      {
                                        "field_value_factor": {
                                          "field": "publications.citationCount",
                                          "missing": 0
                                        }
                                      }
                                    ]
                                  }
                                },
                                "filter": [
                                  {
                                    "simple_query_string": {
                                      "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                                      "default_operator": "AND",
                                      "fields": [
                                        "publications.keywords_eng",
                                        "publications.publicationAbstract_eng",
                                        "publications.title_eng",
                                        "publications.keywords_cmn",
                                        "publications.publicationAbstract_cmn",
                                        "publications.title_cmn",
                                        "publications.keywords_jpn",
                                        "publications.publicationAbstract_jpn",
                                        "publications.title_jpn"
                                      ],
                                      "flags": "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                    }
                                  }
                                ]
                              }
                            },
                            "inner_hits": {
                              "name": "citations",
                              "size": 0,
                              "_source": false,
                              "docvalue_fields": ["publications.citationCount"]
                            }
                          }
                        },
                        "boost_mode": "replace",
                        "functions": [
                          {
                            "script_score": {
                              "script": {
                                "source": "Math.log((_score/params.mean) + 1)*params.weight",
                                "params": {
                                  "mean": 275.77,
                                  "weight": 0.33
                                }
                              }
                            },
                            "weight": undefined
                          }
                        ]
                      }
                    },
                    {
                      "function_score": {
                        "query": {
                          "nested": {
                            "path": "congress",
                            "score_mode": "sum",
                            "query": {
                              "bool": {
                                "must": {
                                  "match_all": {}
                                },
                                "filter": [
                                  {
                                    "simple_query_string": {
                                      "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                                      "default_operator": "AND",
                                      "fields": [
                                        "congress.keywords_eng",
                                        "congress.title_eng",
                                        "congress.organizer_eng.search",
                                        "congress.name_eng.search",
                                        "congress.role.search"
                                      ],
                                      "flags": "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                    }
                                  }
                                ]
                              }
                            },
                            "inner_hits": {
                              "sort": [
                                {
                                  "_score": "desc"
                                },
                                {
                                  "congress.masterInitialDate": {
                                    "order": "desc"
                                  }
                                }
                              ],
                              "highlight": {
                                "order": "score",
                                "fields": {
                                  "congress.keywords_eng": {},
                                  "congress.title_eng": {}
                                }
                              },
                              "size": 0,
                              "_source": false,
                              "docvalue_fields": ["congress.id"]
                            }
                          }
                        },
                        "boost_mode": "replace",
                        "score_mode": undefined,
                        "functions": [
                          {
                            "script_score": {
                              "script": {
                                "source": "Math.log((_score/params.mean) + 1)*params.weight",
                                "params": {
                                  "mean": 0.2,
                                  "weight": 0.66
                                }
                              }
                            },
                            "weight": 1
                          }
                        ]
                      }
                    },
                    {
                      "function_score": {
                        "query": {
                          "nested": {
                            "path": "payments",
                            "score_mode": "sum",
                            "query": {
                              "bool": {
                                "must": [
                                  {
                                    "function_score": {
                                      "functions": [
                                        {
                                          "field_value_factor": {
                                            "field": "payments.amount",
                                            "missing": 0
                                          }
                                        }
                                      ]
                                    }
                                  },
                                  {
                                    "term": {
                                      "payments.category": "Research"
                                    }
                                  }
                                ],
                                "filter": [
                                  {
                                    "simple_query_string": {
                                      "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                                      "default_operator": "AND",
                                      "fields": [
                                        "payments.associatedDrugOrDevice.autocomplete_search",
                                        "payments.payerCompany.autocomplete_search"
                                      ],
                                      "flags": "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                    }
                                  }
                                ]
                              }
                            },
                            "inner_hits": {
                              "name": "payments",
                              "size": 0,
                              "_source": false,
                              "docvalue_fields": ["payments.amount"]
                            }
                          }
                        },
                        "boost_mode": "replace",
                        "functions": [
                          {
                            "script_score": {
                              "script": {
                                "source": "Math.log((_score/params.mean) + 1)*params.weight",
                                "params": {
                                  "mean": 9920.1,
                                  "weight": 0.33
                                }
                              }
                            },
                            "weight": undefined
                          }
                        ]
                      }
                    },
                    {
                      "bool": {
                        "must_not": [
                          {
                            "terms": {
                              "country_multi": ["Brazil"]
                            }
                          }
                        ],
                        "must": [
                          {
                            "function_score": {
                              "query": {
                                "nested": {
                                  "path": "DRG_diagnoses",
                                  "score_mode": "sum",
                                  "query": {
                                    "bool": {
                                      "must": {
                                        "function_score": {
                                          "functions": [
                                            {
                                              "field_value_factor": {
                                                "field": "DRG_diagnoses.internalCount",
                                                "missing": 0
                                              }
                                            }
                                          ]
                                        }
                                      },
                                      "filter": [
                                        {
                                          "bool": {
                                            "should": [
                                              {
                                                "simple_query_string": {
                                                  "query": "(c44501*)|(c44511*)|(c44521*)|(c44591*)|(c4a52*)|(c50*)|(c7981*)|(d05*)|(d24*)|(d486*)|(d493*)|(z123*)|(z1501*)|(z803*)|(z853*)|(z86000*)",
                                                  "default_operator": "AND",
                                                  "fields": [
                                                    "DRG_diagnoses.codeAndDescription_eng"
                                                  ],
                                                  "flags": "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                                }
                                              }
                                            ],
                                            "minimum_should_match": 1
                                          }
                                        }
                                      ]
                                    }
                                  },
                                  "inner_hits": {
                                    "name": "diagnoses_amount",
                                    "size": 0,
                                    "_source": false,
                                    "docvalue_fields": [
                                      "DRG_diagnoses.internalCount"
                                    ]
                                  }
                                }
                              },
                              "boost_mode": "replace",
                              "score_mode": undefined,
                              "functions": [
                                {
                                  "script_score": {
                                    "script": {
                                      "source": "Math.log((_score/params.mean) + 1)*params.weight",
                                      "params": {
                                        "mean": 3059.54,
                                        "weight": 0.33
                                      }
                                    }
                                  },
                                  "weight": undefined
                                }
                              ]
                            }
                          }
                        ]
                      }
                    },
                    {
                      "bool": {
                        "must_not": [
                          {
                            "terms": {
                              "country_multi": ["Brazil"]
                            }
                          }
                        ],
                        "must": [
                          {
                            "function_score": {
                              "query": {
                                "nested": {
                                  "path": "DRG_procedures",
                                  "score_mode": "sum",
                                  "query": {
                                    "bool": {
                                      "must": {
                                        "function_score": {
                                          "functions": [
                                            {
                                              "field_value_factor": {
                                                "field": "DRG_procedures.internalCount",
                                                "missing": 0
                                              }
                                            }
                                          ]
                                        }
                                      },
                                      "filter": [
                                        {
                                          "bool": {
                                            "should": [
                                              {
                                                "simple_query_string": {
                                                  "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                                                  "default_operator": "AND",
                                                  "fields": [
                                                    "DRG_procedures.codeAndDescription_eng"
                                                  ],
                                                  "flags": "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                                }
                                              }
                                            ],
                                            "minimum_should_match": 1
                                          }
                                        }
                                      ]
                                    }
                                  },
                                  "inner_hits": {
                                    "name": "procedures_amount",
                                    "size": 0,
                                    "_source": false,
                                    "docvalue_fields": [
                                      "DRG_procedures.internalCount"
                                    ]
                                  }
                                }
                              },
                              "boost_mode": "replace",
                              "score_mode": undefined,
                              "functions": [
                                {
                                  "script_score": {
                                    "script": {
                                      "source": "Math.log((_score/params.mean) + 1)*params.weight",
                                      "params": {
                                        "mean": 2718.33,
                                        "weight": 0
                                      }
                                    }
                                  },
                                  "weight": undefined
                                }
                              ]
                            }
                          }
                        ]
                      }
                    },
                    {
                      "bool": {
                        "must_not": [
                          {
                            "terms": {
                              "country_multi": ["Brazil"]
                            }
                          }
                        ],
                        "must": [
                          {
                            "function_score": {
                              "query": {
                                "nested": {
                                  "path": "prescriptions",
                                  "score_mode": "sum",
                                  "query": {
                                    "bool": {
                                      "must": {
                                        "function_score": {
                                          "functions": [
                                            {
                                              "field_value_factor": {
                                                "field": "prescriptions.num_prescriptions",
                                                "missing": 0
                                              }
                                            }
                                          ]
                                        }
                                      },
                                      "filter": [
                                        {
                                          "bool": {
                                            "should": [
                                              {
                                                "simple_query_string": {
                                                  "query": "( ( breast cancer )  | ( breast carcinoma )  | ( breast malign tumor )  | ( breast neopl )  | ( breast neoplasm )  | ( breast tumor )  | ( breast tumour )  | ( carcinoma breast )  | ( human mammary carcinoma )  | ( human mammary neoplasm )  | ( malign tumor breast )  | ( mammary cancer )  | ( neopl breast )  | ( tumor breast )  | ( 乳かん )  | ( 乳カン )  | ( 乳房かん )  | ( 乳房新生物 )  | ( 乳房腫瘍 )  | ( 乳癌 )  | ( 乳腺mucocele like tumor )  | ( 乳腺悪性腫瘍 )  | ( 乳腺癌 )  | ( 乳腺腫瘍 ヒト )  | ( 乳腺腫瘍 人間 )  )",
                                                  "default_operator": "AND",
                                                  "fields": [
                                                    "prescriptions.generic_name.text"
                                                  ],
                                                  "flags": "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                                }
                                              }
                                            ],
                                            "minimum_should_match": 1
                                          }
                                        }
                                      ]
                                    }
                                  },
                                  "inner_hits": {
                                    "name": "prescriptions_amount",
                                    "size": 0,
                                    "_source": false,
                                    "docvalue_fields": [
                                      "prescriptions.num_prescriptions"
                                    ]
                                  }
                                }
                              },
                              "boost_mode": "replace",
                              "score_mode": undefined,
                              "functions": [
                                {
                                  "script_score": {
                                    "script": {
                                      "source": "Math.log((_score/params.mean) + 1)*params.weight",
                                      "params": {
                                        "mean": 4294.32,
                                        "weight": 0
                                      }
                                    }
                                  },
                                  "weight": undefined
                                }
                              ]
                            }
                          }
                        ]
                      }
                    }
                  ],
                  "minimum_should_match": 1,
                  "must": [],
                  "filter": [
                    {
                      "term": {
                        "projectIds": "1"
                      }
                    },
                    {
                      "nested": {
                        "path": "addressesForHCPU",
                        "query": {
                          "bool": {
                            "filter": [
                              {
                                "bool": {
                                  "should": [
                                    {
                                      "terms": {
                                        "addressesForHCPU.filters.country": [
                                          "us"
                                        ]
                                      }
                                    },
                                    {
                                      "terms": {
                                        "addressesForHCPU.filters.country_level_regions": [
                                          "United States"
                                        ]
                                      }
                                    }
                                  ]
                                }
                              }
                            ]
                          }
                        }
                      }
                    },
                    {
                      "bool": {
                        "should": [
                          {
                            "term": {
                              "isInactive": false
                            }
                          },
                          {
                            "bool": {
                              "must_not": {
                                "exists": {
                                  "field": "isInactive"
                                }
                              }
                            }
                          }
                        ],
                        "minimum_should_match": 1
                      }
                    },
                    {
                      "bool": {
                        "should": [
                          {
                            "term": {
                              "isIndustry": false
                            }
                          },
                          {
                            "bool": {
                              "must_not": {
                                "exists": {
                                  "field": "isIndustry"
                                }
                              }
                            }
                          }
                        ],
                        "minimum_should_match": 1
                      }
                    }
                  ]
                }
              },
              "functions": [
                {
                  "filter": {
                    "term": {
                      "isAutoCreated": true
                    }
                  },
                  "weight": 0
                }
              ]
            }
          },
          "functions": [
            {
              "script_score": {
                "script": {
                  "source": "saturation(_score, params['saturation_param'])",
                  "params": {
                    "saturation_param": 1
                  }
                }
              }
            }
          ],
          "boost_mode": "replace"
        }
      }
    ],
    "should": [
      {
        "dis_max": {
          "queries": [
            {
              "rank_feature": {
                "field": "location_country_confidence_mapping.United States",
                "linear": {}
              }
            }
          ]
        }
      }
    ]
  }
}
