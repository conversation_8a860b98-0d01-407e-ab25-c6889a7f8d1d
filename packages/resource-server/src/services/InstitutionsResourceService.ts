/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import _, { Dictionary, isArray, toLower, trim, zipObject } from "lodash";
import { estypes } from "@elastic/elasticsearch";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import {
  Entity,
  INSTITUTION_ACCESS_LEVEL,
  InstitutionTagResourceClient,
  UserEntities
} from "@h1nyc/account-sdk";

import {
  InstitutionsResponse,
  InstitutionsSearchInput,
  RPC_NAMESPACE_INSTITUTIONS,
  InstitutionsResource,
  InstitutionFilterAggregation,
  InstitutionsAutocompleteInput,
  TrialsSearchByInstitutionInput,
  TrialsSearchByInstitutionResponse,
  InstitutionTrialsFiltersInput,
  GeoChoroplethLevelTypes,
  IolClaimsInput,
  IolClaimsResponse,
  ClaimFiltersInput,
  ExclusionClaimFiltersInput,
  DiversityFiltersInput,
  MinMaxFilter,
  SearchTypes,
  InstitutionSortOptions,
  PatientsDiversityRaceInfo,
  PatientsDiversityAgeInfo,
  PatientsDiversitySexInfo,
  Apps,
  PeopleSearchInclusionExclusionAggregationResponse,
  TaggedInstitutionsSearchInput,
  TaggedInstitutionsSearchResponse,
  AcademicTypesEnum,
  PatientDiversityEnum,
  InstitutionDiversityStatsInput,
  PatientDiversityStatsResponse,
  MatchedInstitutionTrials,
  InstitutionSearchFilter,
  IndicationType,
  IndicationSource,
  IndicationSortBy,
  SearchIndicationsByQueryInput,
  PatientsUkDiversityRaceInfo,
  PatientRaceUk,
  PatientNationalityFrance,
  InstitutionLocationFilterAggregation,
  ESEnrollmentRatesByIndication,
  FranceGeoChoroplethLevelTypes,
  PatientsFranceDiversityRaceInfo,
  PatientsSpainDiversityRaceInfo,
  HeatMapZoomLevel,
  PatientNationalitySpain
} from "@h1nyc/search-sdk";
import { ElasticSearchInstitutionsService } from "./ElasticSearchInstitutionsService";
import {
  QueryDslQueryContainer,
  AggregationsTermsAggregateBase,
  AggregationsSimpleValueAggregate,
  QueryDslNestedQuery,
  AggregationsAggregationContainer,
  QueryDslFunctionScoreContainer,
  SearchRequest,
  ErrorResponseBase,
  SearchResponse,
  SearchHit,
  CountRequest,
  SearchTotalHits,
  LatLonGeoLocation,
  QueryDslBoolQuery,
  QueryDslScriptScoreFunction
} from "@elastic/elasticsearch/lib/api/types";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { Service } from "typedi";
import {
  InstitutionClaimsQueryBuilder,
  InstitutionClaimsQueryBuilderOptions
} from "./queryBuilders/InstitutionClaimsQueryBuilder";
import { QueryParserService } from "./QueryParserService";
import { InstitutionsClaimsResponseAdapterService } from "./InstitutionsClaimsResponseAdapterService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import {
  extractClaimsCode,
  MINIMUM_RACE_THRESHOLD,
  MAXIMUM_RACE_THRESHOLD,
  DEFAULT_GEO_BOUNDING_BOX,
  AT_LEAST_ONE,
  raceFilterValueToPatientsDiversityRatioFieldMap,
  buildMinMaxFilterScriptForAsset
} from "./KeywordFilterClauseBuilderService";
import {
  ALL_ORs,
  ALL_ANDs,
  ALL_UNICODE_DOUBLE_QUOTES,
  ASCII_DOUBLE_QUOTES,
  SearchContext,
  PATIENT_DIVERSITY_RATIO
} from "./KeywordSearchResourceServiceRewrite";
import {
  buildClaimsRegionFilterForInstitutionsSearch,
  getCountriesToFilterOutForInstitutionsSearch
} from "../util/ClaimsRegionFilterUtils";
import {
  buildFieldExistsQuery,
  buildTermQuery,
  buildTermsQuery,
  sortIcdCodesBySchemeAndAlphabet,
  sortProcedureCodesByAlphabet
} from "../util/QueryBuildingUtils";
import { RulesParserService } from "./RulesParserService";
import { PatientDiversityStatsService } from "./PatientDiversityStatsService";
import {
  ASIAN_RACE_CATEGORY,
  PACIFIC_ISLANDER_RACE_CATEGORY
} from "./KeywordSearchResponseAdapterService";
import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";
import { InstitutionsSearchResponseAdapterService } from "./InstitutionsSearchResponseAdapterService";
import { InstitutionNameSearchResourceService } from "./InstitutionNameSearchResourceService";
import {
  CcsrIcdMappingRepository,
  CcsrPxMappingRepository
} from "@h1nyc/pipeline-repositories";
import {
  REGION_TO_STATES,
  VALID_STATE_CODES
} from "./KeywordAutocompleteResponseAdapterService";
import { sha1 } from "object-hash";
import Redis from "ioredis";
import { BothQueryParseResult } from "../lib/ParserTypes/types";
import { ClaimCode } from "@h1nyc/pipeline-entities";
import { ClaimCodeService } from "./ClaimCodeService";

export type InstitutionSearchFeatureFlags = {
  enableCTMSV2: boolean;
  enableClaimsFilterFunctionScore: boolean;
  enableClaimsFilteringMatchedCountsUpdate: boolean;
  enableTagsInElasticsearch: boolean;
  enableNonIOLsInTrialLandscapeInstitutionSearch: boolean;
  enableUniquePatientCountForClaims: boolean;
  enableBrazilianClaims: boolean;
  enablePatientMatchesForInstitutionProfilePage: boolean;
  disableUniquePatientCountForOnlyProcedures: boolean;
  enableEnhancedHasCTMSDataFilterBehavior: boolean;
  useDocIdForParentPatientClaims: boolean;
  enableInstitutionNameSuggest: boolean;
  enableLocationFilterRegionRollup: boolean;
  enableCcsrExclusionForMatchedCounts: boolean;
  enableNewIolH1DefaultRanking: boolean;
  enablePenalizeAutoCreatedIOLs: boolean;
  enableBothOperatorForSearch: boolean;
  enableInstitutionOrgHierarchy: boolean;
  usePatientIndexForCount: boolean;
};

export const institutionSearchFeatureFlagTypes = [
  "enableCTMSV2",
  "enableClaimsFilterFunctionScore",
  "enableClaimsFilteringMatchedCountsUpdate",
  "enableTagsInElasticsearch",
  "enableNonIOLsInTrialLandscapeInstitutionSearch",
  "enableUniquePatientCountForClaims",
  "enableBrazilianClaims",
  "enablePatientMatchesForInstitutionProfilePage",
  "disableUniquePatientCountForOnlyProcedures",
  "enableEnhancedHasCTMSDataFilterBehavior",
  "useDocIdForParentPatientClaims",
  "enableInstitutionNameSuggest",
  "enableLocationFilterRegionRollup",
  "enableCcsrExclusionForMatchedCounts",
  "enableNewIolH1DefaultRanking",
  "enablePenalizeAutoCreatedIOLs",
  "enableBothOperatorForSearch",
  "enableInstitutionOrgHierarchy",
  "usePatientIndexForCount"
] as const;

enum PaymentType {
  research = "research",
  general = "general"
}
enum UkPatientCountField {
  patientCountAsian = "patientCountAsian",
  patientCountBlack = "patientCountBlack",
  patientCountWhite = "patientCountWhite",
  patientCountMixed = "patientCountMixed",
  patientCountOther = "patientCountOther",
  patientCountUnknown = "patientCountUnknown"
}

enum FrancePatientCountField {
  patientCountFranceNative = "patientCountFranceNative",
  patientCountEuropeanUnion = "patientCountEuropeanUnion",
  patientCountOtherEuropean = "patientCountOtherEuropean",
  patientCountMiddleEastAndNorthAfrica = "patientCountMiddleEastAndNorthAfrica",
  patientCountCentralAndSouthAfrica = "patientCountCentralAndSouthAfrica",
  patientCountOceania = "patientCountOceania",
  patientCountSouthAmerica = "patientCountSouthAmerica",
  patientCountNorthAmerica = "patientCountNorthAmerica",
  patientCountSoutheastAsia = "patientCountSoutheastAsia",
  patientCountEastAsia = "patientCountEastAsia",
  patientCountSouthAsia = "patientCountSouthAsia",
  patientCountCentralAmerica = "patientCountCentralAmerica"
}

enum SpainPatientCountField {
  patientCountSpain = "patientCountSpain",
  patientCountEuropeanUnion = "patientCountEuropeanUnion",
  patientCountOtherEuropean = "patientCountOtherEuropean",
  patientCountMiddleEastAndNorthAfrica = "patientCountMiddleEastAndNorthAfrica",
  patientCountCentralAndSouthAfrica = "patientCountCentralAndSouthAfrica",
  patientCountOceania = "patientCountOceania",
  patientCountSouthAmerica = "patientCountSouthAmerica",
  patientCountNorthAmerica = "patientCountNorthAmerica",
  patientCountSoutheastAsia = "patientCountSoutheastAsia",
  patientCountEastAsia = "patientCountEastAsia",
  patientCountSouthAsia = "patientCountSouthAsia",
  patientCountCentralAsia = "patientCountCentralAsia",
  patientCountCentralAmerica = "patientCountCentralAmerica",
  patientCountOther = "patientCountOther",
  patientCountUnknown = "patientCountUnknown"
}

const raceFilterValueToUkPatientsDiversityRatioFieldMap: ReadonlyMap<
  string,
  string
> = new Map<string, string>([
  [PatientRaceUk.ASIAN, UkPatientCountField.patientCountAsian],
  [PatientRaceUk.BLACK, UkPatientCountField.patientCountBlack],
  [PatientRaceUk.MIXED, UkPatientCountField.patientCountMixed],
  [PatientRaceUk.WHITE, UkPatientCountField.patientCountWhite],
  [PatientRaceUk.OTHER, UkPatientCountField.patientCountOther],
  [PatientRaceUk.UNKNOWN, UkPatientCountField.patientCountUnknown]
]);

const nationalityFilterValueToFrancePatientsDiversityRatioFieldMap: ReadonlyMap<
  string,
  string
> = new Map<string, string>([
  [
    PatientNationalityFrance.FRANCE_NATIVE,
    FrancePatientCountField.patientCountFranceNative
  ],
  [
    PatientNationalityFrance.EUROPEAN_UNION,
    FrancePatientCountField.patientCountEuropeanUnion
  ],
  [
    PatientNationalityFrance.OTHER_EUROPEAN,
    FrancePatientCountField.patientCountOtherEuropean
  ],
  [
    PatientNationalityFrance.MIDDLE_EAST_NORTH_AFRICA,
    FrancePatientCountField.patientCountMiddleEastAndNorthAfrica
  ],
  [
    PatientNationalityFrance.CENTRAL_SOUTH_AFRICA,
    FrancePatientCountField.patientCountCentralAndSouthAfrica
  ],
  [
    PatientNationalityFrance.OCEANIA,
    FrancePatientCountField.patientCountOceania
  ],
  [
    PatientNationalityFrance.NORTH_AMERICA,
    FrancePatientCountField.patientCountNorthAmerica
  ],
  [
    PatientNationalityFrance.SOUTH_AMERICA,
    FrancePatientCountField.patientCountSouthAmerica
  ],
  [
    PatientNationalityFrance.CENTRAL_AMERICA,
    FrancePatientCountField.patientCountCentralAmerica
  ],
  [
    PatientNationalityFrance.SOUTHEAST_ASIA,
    FrancePatientCountField.patientCountSoutheastAsia
  ],
  [
    PatientNationalityFrance.EAST_ASIA,
    FrancePatientCountField.patientCountEastAsia
  ],
  [
    PatientNationalityFrance.SOUTH_ASIA,
    FrancePatientCountField.patientCountSouthAsia
  ]
]);

const nationalityFilterValueToSpainPatientsDiversityRatioFieldMap: ReadonlyMap<
  string,
  string
> = new Map<string, string>([
  [PatientNationalitySpain.SPAIN, SpainPatientCountField.patientCountSpain],
  [
    PatientNationalitySpain.EUROPEAN_UNION,
    SpainPatientCountField.patientCountEuropeanUnion
  ],
  [
    PatientNationalitySpain.OTHER_EUROPEAN,
    SpainPatientCountField.patientCountOtherEuropean
  ],
  [
    PatientNationalitySpain.MIDDLE_EAST_NORTH_AFRICA,
    SpainPatientCountField.patientCountMiddleEastAndNorthAfrica
  ],
  [
    PatientNationalitySpain.CENTRAL_SOUTH_AFRICA,
    SpainPatientCountField.patientCountCentralAndSouthAfrica
  ],
  [PatientNationalitySpain.OCEANIA, SpainPatientCountField.patientCountOceania],
  [
    PatientNationalitySpain.NORTH_AMERICA,
    SpainPatientCountField.patientCountNorthAmerica
  ],
  [
    PatientNationalitySpain.SOUTH_AMERICA,
    SpainPatientCountField.patientCountSouthAmerica
  ],
  [
    PatientNationalitySpain.CENTRAL_AMERICA,
    SpainPatientCountField.patientCountCentralAmerica
  ],
  [
    PatientNationalitySpain.SOUTHEAST_ASIA,
    SpainPatientCountField.patientCountSoutheastAsia
  ],
  [
    PatientNationalitySpain.EAST_ASIA,
    SpainPatientCountField.patientCountEastAsia
  ],
  [
    PatientNationalitySpain.SOUTH_ASIA,
    SpainPatientCountField.patientCountSouthAsia
  ],
  [
    PatientNationalitySpain.CENTRAL_ASIA,
    SpainPatientCountField.patientCountCentralAsia
  ],
  [PatientNationalitySpain.OTHER, SpainPatientCountField.patientCountOther],
  [PatientNationalitySpain.UNKNOWN, SpainPatientCountField.patientCountUnknown]
]);

export type InstitutionSearchFeatureFlag =
  (typeof institutionSearchFeatureFlagTypes)[number];

export const featureFlagDefaults: Readonly<
  Record<keyof InstitutionSearchFeatureFlags, { key: string; default: boolean }>
> = {
  enableCTMSV2: {
    key: "enable-ctms-v2",
    default: false
  },
  enableClaimsFilterFunctionScore: {
    key: "enable-claims-filter-function-score",
    default: false
  },
  enableClaimsFilteringMatchedCountsUpdate: {
    key: "enable-claims-filtering-matched-counts-update",
    default: false
  },
  enableTagsInElasticsearch: {
    key: "search.enable-tags-in-elasticsearch",
    default: false
  },
  enableNonIOLsInTrialLandscapeInstitutionSearch: {
    key: "enable-non-iols-in-trial-landscape-institution-search",
    default: false
  },
  enableUniquePatientCountForClaims: {
    key: "search.enable-unique-patient-count-for-claims",
    default: false
  },
  enableBrazilianClaims: {
    key: "enable-brazilian-claims",
    default: false
  },
  enablePatientMatchesForInstitutionProfilePage: {
    key: "enable-patient-matches-for-institution-profile-page",
    default: false
  },
  disableUniquePatientCountForOnlyProcedures: {
    key: "search.disable-unique-patient-count-for-only-procedures",
    default: false
  },
  enableEnhancedHasCTMSDataFilterBehavior: {
    key: "enable-enhanced-has-ctms-data-filter-behavior-for-people-and-institution-search",
    default: false
  },
  useDocIdForParentPatientClaims: {
    key: "search.use-docid-for-parent-patientclaims",
    default: false
  },
  enableInstitutionNameSuggest: {
    key: "search.enable-institution-name-suggest",
    default: false
  },
  enableLocationFilterRegionRollup: {
    key: "enable-location-filter-region-rollup",
    default: false
  },
  enableCcsrExclusionForMatchedCounts: {
    key: "enable-ccsr-exclusion-for-matched-counts",
    default: false
  },
  enableNewIolH1DefaultRanking: {
    key: "enable-new-iol-h-1-default-ranking",
    default: false
  },
  enablePenalizeAutoCreatedIOLs: {
    key: "enable-penalize-auto-created-io-ls",
    default: false
  },
  enableBothOperatorForSearch: {
    key: "enable-both-operator-for-hcpu-search",
    default: false
  },
  enableInstitutionOrgHierarchy: {
    key: "enable-institution-org-hierarchy",
    default: false
  },
  usePatientIndexForCount: {
    key: "use-patient-index-for-count",
    default: false
  }
};

export const INSTITUTION_INTENT_THRESHOLD = 0.3;

type MSearchRequest = [
  estypes.MsearchMultisearchHeader,
  estypes.MsearchMultisearchBody
];

type SearchFieldMap = {
  sex: string;
  ageRange: string;
  race: string;
  [key: string]: string;
};

export interface DocCountBucket {
  key: string;
  doc_count: number;
  ccsr_size?: {
    buckets: DocCountBucket[];
  };
  matching_desc?: {
    buckets: DocCountBucket[];
  };
}

export interface NestedDocCountBucket extends DocCountBucket {
  institutions: {
    doc_count: number;
  };
}

export interface LocationRegionsInDocCountBucket extends DocCountBucket {
  regions_in: AggregationsTermsAggregateBase<DocCountBucket>;
}

export interface RegionsDocCountBucket extends DocCountBucket {
  locations_in_region: AggregationsTermsAggregateBase<DocCountBucket>;
}

export interface LocationFilteredMatchingAggregation {
  filtered_matching: AggregationsTermsAggregateBase<LocationRegionsInDocCountBucket>;
}

export interface DocCountClaimsBucket {
  doc_count: number;
  filteredClaims: {
    doc_count: number;
    counts: {
      value: number;
    };
  };
}
export interface ClaimsAggregationDocCountBucket {
  key: string;
  doc_count: number;
  procedures: DocCountClaimsBucket;
  diagnoses: DocCountClaimsBucket;
}

export interface ValueAggregateBucket extends DocCountBucket {
  total: AggregationsSimpleValueAggregate;
}

export interface DiagnosesCount {
  diagnosis_count: number;
  diagnosesCount_1_year?: number;
  diagnosesCount_2_year?: number;
  diagnosesCount_5_year?: number;
  diagnosesUniqueCount?: number;
  diagnosesUniqueCount_1_year?: number;
  diagnosesUniqueCount_2_year?: number;
  diagnosesUniqueCount_5_year?: number;
}

export interface ProceduresCount {
  procedure_count: number;
  proceduresCount_1_year?: number;
  proceduresCount_2_year?: number;
  proceduresCount_5_year?: number;
  proceduresUniqueCount?: number;
  proceduresUniqueCount_1_year?: number;
  proceduresUniqueCount_2_year?: number;
  proceduresUniqueCount_5_year?: number;
}

export interface PrescriptionsCount {
  num_prescriptions?: number;
  num_prescriptions_1_year?: number;
  num_prescriptions_2_year?: number;
  num_prescriptions_5_year?: number;
}

export type ElasticsearchInstitutionDoc = {
  id: number;
  institutionId: number;
  masterOrganizationId?: number;
  ultimate_parent_id?: string;
  ultimate_parent_name?: string;
  child_affiliation_count?: number;
  groupH1dnOrganizationId?: string;
  name: string;
  nameTranslations?: {
    name: string;
    languageCode: string;
  }[];
  person_count: number;
  affiliation_count: number;
  publication_count: number;
  citation_count: number;
  social_mention_count: number;
  conference_count: number;
  general_institution_payment_total: number;
  research_institution_payment_total: number;
  general_institution_payment_count: number;
  research_institution_payment_count: number;
  sent_referral_count: number;
  received_referral_count: number;
  totalPatientDocs?: number;
  totalClaimCountUk?: number;
  totalClaimCountFrance?: number;
  totalClaimCountSpain?: number;
  trials_count: number;
  institution_type: string;
  website: string;
  beds: number;
  region: string;
  linkedin_url: string;
  twitter_url: string;
  "address.street1": string;
  "address.street2": string;
  "address.street3": string;
  "address.region": string;
  "address.region_code": string;
  "address.country": string;
  "address.country_code": string;
  "address.city": string;
  "address.postal_code": string;
  "filters.city": string;
  "filters.region": string;
  "filters.country": string;
  "filters.postal_code": string;
  addressTranslations?: {
    id: number;
    street1?: string;
    street2?: string;
    street3?: string;
    postal_code?: string;
    city?: string;
    region?: string;
    region_code?: string;
    country?: string;
    country_code?: string;
    languageCode: string;
  }[];
  location: {
    lat: string;
    lon: string;
  };
  lat_long_precision?: number;
  patientsDiversityRatio: PatientsDiversityRaceInfo;
  patientsDiversityRatioUk?: PatientsUkDiversityRaceInfo;
  patientsDiversityRatioFrance?: PatientsFranceDiversityRaceInfo;
  patientsDiversityRatioSpain?: PatientsSpainDiversityRaceInfo;
  patientDiversity: {
    age?: PatientsDiversityAgeInfo[];
    sex?: PatientsDiversitySexInfo[];
  };
  hasCtms?: boolean;
  inCtmsNetwork?: boolean;
  nci_designated_cancer_center?: boolean;
  trialEnrollmentRate?: number;
  enrollment_rates_by_indication?: ESEnrollmentRatesByIndication[];
  national_comprehensive_cancer_network_member?: boolean;
  teaching_hospital?: boolean;
  community_hospital?: boolean;
  research_facility?: boolean;
  flags?: {
    nci_designated_cancer_center: boolean;
    national_comprehensive_cancer_network_member: boolean;
    teaching_hospital: boolean;
    nih_community_site: boolean;
    minority_community_site: boolean;
    research_facility: boolean;
    cro: boolean;
    part_of_system: {
      part_of_system: boolean;
      health_system: boolean;
      university_system: boolean;
    };
    private_practice: {
      private_practice: boolean;
      large: boolean;
      small: boolean;
    };
    government: {
      government: boolean;
      military: boolean;
      veterans_affairs: boolean;
    };
  };
  cro?: boolean;
  tagIds?: string[];
  orgTypes?: string;
  orgTypesLevel2?: string;
  orgTypesLevel3?: string;
} & DiagnosesCount &
  ProceduresCount &
  PrescriptionsCount;

export interface ElasticsearchTaggedInstitutionDoc {
  id: string;
  masterOrganizationId?: number;
  groupH1dnOrganizationId?: string;
  name: string;
  location: LatLonGeoLocation;
  lat_long_precision?: number;
  tagIds: string[];
  "address.street1": string;
  "address.street2": string;
  "address.street3": string;
  "address.region": string;
  "address.region_code": string;
  "address.country": string;
  "address.country_code": string;
  "address.city": string;
  "address.postal_code": string;
  region: string;
  patientsDiversityRatio: PatientsDiversityRaceInfo;
  patientDiversity: {
    age?: PatientsDiversityAgeInfo[];
    sex?: PatientsDiversitySexInfo[];
  };
}

type nestedPath =
  | "congress"
  | "congresses"
  | "trials"
  | "publications"
  | "payments"
  | "diagnoses"
  | "procedures"
  | "DRG_diagnoses"
  | "DRG_procedures"
  | "prescriptions"
  | "patientClaimsUk"
  | "patientClaimsFrance"
  | "patientClaimsSpain"
  | "ccsr"
  | "ccsr_px";

export interface QueryBuilderArguments {
  name?: nestedPath;
  path: nestedPath;
  fields: string[];
  searchQuery: string | undefined;
  innerHits?: estypes.SearchInnerHits;
  analyzer?: string;
  saturation?: number;
  raceValues?: string[];
  boost?: number;
}

interface ClaimsQueryBuilderArguments
  extends Omit<QueryBuilderArguments, "path"> {
  path:
    | ClaimType
    | "patientClaimsUk"
    | "patientClaimsFrance"
    | "patientClaimsSpain";
  countField: string;
}

export type ClaimType =
  | "diagnoses"
  | "procedures"
  | "prescriptions"
  | "ccsr"
  | "ccsr_px";
type ClaimsCode = string[] | null | undefined;

type FilterAutocompleteType = ClaimType | "ageRange" | "race" | "sex";
export type RequestType = "iol" | "count";

interface NestedClaimsQuery {
  nested: {
    path: string;
    query: {
      bool: {
        filter: estypes.QueryDslQueryContainer[];
      };
    };
    inner_hits?: {
      _source: boolean;
      docvalue_fields: string[];
      size: number;
    };
  };
}

interface ClaimsRangeTypeMapping {
  countPropertyMin: keyof ClaimFiltersInput;
  countPropertyMax: keyof ClaimFiltersInput;
  fieldName: () => string;
}

type TrialsFilterKeys =
  | "filters.trialsFilters.sponsor"
  | "filters.trialsFilters.sponsorType"
  | "filters.trialsFilters.status"
  | "filters.trialsFilters.phase"
  | "filters.trialsFilters.studyType"
  | "filters.trialsFilters.id"
  | "filters.trialsFilters.biomarker";

type TrialsESPathKeys =
  | "trials.sponsors"
  | "trials.sponsor_types"
  | "trials.status"
  | "trials.phase"
  | "trials.type"
  | "trials.id"
  | "trials.biomarker";

enum HeatMapZoomLevelFields {
  Level1Geography = "level1Geography",
  Level2Geography = "level2Geography",
  Level3Geography = "level3Geography"
}

const AGGREGATE_HITS = 10;
const IGNORE_HITS = 0;
const FUZZINESS = "AUTO:4,7";
const INSTITUTION_NAME_FIELD_HIT_BOOST = 100.0;
const OR = " | ";
export const MIN_DIVERSITY_SCORE = 0.000000001;
const PATIENT_POPULATION_WEIGHT = 10;
const H1_DEFAULT_WEIGHT = 1;
const ZERO_LENGTH_FILTER_VALUE = "";
const LOCATIONS_IN_REGION_AGGREGATION_SIZE = 200;

export const PUNCTUATION_REMOVER_ANALYZER = "institute_name_analyzer";
export const PUNCTUATION_SPLITTER_ANALYZER = "name_analyzer";
export const CLAIMS_DESCRIPTION_ANALYZER = "main_analyzer";
const MASTER_ORGANIZATION_ID = "masterOrganizationId";
const PROJECT_IDS = "projectIds";
const SCRIPT_IS_DIVERSITY_ZERO_OR_NULL =
  "(doc['patientsDiversityRatio.whiteNonHispanic'].size() != 0)&&(doc['patientsDiversityRatio.whiteNonHispanic'].value != 1)";
export const SCRIPT_IS_LOCATED_IN_EXCLUDED_CLAIMS_REGION =
  "doc['filters.country'].size() != 0 && params.countriesToFilterOut.contains(doc['filters.country'].value)";
export const SCRIPT_SCORE_FOR_TRIAL_INVESIGATOR_COUNT =
  "(doc['trials.investigator_count'].size()==0 || doc['trials.investigator_count'].value<=0) ? 0 : doc['trials.investigator_count'].value";

export const TRIAL_IS_COMPLETED = {
  term: {
    "trials.status": "Completed"
  }
};

export const SCRIPT_COUNT_PI_IN_TRIALS = "doc['trials.person_ids'].size()";

const AFFILIATIONS_INSTITUTION_IS_IOL = {
  term: {
    "affiliations.institution.isIol": true
  }
};

const AFFILIATIONS_IS_CURRENT = {
  term: {
    "affiliations.isCurrent": true
  }
};

const AFFILIATIONS_IS_WORK_TYPE = {
  term: {
    "affiliations.type": "Work Affiliation"
  }
};

const INSTITUTION_IS_IOL = {
  term: {
    isIol: true
  }
};

const INSTITUTION_IN_CTMS_NETWORK = {
  term: {
    inCtmsNetwork: true
  }
};

const MATCH_ALL = {
  match_all: {}
};

const queryableAddressFields = {
  country: ["address.country", "address.country_code"],
  region: ["address.region", "address.region_code"],
  city: ["address.city"],
  postal_code: ["address.postal_code"]
};

const queryableRegionFields = {
  country_level_regions: "address.country_level_regions",
  state_level_regions: "address.state_level_regions",
  city_level_regions: "address.city_level_regions"
};

const queryableOrgTypesFields = {
  orgTypes: "orgTypes",
  orgTypesLevel2: "orgTypesLevel2",
  orgTypesLevel3: "orgTypesLevel3"
};

const queryableFields = {
  ...queryableAddressFields,
  ...queryableOrgTypesFields
};
// These elasticsearch institution index source fields are retrieved with the hits to populate the response.
export const SOURCE_INCLUDES: string[] = [
  MASTER_ORGANIZATION_ID,
  "id",
  "institutionId",
  "name",
  "ultimate_parent_id",
  "ultimate_parent_name",
  "child_affiliation_count",
  "nameTranslations",
  "institutionType",
  "website",
  "beds",
  "region",
  "linkedinUrl",
  "twitterUrl",
  "person_count",
  "publication_count",
  "conference_count",
  "trials_count",
  "diagnosis_count",
  "diagnosesCount_1_year",
  "diagnosesCount_2_year",
  "diagnosesCount_5_year",
  "diagnosesUniqueCount",
  "diagnosesUniqueCount_1_year",
  "diagnosesUniqueCount_2_year",
  "diagnosesUniqueCount_5_year",
  "procedure_count",
  "proceduresCount_1_year",
  "proceduresCount_2_year",
  "proceduresCount_5_year",
  "proceduresUniqueCount",
  "proceduresUniqueCount_1_year",
  "proceduresUniqueCount_2_year",
  "proceduresUniqueCount_5_year",
  "num_prescriptions",
  "num_prescriptions_1_year",
  "num_prescriptions_2_year",
  "num_prescriptions_5_year",
  "totalClaimCountUk",
  "totalClaimCountFrance",
  "totalClaimCountSpain",
  "subIcbLocationCode",
  "patientsDiversityRatioUk",
  "patientsDiversityRatioFrance",
  "patientsDiversityRatioSpain",
  "general_institution_payment_total",
  "research_institution_payment_total",
  "address.street1",
  "address.street2",
  "address.street3",
  "address.city",
  "address.postal_code",
  "address.region",
  "address.region_code",
  "address.country",
  "address.country_code",
  "addressTranslations",
  "location",
  "lat_long_precision",
  "hasCtms",
  "inCtmsNetwork",
  "trialEnrollmentRate",
  "enrollment_rates_by_indication",
  "patientsDiversityRatio",
  "patientDiversity",
  "totalPatientDocs",
  "nci_designated_cancer_center",
  "national_comprehensive_cancer_network_member",
  "teaching_hospital",
  "community_hospital",
  "research_facility",
  "cro",
  "flags",
  "orgTypes",
  "orgTypesLevel2",
  "orgTypesLevel3",
  "tagIds"
];

export const SOURCE_INCLUDES_FOR_BULK_ENTITY_SEARCH: string[] = [
  MASTER_ORGANIZATION_ID,
  "id",
  "name"
];

const SOURCE_INCLUDES_FOR_TAGGED_INSTITUTIONS_SEARCH: string[] = [
  MASTER_ORGANIZATION_ID,
  "id",
  "name",
  "groupH1dnOrganizationId",
  "location",
  "lat_long_precision",
  "tagIds",
  "address.street1",
  "address.street2",
  "address.street3",
  "address.city",
  "address.postal_code",
  "address.region",
  "address.region_code",
  "address.country",
  "address.country_code",
  "region",
  "patientsDiversityRatio",
  "patientDiversity"
];

const HEADER: Readonly<estypes.MsearchMultisearchHeader> = {};

const ALL_ASSETS_OPTIONAL = 0;
const AT_LEAST_ONE_ASSET = 1;
export const DIAGNOSES_SATURATION = 50000;
export const PROCEDURES_SATURATION = 7000;
export const PRESCRIPTIONS_SATURATION = 4000;
export const TRIALS_SATURATION = 10;
export const CTMS_TRIALS_SATURATION = 3;
const CTMS_TRIALS_BOOST = 5;
const NAME_HIT_BOOST = 0.01;
export const CTMS_TRIALS_BOOST_FOR_DIVERSITY = 10;

const DEFAULT_TAGGED_INSTITUTION_SEARCH_SIZE = 100;

const SKIP_INNER_HITS = true;
const INCLUDE_INNER_HITS = false;
const DIAGNOSES = "diagnoses";
const PROCEDURES = "procedures";
const PRESCRIPTIONS = "prescriptions";
const CCSR = "ccsr";
const CCSR_PX = "ccsr_px";
const NESTED_CLAIMS_FUNCTION_SCORE_TYPE_MAPPING = {
  diagnoses: {
    path: "diagnoses" as ClaimType,
    claimCodesProperty: "diagnosesICD",
    claimsMinCount: "diagnosesICDMinCount",
    claimsMaxCount: "diagnosesICDMaxCount"
  },
  procedures: {
    path: "procedures" as ClaimType,
    claimCodesProperty: "proceduresCPT",
    claimsMinCount: "proceduresCPTMinCount",
    claimsMaxCount: "proceduresCPTMaxCount"
  },
  prescriptions: {
    path: "prescriptions" as ClaimType,
    claimCodesProperty: "genericNames",
    claimsMinCount: "prescriptionsMinCount",
    claimsMaxCount: "prescriptionsMaxCount"
  },
  ccsr: {
    path: "ccsr" as ClaimType,
    claimCodesProperty: "ccsr",
    claimsMinCount: "diagnosesICDMinCount",
    claimsMaxCount: "diagnosesICDMaxCount"
  },
  ccsr_px: {
    path: "ccsr_px" as ClaimType,
    claimCodesProperty: "ccsr_px",
    claimsMinCount: "proceduresCPTMinCount",
    claimsMaxCount: "proceduresCPTMaxCount"
  }
};

export const DEFAULT_UK_PATIENT_DIVERSITY_WEIGHTED_CLAIMS = [
  "(doc['patientClaimsUk.patientCountAsian'].size() == 0 ? 0 : doc['patientClaimsUk.patientCountAsian'].value)",
  "(doc['patientClaimsUk.patientCountBlack'].size() == 0 ? 0 : doc['patientClaimsUk.patientCountBlack'].value)",
  "(doc['patientClaimsUk.patientCountMixed'].size() == 0 ? 0 : doc['patientClaimsUk.patientCountMixed'].value)"
];

export const DEFAULT_FRANCE_PATIENT_DIVERSITY_WEIGHTED_CLAIMS = [
  "(doc['patientClaimsFrance.patientCountEuropeanUnion'].size() == 0 ? 0 : doc['patientClaimsFrance.patientCountEuropeanUnion'].value)",
  "(doc['patientClaimsFrance.patientCountMiddleEastAndNorthAfrica'].size() == 0 ? 0 : doc['patientClaimsFrance.patientCountMiddleEastAndNorthAfrica'].value)",
  "(doc['patientClaimsFrance.patientCountOtherEuropean'].size() == 0 ? 0 : doc['patientClaimsFrance.patientCountOtherEuropean'].value)",
  "(doc['patientClaimsFrance.patientCountCentralAndSouthAfrica'].size() == 0 ? 0 : doc['patientClaimsFrance.patientCountCentralAndSouthAfrica'].value)",
  "(doc['patientClaimsFrance.patientCountOceania'].size() == 0 ? 0 : doc['patientClaimsFrance.patientCountOceania'].value)",
  "(doc['patientClaimsFrance.patientCountNorthAmerica'].size() == 0 ? 0 : doc['patientClaimsFrance.patientCountNorthAmerica'].value)",
  "(doc['patientClaimsFrance.patientCountSouthAmerica'].size() == 0 ? 0 : doc['patientClaimsFrance.patientCountSouthAmerica'].value)",
  "(doc['patientClaimsFrance.patientCountSoutheastAsia'].size() == 0 ? 0 : doc['patientClaimsFrance.patientCountSoutheastAsia'].value)",
  "(doc['patientClaimsFrance.patientCountEastAsia'].size() == 0 ? 0 : doc['patientClaimsFrance.patientCountEastAsia'].value)",
  "(doc['patientClaimsFrance.patientCountSouthAsia'].size() == 0 ? 0 : doc['patientClaimsFrance.patientCountSouthAsia'].value)",
  "(doc['patientClaimsFrance.patientCountCentralAmerica'].size() == 0 ? 0 : doc['patientClaimsFrance.patientCountCentralAmerica'].value)"
];

export const DEFAULT_SPAIN_PATIENT_DIVERSITY_WEIGHTED_CLAIMS = [
  "(doc['patientClaimsSpain.patientCountEuropeanUnion'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountEuropeanUnion'].value)",
  "(doc['patientClaimsSpain.patientCountMiddleEastAndNorthAfrica'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountMiddleEastAndNorthAfrica'].value)",
  "(doc['patientClaimsSpain.patientCountOtherEuropean'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountOtherEuropean'].value)",
  "(doc['patientClaimsSpain.patientCountCentralAndSouthAfrica'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountCentralAndSouthAfrica'].value)",
  "(doc['patientClaimsSpain.patientCountOceania'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountOceania'].value)",
  "(doc['patientClaimsSpain.patientCountNorthAmerica'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountNorthAmerica'].value)",
  "(doc['patientClaimsSpain.patientCountSouthAmerica'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountSouthAmerica'].value)",
  "(doc['patientClaimsSpain.patientCountSoutheastAsia'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountSoutheastAsia'].value)",
  "(doc['patientClaimsSpain.patientCountEastAsia'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountEastAsia'].value)",
  "(doc['patientClaimsSpain.patientCountSouthAsia'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountSouthAsia'].value)",
  "(doc['patientClaimsSpain.patientCountCentralAsia'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountCentralAsia'].value)",
  "(doc['patientClaimsSpain.patientCountCentralAmerica'].size() == 0 ? 0 : doc['patientClaimsSpain.patientCountCentralAmerica'].value)"
];

export type ScoringValueRange = {
  min: number;
  mean: number;
  max: number;
};

export type ScaleScoreTargetRange = {
  targetMin: number;
  targetMax: number;
};

export type RootFieldsForScoringForH1Default =
  | "trials_count"
  | "research_institution_payment_total"
  | "trialInvestigatorCount"
  | "totalPatientDocs"
  | "totalDiagnosesCount";

export const assetAggregationValues: Readonly<
  Record<RootFieldsForScoringForH1Default, ScoringValueRange>
> = {
  trials_count: {
    min: 1,
    mean: 31.5,
    max: 8178
  },
  research_institution_payment_total: {
    min: 0,
    mean: 1549991.39,
    max: 358108759
  },
  trialInvestigatorCount: {
    min: 1,
    mean: 24.57,
    max: 1596
  },
  totalPatientDocs: {
    min: 1,
    mean: 4957.12,
    max: 4552952
  },
  totalDiagnosesCount: {
    min: 0,
    mean: 156874.57,
    max: 239001410
  }
};

export const priorityBasedScoreScaleRanges: Readonly<
  Record<number, ScaleScoreTargetRange>
> = {
  0: { targetMin: 1, targetMax: 5 },
  1: { targetMin: 10000, targetMax: 20000 },
  2: { targetMin: 1000, targetMax: 5000 },
  3: { targetMin: 100, targetMax: 500 },
  4: { targetMin: 10, targetMax: 50 }
};

export const fieldPriorityMapForNewTLH1DefaultRanking: Readonly<
  Record<RootFieldsForScoringForH1Default, number>
> = {
  trials_count: 1,
  research_institution_payment_total: 2,
  trialInvestigatorCount: 2,
  totalPatientDocs: 4,
  totalDiagnosesCount: 4
};

/**
 * Maps country field values from ElasticSearch to their corresponding country names
 * as used in the pipeline database.
 */
export const elasticCountryFieldToPipelineDBCountryMap: Record<string, string> =
  {
    Austria: "Austria",
    Brazil: "Brazil",
    Denmark: "Denmark",
    France: "France",
    Germany: "Germany",
    "Germany,United Kingdom,Norway": "Germany",
    Ireland: "Ireland",
    Italy: "Italy",
    "Norway,Denmark,Sweden,Finland": "Norway",
    Spain: "Spain",
    "United Kingdom": "United Kingdom",
    UK: "United Kingdom",
    GBR: "United Kingdom",
    US: "US",
    USA: "US",
    "United States": "US",
    "United States of America": "US"
  };

function hasSource(doc: SearchHit<any>) {
  return !!doc._source;
}

function getHitsTotal(total: number | SearchTotalHits) {
  if (typeof total === "number") {
    return total;
  }

  return total.value;
}
export type InstitutionClaimsMapForExports = {
  diagnosesCountMap: Dictionary<
    Dictionary<{
      count: number;
      scheme: string | undefined;
      description: string | undefined;
    }>
  >;
  proceduresCountMap: Dictionary<
    Dictionary<{
      count: number;
      scheme: string | undefined;
      description: string | undefined;
    }>
  >;
};
export type SearchDirectlyResponse = {
  data: SearchResponse<ElasticsearchInstitutionDoc>;
  peopleData: Array<estypes.MsearchResponseItem>;
  sharedPeopleFromAffiliationCounts?: Dictionary<number>;
  isQueryAnInstitution: boolean;
  keywordIcdCodeSynonyms?: string[];
  indicationIcdCodeSynonyms?: string[];
  indicationSynonyms?: string[];
  resolvedIndications: string[];
  peopleDataForIE?: PeopleSearchInclusionExclusionAggregationResponse | null;
  matchedPatientCountMap?: Dictionary<number>;
  matchedClaimsForInstitutionsMap?: InstitutionClaimsMapForExports;
  patientDiversityDistributionMap?: Dictionary<{
    race: DocCountBucket[];
    gender: DocCountBucket[];
    age: DocCountBucket[];
  }>;
  trialsOngoingCountMap?: Dictionary<number>;
  trialsActivelyRecruitingCountMap?: Dictionary<number>;
  totalInstitutionsInArea?: number;
};

const EMPTY_STRING = "";

export const SCORING_FUNCTIONS_FOR_TRIAL_PERFORMANCE_RANKING_WITHOUT_TRIAL_COUNT: QueryDslFunctionScoreContainer[] =
  [
    {
      script_score: {
        script: `doc['ctmsOrganizationStats.averagePatientsEnrolled'].size() == 0 ?
          0 : saturation(doc['ctmsOrganizationStats.averagePatientsEnrolled'].value, 10)`
      }
    },
    {
      script_score: {
        script: `doc['ctmsOrganizationStats.averagePatientRetentionPercentage'].size() == 0 ?
          0 : 3 * Math.tanh(doc['ctmsOrganizationStats.averagePatientRetentionPercentage'].value / 100)`
      }
    },
    {
      script_score: {
        script: `doc['ctmsOrganizationStats.averageTimeToFirstPatientIn'].size() == 0 ?
          0 : 5 / (doc['ctmsOrganizationStats.averageTimeToFirstPatientIn'].value + 1)`
      }
    },
    {
      script_score: {
        script: `doc['ctmsOrganizationStats.averagePatientRecruitmentPeriod'].size() == 0 ?
          0 : 5 / (doc['ctmsOrganizationStats.averagePatientRecruitmentPeriod'].value + 1)`
      }
    }
  ];

export const SCORING_FUNCTIONS_FOR_PENALIZING_AUTO_CREATED_IOL: QueryDslFunctionScoreContainer[] =
  [
    {
      filter: {
        term: {
          is_auto_created: true
        }
      },
      script_score: getSaturationScriptFunction(100).script_score
    }
  ];

export const SCORING_FUNCTIONS_FOR_TRIAL_PERFORMANCE_RANKING: QueryDslFunctionScoreContainer[] =
  [
    {
      field_value_factor: {
        field: "ctmsOrganizationStats.trialCount",
        missing: 0,
        factor: 5
      }
    },
    ...SCORING_FUNCTIONS_FOR_TRIAL_PERFORMANCE_RANKING_WITHOUT_TRIAL_COUNT
  ];
const INSTITUTION_ID_FIELD_IN_PEOPLE =
  "affiliations.institution.masterOrganizationId";

const LEVEL_1_ORG_TYPES_TO_FILTER_OUT_FROM_TRIAL_LANDSCAPE = ["Company"];

const LEVEL_2_ORG_TYPES_TO_FILTER_OUT_FROM_TRIAL_LANDSCAPE = [
  "Respite Care Facility",
  "Suppliers",
  "Laboratories",
  "Nursing & Custodial Care Facilities",
  "Transportation Services"
];

const LEVEL_3_ORG_TYPES_TO_FILTER_OUT_FROM_TRIAL_LANDSCAPE = [
  "Ambulatory Surgical Clinic/Center",
  "Emergency Care Clinic/Center",
  "State or Local Public Health Clinic/Center",
  "Assisted Living Facility (Mental Illness)",
  "Assisted Living Facility (Behavioral Disturbances)",
  "Meals Provider",
  "Mammography Clinic/Center",
  "Ambulatory Family Planning Facility",
  "Federal Public Health Clinic/Center",
  "Foster Care Agency",
  "Lodging Provider",
  "Urgent Care Clinic/Center",
  "Radiology Clinic/Center",
  "Voluntary or Charitable Agency",
  "Dental Clinic/Center",
  "Radiation Oncology Clinic/Center",
  "Mobile Mammography Clinic/Center",
  "Mobile Radiology Clinic/Center",
  "Home Delivered Meals",
  "Home Health Agency",
  "Public Health or Welfare Agency",
  "Podiatric Clinic/Center",
  "Magnetic Resonance Imaging (MRI) Clinic/Center",
  "Local Education Agency (LEA)",
  "Nursing Care Agency",
  "Supports Brokerage Agency"
];

const IRRELEVANT_ORG_TYPE_FILTER_FOR_TRIAL_LANDSCAPE: QueryDslQueryContainer = {
  bool: {
    must_not: [
      {
        terms: {
          "orgTypes.keyword":
            LEVEL_1_ORG_TYPES_TO_FILTER_OUT_FROM_TRIAL_LANDSCAPE
        }
      },
      {
        terms: {
          "orgTypesLevel2.keyword":
            LEVEL_2_ORG_TYPES_TO_FILTER_OUT_FROM_TRIAL_LANDSCAPE
        }
      },
      {
        terms: {
          "orgTypesLevel3.keyword":
            LEVEL_3_ORG_TYPES_TO_FILTER_OUT_FROM_TRIAL_LANDSCAPE
        }
      }
    ]
  }
};

const PACIFIC = ["AK", "CA", "HI", "OR", "WA"];
const MOUNTAIN = ["AZ", "CO", "ID", "MT", "NV", "NM", "UT", "WY"];
const WEST_NORTH_CENTRAL = ["IA", "KS", "MN", "MO", "NE", "ND", "SD"];
const WEST_SOUTH_CENTRAL = ["AR", "LA", "OK", "TX"];
const EAST_NORTH_CENTRAL = ["IL", "IN", "MI", "OH", "WI"];
const EAST_SOUTH_CENTRAL = ["AL", "KY", "MS", "TN"];
const SOUTH_ATLANTIC = ["DE", "DC", "FL", "GA", "MD", "NC", "SC", "VA", "WV"];
const MIDDLE_ATLANTIC = ["NJ", "NY", "PA"];
const NEW_ENGLAND = ["CT", "ME", "MA", "NH", "RI", "VT"];
export const REGION_TO_STATE_CODES = new Map([
  ["Pacific", PACIFIC],
  ["Mountain", MOUNTAIN],
  ["West North Central", WEST_NORTH_CENTRAL],
  ["West South Central", WEST_SOUTH_CENTRAL],
  ["East North Central", EAST_NORTH_CENTRAL],
  ["East South Central", EAST_SOUTH_CENTRAL],
  ["South Atlantic", SOUTH_ATLANTIC],
  ["Middle Atlantic", MIDDLE_ATLANTIC],
  ["New England", NEW_ENGLAND],
  ["Northeast", [...NEW_ENGLAND, ...MIDDLE_ATLANTIC]],
  ["Midwest", [...WEST_NORTH_CENTRAL, ...EAST_NORTH_CENTRAL]],
  ["South", [...WEST_SOUTH_CENTRAL, ...EAST_SOUTH_CENTRAL, ...SOUTH_ATLANTIC]],
  ["West", [...PACIFIC, ...MOUNTAIN]]
]);
export const VALID_STATE_NAMES = new Set([
  "Alabama",
  "Alaska",
  "Arizona",
  "Arkansas",
  "California",
  "Colorado",
  "Connecticut",
  "District Of Columbia",
  "Delaware",
  "Florida",
  "Georgia",
  "Hawaii",
  "Idaho",
  "Illinois",
  "Indiana",
  "Iowa",
  "Kansas",
  "Kentucky",
  "Louisiana",
  "Maine",
  "Maryland",
  "Massachusetts",
  "Michigan",
  "Minnesota",
  "Mississippi",
  "Missouri",
  "Montana",
  "Nebraska",
  "Nevada",
  "New Hampshire",
  "New Jersey",
  "New Mexico",
  "New York",
  "North Carolina",
  "North Dakota",
  "Ohio",
  "Oklahoma",
  "Oregon",
  "Pennsylvania",
  "Rhode Island",
  "South Carolina",
  "South Dakota",
  "Tennessee",
  "Texas",
  "Utah",
  "Vermont",
  "Virginia",
  "Washington",
  "West Virginia",
  "Wisconsin",
  "Wyoming"
]);

const SECONDS = "ex";
const EXPIRATION_PERIOD = 600;

const SPAIN_DIVERSITY_AGE_RANGE_COUNT = 21;

@Service()
@RpcService()
export class InstitutionsResourceService
  extends RpcResourceService
  implements InstitutionsResource
{
  private readonly logger = createLogger(this);
  private institutionsIndexName: string;
  private peopleIndexName: string;
  private patientLevelIndexName: string;
  private redisClient;

  constructor(
    config: ConfigService,
    private elasticSearchService: ElasticSearchInstitutionsService,
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient,
    private institutionTagResourceClient: InstitutionTagResourceClient,
    private institutionClaimsQueryBuilder: InstitutionClaimsQueryBuilder,
    private queryParserService: QueryParserService,
    private claimsResponseAdapter: InstitutionsClaimsResponseAdapterService,
    private featureFlagsService: FeatureFlagsService,
    private rulesParserService: RulesParserService,
    private patientDiversityService: PatientDiversityStatsService,
    private indicationsTreeSearchService: IndicationsTreeSearchService,
    private institutionsSearchResponseAdapterService: InstitutionsSearchResponseAdapterService,
    private institutionNameSearchResourceService: InstitutionNameSearchResourceService,
    private ccsrIcdMappingRepository: CcsrIcdMappingRepository,
    private ccsrPxMappingRepository: CcsrPxMappingRepository,
    private claimCodeService: ClaimCodeService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_INSTITUTIONS,
      config.searchRedisOptions
    );

    this.institutionsIndexName = config.elasticInstitutionsIndex;
    this.peopleIndexName = config.elasticPeopleIndex;
    this.patientLevelIndexName = config.elasticPatientLevelIndex;
    this.redisClient = new Redis(config.searchCacheRedisOptions);
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteCountries")
  async autocompleteCountries(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionLocationFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      "country",
      _.omit(input, "filters.countries"),
      "country_level_regions"
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteRegions")
  async autocompleteRegions(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionLocationFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      "region",
      _.omit(input, "filters.regions"),
      "state_level_regions"
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteCities")
  async autocompleteCities(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionLocationFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      "city",
      _.omit(input, "filters.cities"),
      "city_level_regions"
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompletePostalCodes")
  async autocompletePostalCodes(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionLocationFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      "postal_code",
      _.omit(input, "filters.postalCodes")
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteAdminLevel1")
  async autocompleteAdminLevel1(
    input: string,
    country: string
  ): Promise<InstitutionLocationFilterAggregation[]> {
    return this.executeAdminLevelAutocompleteQuery(
      input,
      HeatMapZoomLevelFields.Level1Geography,
      country
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteAdminLevel2")
  async autocompleteAdminLevel2(
    input: string,
    country: string
  ): Promise<InstitutionLocationFilterAggregation[]> {
    return this.executeAdminLevelAutocompleteQuery(
      input,
      HeatMapZoomLevelFields.Level2Geography,
      country
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteTrialsSponsor")
  async autocompleteTrialsSponsorType(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.getTrialsAutocompleteOptions(
      input,
      "filters.trialsFilters.sponsorType",
      "trials.sponsor_types"
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteTrialsSponsor")
  async autocompleteTrialsSponsor(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.getTrialsAutocompleteOptions(
      input,
      "filters.trialsFilters.sponsor",
      "trials.sponsors"
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteTrialsStatus")
  async autocompleteTrialsStatus(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.getTrialsAutocompleteOptions(
      input,
      "filters.trialsFilters.status",
      "trials.status"
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteTrialsPhase")
  async autocompleteTrialsPhase(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.getTrialsAutocompleteOptions(
      input,
      "filters.trialsFilters.phase",
      "trials.phase"
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteTrialsStudyType")
  async autocompleteTrialsStudyType(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.getTrialsAutocompleteOptions(
      input,
      "filters.trialsFilters.studyType",
      "trials.type"
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteTrialsId")
  async autocompleteTrialsId(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.getTrialsAutocompleteOptions(
      input,
      "filters.trialsFilters.id",
      "trials.id"
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteTrialsBiomarker")
  async autocompleteTrialsBiomarker(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.getTrialsAutocompleteOptions(
      input,
      "filters.trialsFilters.biomarker",
      "trials.biomarker"
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteOrgTypes")
  async autocompleteOrgTypes(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    this.logger.info(
      { field: "orgTypes ", input: JSON.stringify(input) },
      "direct institution autocomplete"
    );
    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);
    const featureFlags = await this.getFeatureFlagValues(input);

    const queries: Array<MSearchRequest> = [];

    for (const field in queryableOrgTypesFields) {
      const query = this.buildAutoCompleteQueryForOrgTypes(
        field,
        _.omit(input, "filters.orgTypes"),
        featureFlags,
        queryUnderstandingServiceResponse
      );
      queries.push(query);
    }

    const multiSearchRequest: estypes.MsearchRequest = {
      index: this.institutionsIndexName,
      searches: queries.flat()
    };

    this.logger.info(
      { query: JSON.stringify(multiSearchRequest, null, "") },
      "elasticsearch multi search request for org types autocomplete"
    );

    const { took, responses }: estypes.MsearchResponse =
      await this.elasticSearchService.msearch(multiSearchRequest);

    this.logger.info({ took: took }, "execution time");

    const aggs = _.map(responses, "aggregations");
    const buckets = _.flatten(
      aggs.map((val) => {
        return [
          ...this.docCountBucketsToAggregations(
            val[`${queryableOrgTypesFields.orgTypes}`]
          ),
          ...this.docCountBucketsToAggregations(
            val[`${queryableOrgTypesFields.orgTypesLevel2}`]
          ),
          ...this.docCountBucketsToAggregations(
            val[`${queryableOrgTypesFields.orgTypesLevel3}`]
          )
        ];
      })
    );

    const orgTypes = _.orderBy(buckets, "count", "desc");
    return _.take(orgTypes, input.count);
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteDiagnoses")
  async autocompleteDiagnoses(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.invokeBuildClaimsAutocompleteQuery(input, "diagnoses");
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteProcedures")
  async autocompleteProcedures(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.invokeBuildClaimsAutocompleteQuery(input, "procedures");
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteCcsr")
  async autocompleteCcsr(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.invokeBuildClaimsAutocompleteQuery(input, "ccsr");
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteCcsrPx")
  async autocompleteCcsrPx(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.invokeBuildClaimsAutocompleteQuery(input, "ccsr_px");
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteCcsrDiagnoses")
  async autocompleteCcsrDiagnoses(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.invokeBuildCcsrClaimsAutocompleteQuery(input, "diagnoses");
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteCcsrProcedures")
  async autocompleteCcsrProcedures(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.invokeBuildCcsrClaimsAutocompleteQuery(input, "procedures");
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompleteGenericNames")
  async autocompleteGenericNames(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.invokeBuildClaimsAutocompleteQuery(input, "prescriptions");
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompletePatientAgeRange")
  async autocompletePatientAgeRange(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.invokeBuildPatientDiversityAutocompleteQuery(input, "ageRange");
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompletePatientRace")
  async autocompletePatientRace(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.invokeBuildPatientDiversityAutocompleteQuery(input, "race");
  }

  @RpcMethod()
  @Trace("h1-search.institutions.autocompletePatientSex")
  async autocompletePatientSex(
    input: InstitutionsAutocompleteInput
  ): Promise<InstitutionFilterAggregation[]> {
    return this.invokeBuildPatientDiversityAutocompleteQuery(input, "sex");
  }

  @RpcMethod()
  @Trace("h1-search.institutions.searchInstitutions")
  async search(
    input: Readonly<InstitutionsSearchInput>
  ): Promise<InstitutionsResponse> {
    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);
    const featureFlags = await this.getFeatureFlagValues(input);
    const {
      data,
      peopleData,
      sharedPeopleFromAffiliationCounts,
      isQueryAnInstitution,
      keywordIcdCodeSynonyms,
      indicationIcdCodeSynonyms,
      indicationSynonyms,
      resolvedIndications,
      peopleDataForIE,
      matchedPatientCountMap,
      matchedClaimsForInstitutionsMap,
      patientDiversityDistributionMap,
      trialsOngoingCountMap,
      trialsActivelyRecruitingCountMap,
      totalInstitutionsInArea
    } = (await this.searchDirectly(
      input,
      "keyword",
      featureFlags,
      "iol",
      queryUnderstandingServiceResponse
    )) as SearchDirectlyResponse;

    return this.institutionsSearchResponseAdapterService.adaptToInstitutionSearchResponse(
      input,
      data,
      peopleData,
      isQueryAnInstitution,
      featureFlags,
      keywordIcdCodeSynonyms ?? [],
      indicationSynonyms ?? [],
      resolvedIndications ?? [],
      indicationIcdCodeSynonyms ?? [],
      totalInstitutionsInArea,
      queryUnderstandingServiceResponse,
      peopleDataForIE,
      matchedPatientCountMap,
      matchedClaimsForInstitutionsMap,
      patientDiversityDistributionMap,
      trialsOngoingCountMap,
      trialsActivelyRecruitingCountMap,
      sharedPeopleFromAffiliationCounts
    );
  }

  private async patientCountV2(
    input: Readonly<InstitutionsSearchInput>,
    featureFlags: InstitutionSearchFeatureFlags
  ) {
    const rules = this.rulesParserService.parseRulesToEsQueries(
      input.filters,
      featureFlags
    );

    const { count } = await this.elasticSearchService.count({
      index: this.patientLevelIndexName,
      query: {
        bool: {
          filter: rules
        }
      }
    });

    this.logger.info({ data: rules, count }, "patient count query");
    return count;
  }

  @RpcMethod()
  @Trace("h1-search.institutions.patientCount")
  async patientCount(
    input: Readonly<InstitutionsSearchInput>
  ): Promise<number> {
    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);
    const featureFlags = await this.getFeatureFlagValues(input);

    if (featureFlags.usePatientIndexForCount) {
      return this.patientCountV2(input, featureFlags);
    }

    const count = (await this.searchDirectly(
      input,
      "keyword",
      featureFlags,
      "count",
      queryUnderstandingServiceResponse
    )) as number;
    return count;
  }

  @RpcMethod()
  @Trace("h1-search.institutions.searchInstitutions")
  async bulkInstitutionSearch(
    input: Readonly<InstitutionsSearchInput>
  ): Promise<Array<Entity>> {
    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);
    const featureFlags = await this.getFeatureFlagValues(input);
    const { data } = (await this.searchDirectly(
      input,
      "bulk",
      featureFlags,
      "iol",
      queryUnderstandingServiceResponse
    )) as SearchDirectlyResponse;
    return data.hits.hits.filter(hasSource).map((hit) => {
      return {
        entityType: UserEntities.EntityType.INSTITUTION,
        entityId:
          hit._source!.masterOrganizationId?.toString() ??
          hit._source!.id.toString(),
        entityName: hit._source!.name
      };
    });
  }

  @RpcMethod()
  @Trace("h1-search.institutions.searchTrialsByInstitution")
  async searchTrialsByInstitution(
    input: TrialsSearchByInstitutionInput
  ): Promise<TrialsSearchByInstitutionResponse> {
    this.logger.debug(
      { input: JSON.stringify(input) },
      "search trials of an institution"
    );

    const { institutionId, h1dnInstitutionId } = input;
    const emptyResponse = {
      institutionId,
      h1dnInstitutionId,
      matchedTrialIds: [],
      matchedCtmsTrialIds: [],
      totalMatchedTrials: 0,
      // TODO: Currently when no hits are returned we cannot populate the total fields (trials and person), hence its zero.
      // We should change this behaviour of this endpoint.
      // One approach is to fire a second query that will always bring total trials and total persons of an institute.
      totalTrials: 0,
      totalMatchedPersons: 0,
      totalPersons: 0,
      matchedTrials: []
    };

    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);

    const request = await this.buildTrialsSearchByInstitutionRequest(
      input,
      queryUnderstandingServiceResponse
    );

    this.logger.info(
      { query: JSON.stringify(request) },
      "elasticsearch request"
    );

    const data = await this.elasticSearchService.query<{
      id: string;
      masterOrganizationId?: number;
      trials_count: number;
      person_count: number;
    }>(request);

    this.logger.debug(
      { took: data.took, total: data.hits.total },
      "execution time"
    );

    if (this.extractTotalValue(data.hits.total ?? 0) < 1) {
      return emptyResponse;
    }

    const trialIds: string[] = [];
    const ctmsTrialIds: string[] = [];
    const trialStatus: string[] = [];
    const matchedTrials: MatchedInstitutionTrials[] = [];

    let totalMatchedTrials = 0;
    const totalMatchedPersonsInTrials = new Set<number>();
    // create a list of trial ids from the elastic search response.
    if (data.hits.hits[0].inner_hits) {
      totalMatchedTrials = this.extractTotalValue(
        data.hits.hits[0].inner_hits.trials.hits.total ?? 0
      );
      for (const hit of data.hits.hits[0].inner_hits.trials.hits.hits) {
        // add the trial id to the list of of matching trial ids
        const source = _.get(hit, "fields['trials.source']", []);
        const trialId = _.get(hit, "fields['trials.id']", []);

        if (!_.isEmpty(source)) {
          if (source[0] === "ctms") {
            ctmsTrialIds.push(trialId[0]);
          } else {
            trialIds.push(trialId[0]);
          }
        } else {
          trialIds.push(trialId[0]);
        }

        // add trial status to get ongoing trials count.
        if (hit.fields && !_.isEmpty(hit.fields["trials.status"])) {
          trialStatus.push(hit.fields["trials.status"][0]);
        }
        // add HCPs involved in the matching trials to the set of unique HCPs
        if (hit.fields && !_.isEmpty(hit.fields["trials.person_ids"])) {
          hit.fields["trials.person_ids"].forEach((e: any) =>
            totalMatchedPersonsInTrials.add(e)
          );
        }
        // **
        // add affiliation_type for institution trials docs
        if (hit.fields && !_.isEmpty(hit.fields["trials.affiliation_type"])) {
          matchedTrials.push({
            trialId: trialId[0],
            affiliationType: hit.fields["trials.affiliation_type"][0]
          });
        }
      }
    }

    return {
      institutionId,
      h1dnInstitutionId,
      matchedTrialIds: trialIds,
      matchedCtmsTrialIds: ctmsTrialIds,
      totalMatchedTrials: totalMatchedTrials,
      totalTrials: data.hits.hits[0]._source!.trials_count,
      totalMatchedPersons: totalMatchedPersonsInTrials.size,
      totalPersons: data.hits.hits[0]._source!.person_count,
      ongoingTrialsCount: this.extractOngoingTrialsCount(trialStatus),
      matchedTrials
    };
  }

  @RpcMethod()
  @Trace("h1-search.institutions.searchClaimsInInstitution")
  async searchClaimsForInstitute(
    input: Readonly<IolClaimsInput>
  ): Promise<IolClaimsResponse> {
    const queryParserResponse =
      await this.queryParserService.parseQueryWithQueryUnderstandingService(
        input.filters?.keywords?.join(" OR "),
        {
          projectSupportsAdvancedOperators: true //This is default behaviour in institutions claims search as OR operator is used
        }
      );

    const featureFlags = await this.getFeatureFlagValues(input);
    this.logger.info({ input }, "institution claim search");
    const {
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures
    } = featureFlags;
    const options: InstitutionClaimsQueryBuilderOptions = {
      shouldUseUniquePatientCount:
        enableUniquePatientCountForClaims &&
        (input.showUniquePatients ?? false),
      disableUniquePatientCountForOnlyProcedures:
        disableUniquePatientCountForOnlyProcedures
    };
    const request: estypes.SearchRequest =
      this.institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
        input,
        queryParserResponse?.parsedQueryTree,
        queryParserResponse?.queryUnderstandingServiceResponse,
        options
      );

    this.logger.info({ query: request }, "elasticsearch request");

    const data = await this.elasticSearchService.query(request);

    this.logger.info(
      { took: data.took, total: data.hits.total },
      "execution time"
    );

    return this.claimsResponseAdapter.adaptToInstitutionClaimsSearchResponse(
      input,
      data,
      options
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.getTaggedInstitutions")
  async getTaggedInstitutions(
    input: Readonly<TaggedInstitutionsSearchInput>
  ): Promise<TaggedInstitutionsSearchResponse> {
    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);
    const featureFlags = await this.getFeatureFlagValues(input);
    const inputToBuildRequest: InstitutionsSearchInput = {
      ...input,
      accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
      paging: {
        limit: input.limit ?? DEFAULT_TAGGED_INSTITUTION_SEARCH_SIZE,
        offset: input.offset ?? 0
      }
    };
    const filtersToFindTaggedInstitutionsWithGeoData =
      this.buildFiltersToGetTaggedInstitutionsWithGeoLocationData(input);

    // Force the use of the tagIds field
    // This should be removed once we enable tags in search for all projects
    const featureFlagsToBuildRequest: InstitutionSearchFeatureFlags = {
      ...featureFlags,
      enableTagsInElasticsearch: true
    };

    let resolvedIndication: string[] = [];
    if (
      inputToBuildRequest.query &&
      !_.isEmpty(inputToBuildRequest.filters?.trialEnrollmentRate)
    ) {
      resolvedIndication = await this.resolveInputQueryToIndications(
        inputToBuildRequest
      );
    }

    const { request, isQueryAnInstitution } = await this.buildSearchRequest(
      inputToBuildRequest,
      inputToBuildRequest,
      "keyword",
      featureFlagsToBuildRequest,
      "iol",
      filtersToFindTaggedInstitutionsWithGeoData,
      resolvedIndication,
      queryUnderstandingServiceResponse
    );

    if (!request) {
      return {
        total: 0,
        institutions: []
      };
    }

    request.aggs = undefined;
    request._source = SOURCE_INCLUDES_FOR_TAGGED_INSTITUTIONS_SEARCH;
    request.track_total_hits = undefined;

    this.logger.info(
      { query: request },
      "elasticsearch request to get tagged institutions"
    );

    const elasticsearchResponse =
      await this.elasticSearchService.query<ElasticsearchTaggedInstitutionDoc>(
        request
      );

    if (
      this.extractTotalValue(elasticsearchResponse.hits.total ?? 0) == 0 &&
      !isQueryAnInstitution
    ) {
      return await this.tryNameSearchToGetTaggedInstitutions(
        input,
        inputToBuildRequest,
        featureFlagsToBuildRequest,
        resolvedIndication,
        queryUnderstandingServiceResponse
      );
    }

    this.logger.info(
      `elasticsearch request to get tagged institutions took ${elasticsearchResponse.took} ms`
    );

    return this.institutionsSearchResponseAdapterService.adaptToTaggedInstitutionsSearchResponse(
      elasticsearchResponse
    );
  }

  @RpcMethod()
  @Trace("h1-search.institutions.diversity.stats")
  async institutionDiversityStats(
    input: InstitutionDiversityStatsInput
  ): Promise<PatientDiversityStatsResponse> {
    const { docId: iolId, patientsDiversityRatio } =
      await this.getInstitutionDocIdAndPatientDiversityRatio(
        input.masterOrganizationId
      );
    return this.patientDiversityService.getPatientDiversityStats(
      input,
      iolId!,
      this.institutionsIndexName,
      patientsDiversityRatio
    );
  }

  private async tryNameSearchToGetTaggedInstitutions(
    inputToGetTaggedInstitutions: Readonly<TaggedInstitutionsSearchInput>,
    inputToBuildRequest: Readonly<InstitutionsSearchInput>,
    featureFlags: Readonly<InstitutionSearchFeatureFlags>,
    resolvedIndications: string[],
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
  ) {
    const isBulkSearch = false;
    const filtersToFindTaggedInstitutionsWithGeoData =
      this.buildFiltersToGetTaggedInstitutionsWithGeoLocationData(
        inputToGetTaggedInstitutions
      );
    const request = await this.buildNameSearchRequest(
      inputToBuildRequest,
      featureFlags,
      isBulkSearch,
      "iol",
      filtersToFindTaggedInstitutionsWithGeoData,
      resolvedIndications,
      queryUnderstandingServiceResponse
    );

    if (!request) {
      return {
        total: 0,
        institutions: []
      };
    }

    request.aggs = undefined;
    request._source = SOURCE_INCLUDES_FOR_TAGGED_INSTITUTIONS_SEARCH;
    request.track_total_hits = undefined;

    this.logger.info(
      { query: request },
      "elasticsearch request to get tagged institutions"
    );

    const elasticsearchResponse =
      await this.elasticSearchService.query<ElasticsearchTaggedInstitutionDoc>(
        request
      );

    this.logger.info(
      `elasticsearch request to get tagged institutions took ${elasticsearchResponse.took} ms`
    );

    return this.institutionsSearchResponseAdapterService.adaptToTaggedInstitutionsSearchResponse(
      elasticsearchResponse
    );
  }

  private buildFiltersToGetTaggedInstitutionsWithGeoLocationData(
    input: TaggedInstitutionsSearchInput
  ) {
    const filters: QueryDslQueryContainer[] = [
      buildFieldExistsQuery("location")
    ];

    if (input.tagIds?.length) {
      filters.push(this.buildTagIdFilter(input.tagIds));
    } else {
      filters.push(buildFieldExistsQuery("tagIds"));
    }

    return filters;
  }

  private async getInstitutionDocId(masterOrganizationId: string) {
    const query: QueryDslQueryContainer = {
      term: {
        masterOrganizationId
      }
    };

    const request: estypes.SearchRequest = {
      index: this.institutionsIndexName,
      query,
      _source: false
    };

    const data = await this.elasticSearchService.query<{
      id: string;
      masterOrganizationId?: number;
      trials_count: number;
      person_count: number;
    }>(request);

    if (data.hits.hits.length) {
      return data.hits.hits[0]._id;
    }

    return undefined;
  }

  private async getInstitutionDocIdAndPatientDiversityRatio(
    masterOrganizationId: string
  ) {
    const query: QueryDslQueryContainer = {
      term: {
        masterOrganizationId
      }
    };

    const request: estypes.SearchRequest = {
      index: this.institutionsIndexName,
      query,
      _source: ["patientsDiversityRatio"]
    };

    const data = await this.elasticSearchService.query<{
      id: string;
      masterOrganizationId?: number;
      patientsDiversityRatio?: PatientsDiversityRaceInfo;
    }>(request);

    if (data.hits.hits.length) {
      return {
        docId: data.hits.hits[0]._id,
        patientsDiversityRatio:
          data.hits.hits[0]._source?.patientsDiversityRatio
      };
    }

    return {
      docId: undefined,
      patientsDiversityRatio: undefined
    };
  }

  async aggregatePatientClaimsForInstitution(
    input: Readonly<InstitutionsSearchInput>,
    featureFlags: Readonly<InstitutionSearchFeatureFlags>
  ): Promise<number> {
    const masterOrganizationId = input.filters!.iolIds![0];
    let docId = masterOrganizationId;

    if (featureFlags.useDocIdForParentPatientClaims) {
      const parentDocId = await this.getInstitutionDocId(masterOrganizationId);

      if (!parentDocId) {
        this.logger.info(
          { masterOrganizationId },
          "masterOrganizationId has no matching document"
        );
        return 0;
      }
      docId = parentDocId!;
    }

    const query = {
      bool: {
        filter: [
          {
            term: {
              _routing: docId
            }
          } as QueryDslQueryContainer
        ]
      }
    };

    const filtersToApply: QueryDslQueryContainer[] = [];

    if (input.filters?.indications && input.filters.indications.length) {
      input.filters.indications.forEach((indication: string) => {
        filtersToApply.push({
          term: {
            ["patientClaims.diagnosisIndications"]: indication
          }
        });
      });
    }

    if (
      input.filters?.claimsFilters?.diagnosesICD &&
      input.filters.claimsFilters.diagnosesICD.length
    ) {
      input.filters.claimsFilters.diagnosesICD
        .map(extractClaimsCode)
        .forEach((icdCode: string) => {
          filtersToApply.push({
            term: {
              ["patientClaims.diagnosisIcdCode"]: icdCode
            }
          });
        });
    }
    query.bool.filter.push(...filtersToApply);
    const request: estypes.SearchRequest = {
      index: this.institutionsIndexName,
      query
    };

    this.logger.info({ query: request }, "Elasticsearch request");

    try {
      const data = await this.elasticSearchService.count(request);
      this.logger.info({ count: data.count }, "Matched Patients");

      return data.count;
    } catch (error) {
      this.logger.error({ error }, "Failed to query Elasticsearch");
      throw error;
    }
  }

  private extractOngoingTrialsCount(trialStatus: string[]) {
    const completedTrialsStatus = [
      "Completed",
      "Terminated",
      "Withdrawn",
      "Unknown status"
    ];
    return trialStatus.filter(
      (status) => !!status && !completedTrialsStatus.includes(status)
    ).length;
  }

  private async executeAutocompleteQuery(
    field: keyof typeof queryableFields,
    input: InstitutionsAutocompleteInput,
    regionField?: keyof typeof queryableRegionFields
  ): Promise<InstitutionFilterAggregation[]> {
    let queryUnderstandingServiceResponse;
    if (!input.searchType) {
      queryUnderstandingServiceResponse =
        await this.retrieveQueryUnderstandingServiceResponse(input);
    }

    let resolvedIndication: string[] = [];
    if (input.query && !_.isEmpty(input.filters?.trialEnrollmentRate)) {
      resolvedIndication = await this.resolveInputQueryToIndications(input);
    }

    return this.autocomplete(
      field,
      input,
      resolvedIndication,
      queryUnderstandingServiceResponse,
      regionField
    );
  }

  private async executeAdminLevelAutocompleteQuery(
    input: string,
    zoomLevel: HeatMapZoomLevelFields,
    country: string
  ): Promise<InstitutionLocationFilterAggregation[]> {
    return this.autocompleteAdminLevelLocation(zoomLevel, country, input);
  }

  private buildProjectIdFilter(projectId: string): QueryDslQueryContainer {
    return {
      bool: {
        should: [
          {
            bool: {
              must: [
                buildFieldExistsQuery(PROJECT_IDS),
                buildTermsQuery(PROJECT_IDS, [projectId])
              ]
            }
          },
          {
            bool: {
              must_not: [buildFieldExistsQuery(PROJECT_IDS)]
            }
          }
        ]
      }
    };
  }

  private buildInstitutionH1dnIdFilter(
    h1dnInstitutionIds: string[]
  ): QueryDslQueryContainer {
    return {
      bool: {
        should: [
          buildTermsQuery("id", h1dnInstitutionIds),
          buildTermsQuery("groupH1dnOrganizationId", h1dnInstitutionIds)
        ],
        minimum_should_match: 1
      }
    };
  }

  private buildTagIdFilter(tagIds: string[]): QueryDslQueryContainer {
    return buildTermsQuery("tagIds", tagIds);
  }

  private async buildTagFilterQuery(
    tags: string[],
    intersectTags: boolean,
    featureFlags: Readonly<InstitutionSearchFeatureFlags>
  ): Promise<QueryDslQueryContainer | null> {
    let filterQuery: QueryDslQueryContainer | null = null;
    if (featureFlags.enableTagsInElasticsearch) {
      filterQuery = this.buildTagIdFilter(tags);
    } else {
      const entities =
        await this.institutionTagResourceClient.getActiveInstitutionTagAssignmentsForTags(
          tags!
        );

      let institutionIds: string[] = [];
      if (intersectTags && tags.length > 1) {
        const institutionIdToTagIdMap = new Map<string, string[]>();
        entities.forEach((entity) => {
          if (entity?.entityId) {
            const tagIds = institutionIdToTagIdMap.get(entity.entityId) ?? [];
            tagIds.push(entity.tagId);
            institutionIdToTagIdMap.set(entity.entityId, tagIds);
          }
        });
        institutionIdToTagIdMap.forEach(
          (tagIds: string[], institutionId: string) => {
            if (tagIds.length === tags.length) {
              institutionIds.push(institutionId);
            }
          }
        );
      } else {
        institutionIds = _.compact(entities.map((obj) => obj.entityId));
      }

      this.logger.info(
        {
          tags,
          entityLength: entities.length,
          institutionIdLength: institutionIds.length
        },
        "Institution tag filter info"
      );

      if (institutionIds.length > 0) {
        filterQuery = buildInstitutionIdFilter(institutionIds);
      } else {
        // this is to handle saved searches with deleted tags
        return null;
      }
    }
    return filterQuery;
  }

  private async buildFiltersForTags(
    input: InstitutionsSearchInput,
    request: estypes.SearchRequest,
    isDiversitySort: boolean,
    featureFlags: Readonly<InstitutionSearchFeatureFlags>
  ): Promise<estypes.SearchRequest | null> {
    const inclusionTags = input.filters!.tags;
    const exclusionTags = input.filters!.excludeTags;
    let inclusionTagsFilterQuery: QueryDslQueryContainer | null = null;
    if (inclusionTags) {
      inclusionTagsFilterQuery = await this.buildTagFilterQuery(
        inclusionTags,
        input.filters?.intersectTags ?? false,
        featureFlags
      );
      // handle saved searches with deleted inclusion tags.
      if (inclusionTagsFilterQuery === null) {
        return null;
      }
    }

    let exclusionTagsFilterQuery: QueryDslQueryContainer | null = null;
    if (exclusionTags) {
      exclusionTagsFilterQuery = await this.buildTagFilterQuery(
        exclusionTags,
        false,
        featureFlags
      );
      // handle saved searches with deleted exclusion tags.
      if (exclusionTagsFilterQuery === null) {
        return null;
      }
    }

    const filterQuery: QueryDslQueryContainer = {
      bool: {
        filter: inclusionTagsFilterQuery ?? undefined,
        must_not: exclusionTagsFilterQuery ?? undefined
      }
    };

    //Need to ensure that we understand the structure of the request before adding the filter
    //Diversity sort and non-diversity sort requests have different structures
    if (
      isDiversitySort &&
      request.query?.function_score?.query?.function_score &&
      Array.isArray(
        request.query!.function_score!.query!.function_score!.query!.bool!
          .filter!
      )
    ) {
      request.query!.function_score!.query!.function_score!.query!.bool!.filter!.push(
        filterQuery
      );
      return request;
    }

    if (
      request.query!.function_score &&
      Array.isArray(request.query!.function_score!.query!.bool!.filter!)
    ) {
      request.query!.function_score!.query!.bool!.filter!.push(filterQuery);
    } else if (
      request.query!.bool &&
      Array.isArray(request.query!.bool!.filter!)
    ) {
      request.query!.bool!.filter!.push(filterQuery);
    } else {
      request.query!.function_score! = {
        query: {
          bool: {
            filter: [filterQuery]
          }
        }
      };
    }

    return request;
  }

  private buildRequestWithRegionFilterForClaims(
    request: estypes.SearchRequest,
    featureFlags: Readonly<InstitutionSearchFeatureFlags>
  ): estypes.SearchRequest {
    const claimsRegionFilter =
      buildClaimsRegionFilterForInstitutionsSearch(featureFlags);

    if (claimsRegionFilter) {
      return buildFiltersForClaims([claimsRegionFilter], request, "must_not");
    }

    return request;
  }

  shouldAddTimeFrameRangeFilter = (
    claimFilters: ClaimFiltersInput,
    path: ClaimType
  ): boolean => {
    const diagnosesProperties: (keyof ClaimFiltersInput)[] = [
      "diagnosesICDMinCount",
      "diagnosesICDMaxCount"
    ];
    const proceduresProperties: (keyof ClaimFiltersInput)[] = [
      "proceduresCPTMinCount",
      "proceduresCPTMaxCount"
    ];

    if (
      path === "diagnoses" &&
      _.some(diagnosesProperties, (prop) => !_.isNil(claimFilters[prop]))
    ) {
      return true;
    }

    if (
      path === "procedures" &&
      _.some(proceduresProperties, (prop) => !_.isNil(claimFilters[prop]))
    ) {
      return true;
    }

    return false;
  };

  private buildElasticsearchFiltersFromTrialsFilters(
    trialsFilters: InstitutionTrialsFiltersInput
  ): QueryDslQueryContainer[] {
    this.logger.info(trialsFilters);

    const esFilters: QueryDslQueryContainer[] = [];

    if (!_.isEmpty(trialsFilters.status)) {
      esFilters.push(buildTermsQuery("trials.status", trialsFilters.status!));
    }

    if (!_.isEmpty(trialsFilters.conditions)) {
      esFilters.push(
        buildTermsQuery("trials.conditions", trialsFilters.conditions!)
      );
    }

    if (!_.isEmpty(trialsFilters.phase)) {
      esFilters.push(buildTermsQuery("trials.phase", trialsFilters.phase!));
    }

    if (!_.isEmpty(trialsFilters.sponsor)) {
      esFilters.push(
        buildTermsQuery("trials.sponsors", trialsFilters.sponsor!)
      );
    }

    if (!_.isEmpty(trialsFilters.sponsorType)) {
      esFilters.push(
        buildTermsQuery("trials.sponsor_types", trialsFilters.sponsorType!)
      );
    }

    if (!_.isEmpty(trialsFilters.id)) {
      esFilters.push(buildTermsQuery("trials.id", trialsFilters.id!));
    }

    if (!_.isEmpty(trialsFilters.studyType)) {
      esFilters.push(buildTermsQuery("trials.type", trialsFilters.studyType!));
    }

    if (trialsFilters.startDateTimeFrame) {
      esFilters.push({
        range: {
          "trials.start_date": {
            gte: trialsFilters.startDateTimeFrame!.min,
            lte: trialsFilters.startDateTimeFrame!.max
          }
        }
      });
    }

    if (trialsFilters.dateRange) {
      const trialsStartDate = {
        gte: trialsFilters.dateRange!.min,
        format: "epoch_second"
      };
      esFilters.push({
        range: {
          "trials.start_date": trialsStartDate
        }
      });

      if (trialsFilters.dateRange!.max) {
        const trialsEndDate = {
          lte: trialsFilters.dateRange!.max,
          format: "epoch_second"
        };
        esFilters.push({
          range: {
            "trials.completion_date": trialsEndDate
          }
        });
      }
    }

    if (trialsFilters.biomarkers) {
      esFilters.push(
        buildTermsQuery("trials.biomarker", trialsFilters.biomarkers!)
      );
    }
    return esFilters;
  }

  /**
   * Builds an elastic search request that ranks the results based on the sum of total trials, procedures and diagnoses of an institution.
   * If input query is present then we only search it within the institution name.
   * @param input user input
   * @param featureFlags bag of feature flags
   * @returns elasticsearch request container
   */
  private buildSearchRequestToRankByTotalFields(
    input: InstitutionsSearchInput,
    featureFlags: InstitutionSearchFeatureFlags,
    isBulkSearch: boolean,
    extraFilters: ReadonlyArray<QueryDslQueryContainer>,
    resolvedIndications: string[]
  ): estypes.SearchRequest {
    const filters = buildElasticsearchFiltersFromInputFilters(
      input,
      featureFlags,
      extraFilters,
      resolvedIndications
    );
    const isDiversitySort = input.sortBy === InstitutionSortOptions.DIVERSITY;
    const isTrialPerformanceSort =
      input.sortBy === InstitutionSortOptions.TRIAL_PERFORMANCE;
    const [isAgeSort, ageRangesSelected, ageSortWeight] =
      getAgeSortValues(input);

    const countriesToFilterOut =
      getCountriesToFilterOutForInstitutionsSearch(featureFlags);
    const {
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures
    } = featureFlags;
    const uniquePatientToggleFromInput =
      input.filters?.claimsFilters?.showUniquePatients ?? false;
    const claimsTotalCountFieldForDiagnoses =
      !disableUniquePatientCountForOnlyProcedures &&
      enableUniquePatientCountForClaims &&
      uniquePatientToggleFromInput
        ? "diagnosesUniqueCount"
        : "diagnosis_count";
    const claimsTotalCountFieldForProcedures =
      !disableUniquePatientCountForOnlyProcedures &&
      enableUniquePatientCountForClaims &&
      uniquePatientToggleFromInput
        ? "proceduresUniqueCount"
        : "procedure_count";
    const query: QueryDslQueryContainer = {
      function_score: {
        boost_mode: "replace",
        score_mode: "sum",
        query: {
          bool: {
            filter: filters
          }
        },
        functions: isTrialPerformanceSort
          ? SCORING_FUNCTIONS_FOR_TRIAL_PERFORMANCE_RANKING
          : [
              this.buildScoreFunctionForClaimsAsset(
                countriesToFilterOut,
                claimsTotalCountFieldForDiagnoses
              ),
              this.buildScoreFunctionForClaimsAsset(
                countriesToFilterOut,
                claimsTotalCountFieldForProcedures
              ),
              this.buildScoreFunctionForClaimsAsset(
                countriesToFilterOut,
                "num_prescriptions"
              ),
              {
                field_value_factor: {
                  field: "trials_count",
                  modifier: "log1p",
                  missing: 0
                }
              },
              {
                field_value_factor: {
                  field: "publication_count",
                  modifier: "log1p",
                  missing: 0
                }
              }
            ]
      }
    };

    const diversityWeight = buildDiversityRaceRankingScript(
      input.filters?.diversityFilters?.race
    );
    const isRaceFilterPresent = input.filters?.diversityFilters?.race?.length;
    const diversityScriptNoRace = `doc['patientsDiversityRatio.whiteNonHispanic'].size() == 0 ? 0 : _score * ${diversityWeight}`;
    const trialsSaturationScript =
      "(doc['trials_count'].size() != 0 ? saturation(doc['trials_count'].value, params.trials_saturation) : 0)";

    let diversityRankingScript = `(doc['${claimsTotalCountFieldForDiagnoses}'].size() != 0 ? saturation(doc['${claimsTotalCountFieldForDiagnoses}'].value, params.diagnoses_saturation) : 0) \
    + (doc['${claimsTotalCountFieldForProcedures}'].size() != 0 ? saturation(doc['${claimsTotalCountFieldForProcedures}'].value, params.procedures_saturation) : 0) \
    + (doc['num_prescriptions'].size() != 0 ? saturation(doc['num_prescriptions'].value, params.prescriptions_saturation) : 0) \
    + ${trialsSaturationScript}`;

    if (countriesToFilterOut.length) {
      diversityRankingScript = `${SCRIPT_IS_LOCATED_IN_EXCLUDED_CLAIMS_REGION} ? ${trialsSaturationScript} : ${diversityRankingScript}`;
    }

    const queryForDiversityRanking: QueryDslQueryContainer = {
      function_score: {
        boost_mode: "replace",
        query: {
          function_score: {
            boost_mode: "replace",
            score_mode: "sum",
            query: {
              bool: {
                filter: filters
              }
            },
            functions: [
              {
                script_score: {
                  script: {
                    source: diversityRankingScript,
                    params: {
                      countriesToFilterOut,
                      diagnoses_saturation: DIAGNOSES_SATURATION,
                      procedures_saturation: PROCEDURES_SATURATION,
                      trials_saturation: TRIALS_SATURATION,
                      prescriptions_saturation: PRESCRIPTIONS_SATURATION
                    }
                  }
                }
              }
            ]
          }
        },
        functions: [
          {
            script_score: {
              script: {
                source: isRaceFilterPresent
                  ? `_score * ${diversityWeight}`
                  : diversityScriptNoRace
              }
            }
          }
        ]
      }
    };

    let finalQuerySelectedBasedOnSort: QueryDslQueryContainer = query;
    if (isDiversitySort) {
      finalQuerySelectedBasedOnSort = queryForDiversityRanking;
    } else if (isEuropeanDiversitySort(input.sortBy)) {
      const europeanRankingQuery = getEuropeanDiversityRankingQuery(
        input.sortBy,
        input.filters?.diversityFilters?.race
      );

      if (europeanRankingQuery) {
        const queryForDiversityRanking: QueryDslQueryContainer = {
          bool: {
            filter: filters,
            must: [europeanRankingQuery]
          }
        };
        finalQuerySelectedBasedOnSort = queryForDiversityRanking;
      }
    } else if (isAgeSort) {
      finalQuerySelectedBasedOnSort =
        buildAgeSortQueryForNameSearchOrNoQueryRequest(
          ageRangesSelected,
          ageSortWeight,
          filters
        );
    }
    let request: estypes.SearchRequest = {
      index: this.institutionsIndexName,
      track_total_hits: true,
      _source: {
        include: !isBulkSearch
          ? SOURCE_INCLUDES
          : SOURCE_INCLUDES_FOR_BULK_ENTITY_SEARCH
      },
      query: finalQuerySelectedBasedOnSort
    };

    if (input.exportsSelections?.matchedClaimsDetails) {
      const shoulds: QueryDslQueryContainer[] = [];
      shoulds.push(
        ...toNestedMatchAllClaimsQuery(
          featureFlags,
          input.sortBy,
          input.filters?.claimsFilters?.showUniquePatients
        )
      );
      if (isDiversitySort) {
        request.query!.function_score!.query!.function_score!.query!.bool!.should =
          shoulds;
      } else if (isEuropeanDiversitySort(input.sortBy)) {
        request.query!.bool!.should = shoulds;
      } else {
        request.query!.function_score!.query!.bool!.should = shoulds;
      }
    }

    if (input.query) {
      request = this.addNameSearchQueryToRequest(
        input.query!,
        request,
        isDiversitySort,
        this.codesOrTimeFrameSpecified(input.filters?.claimsFilters)
      );
    }

    if (input.paging?.limit != null && input.paging.limit >= 0) {
      request.size = input.paging.limit;
    }
    if (input.paging?.offset) {
      request.from = input.paging.offset;
    }

    return request;
  }

  private buildScoreFunctionForClaimsAsset(
    countriesToFilterOut: string[],
    field:
      | "diagnosis_count"
      | "procedure_count"
      | "diagnosesUniqueCount"
      | "proceduresUniqueCount"
      | "num_prescriptions"
  ) {
    let scoreFunction: QueryDslFunctionScoreContainer = {
      field_value_factor: {
        field,
        modifier: "log1p",
        missing: 0
      }
    };

    if (countriesToFilterOut.length) {
      const fieldValueWithDefault = `(doc['${field}'].size() == 0 ? 0 : doc['${field}'].value)`;
      const log1pFieldValueFactor = `Math.log10(${fieldValueWithDefault} + 1)`;

      scoreFunction = {
        script_score: {
          script: {
            source: `${SCRIPT_IS_LOCATED_IN_EXCLUDED_CLAIMS_REGION} ? 0 : ${log1pFieldValueFactor}`,
            params: {
              countriesToFilterOut
            }
          }
        }
      };
    }

    return scoreFunction;
  }

  /**
   * Builds an elastic search request that ranks the results based on the matched diagnoses for an institution.
   * If input query is present then we only search it within the institution name.
   * @param input user input
   * @param featureFlags bag of feature flags
   * @returns elasticsearch request container
   */
  private buildSearchRequestWithoutRankByTotal(
    input: InstitutionsSearchInput,
    featureFlags: InstitutionSearchFeatureFlags,
    isBulkSearch: boolean,
    extraFilters: ReadonlyArray<QueryDslQueryContainer>,
    resolvedIndication: string[],
    icdCodesForCareClusterSelected?: string[]
  ): estypes.SearchRequest {
    const filters = buildElasticsearchFiltersFromInputFilters(
      input,
      featureFlags,
      extraFilters,
      resolvedIndication
    );

    const isDiversitySort = input.sortBy === InstitutionSortOptions.DIVERSITY;
    const isTrialPerformanceSort =
      input.sortBy === InstitutionSortOptions.TRIAL_PERFORMANCE;
    const [isAgeSort, ageRangesSelected, ageSortWeight] =
      getAgeSortValues(input);

    const query: QueryDslQueryContainer = {
      function_score: {
        boost_mode: "replace",
        score_mode: "sum",
        query: {
          bool: {
            must: isAgeSort
              ? [buildAgeSortQuery(ageRangesSelected, ageSortWeight)]
              : undefined,
            filter: filters
          }
        },
        functions: isTrialPerformanceSort
          ? SCORING_FUNCTIONS_FOR_TRIAL_PERFORMANCE_RANKING
          : []
      }
    };

    let request: estypes.SearchRequest = {
      index: this.institutionsIndexName,
      track_total_hits: true,
      _source: {
        include: !isBulkSearch
          ? SOURCE_INCLUDES
          : SOURCE_INCLUDES_FOR_BULK_ENTITY_SEARCH
      },
      query: query
    };

    if (input.exportsSelections?.matchedClaimsDetails) {
      const shoulds: QueryDslQueryContainer[] = [];
      const diagnosesICDFilterApplied =
        input.filters?.claimsFilters?.diagnosesICD;
      let diagnosesCodesToCheck: string[] = [];
      if (
        diagnosesICDFilterApplied?.length ||
        !!icdCodesForCareClusterSelected?.length
      ) {
        diagnosesCodesToCheck = (
          diagnosesICDFilterApplied?.map(extractClaimsCode) ?? []
        ).concat(icdCodesForCareClusterSelected ?? []);
      }
      if (diagnosesCodesToCheck.length) {
        let diagnosesPath: nestedPath = "diagnoses";
        let diagnosesCountField = internalCountFieldForNestedClaimsQuery(
          input.filters!.claimsFilters!,
          "diagnoses",
          featureFlags
        );

        let diagnosesFields = ["description_eng", "code_eng"];

        if (isEuropeanDiversitySort(input.sortBy)) {
          const europeanDiagnosesFields = getEuropeanDiagnosesFields(
            input.sortBy
          );
          if (europeanDiagnosesFields) {
            diagnosesPath = europeanDiagnosesFields.diagnosesPath;
            diagnosesCountField = europeanDiagnosesFields.diagnosesCountField;
            diagnosesFields = ["code", "description"];
          }
        }

        const diagnosesDetailsQueryArguments: QueryBuilderArguments = {
          path: diagnosesPath,
          fields: diagnosesFields,
          searchQuery: diagnosesCodesToCheck.join("|"),
          innerHits: {
            name: "diagnoses_collection",
            _source: true,
            docvalue_fields: [diagnosesCountField],
            sort: [
              {
                [diagnosesCountField]: {
                  order: "desc"
                }
              }
            ],
            size: 5
          },
          analyzer: CLAIMS_DESCRIPTION_ANALYZER
        };
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            diagnosesDetailsQueryArguments,
            featureFlags
          )
        );
      }
      const proceduresCountField = internalCountFieldForNestedClaimsQuery(
        input.filters!.claimsFilters!,
        "procedures",
        featureFlags
      );
      const proceduresCPTFilterApplied =
        input.filters?.claimsFilters?.proceduresCPT;
      if (proceduresCPTFilterApplied?.length) {
        const procedureDetailsQueryArguments: QueryBuilderArguments = {
          path: "procedures",
          fields: ["description_eng", "code_eng"],
          searchQuery: proceduresCPTFilterApplied!
            .map(extractClaimsCode)
            .join("|"),
          innerHits: {
            name: "procedures_collection",
            _source: true,
            docvalue_fields: [proceduresCountField],
            sort: [
              {
                [proceduresCountField]: {
                  order: "desc"
                }
              }
            ],
            size: 5
          },
          analyzer: CLAIMS_DESCRIPTION_ANALYZER
        };
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            procedureDetailsQueryArguments,
            featureFlags
          )
        );
      }
      if (
        !diagnosesCodesToCheck?.length &&
        !proceduresCPTFilterApplied?.length
      ) {
        shoulds.push(
          ...toNestedMatchAllClaimsQuery(featureFlags, input.sortBy)
        );
      }
      request.query!.function_score!.query!.bool!.should = shoulds;
    }

    if (input.query) {
      request = this.addNameSearchQueryToRequest(
        input.query!,
        request,
        isDiversitySort,
        this.codesOrTimeFrameSpecified(input.filters?.claimsFilters)
      );
    }

    if (input.paging?.limit != null && input.paging.limit >= 0) {
      request.size = input.paging.limit;
    }
    if (input.paging?.offset) {
      request.from = input.paging.offset;
    }

    return request;
  }

  private addNameSearchQueryToRequest(
    query: string,
    request: SearchRequest,
    isDiversitySort: boolean,
    isClaimsMatchedCount: boolean
  ) {
    const requestWithNameQuery = _.cloneDeep(request);
    const nameSearchQuery: QueryDslQueryContainer = {
      dis_max: {
        queries: [
          {
            match: {
              name: {
                query,
                operator: "AND",
                fuzziness: FUZZINESS,
                analyzer: PUNCTUATION_SPLITTER_ANALYZER,
                prefix_length: 1
              }
            }
          },
          {
            match: {
              name: {
                query,
                operator: "AND",
                fuzziness: FUZZINESS,
                analyzer: PUNCTUATION_REMOVER_ANALYZER,
                prefix_length: 1
              }
            }
          }
        ]
      }
    };
    if (isDiversitySort && !isClaimsMatchedCount) {
      requestWithNameQuery.query!.function_score!.query!.function_score!.query!.bool!.must =
        [nameSearchQuery];
    } else {
      requestWithNameQuery.query!.function_score!.query!.bool!.must = [
        nameSearchQuery
      ];
    }

    requestWithNameQuery.query!.function_score!.functions!.push({
      filter: {
        dis_max: {
          queries: [
            {
              match: {
                name: {
                  query,
                  operator: "AND",
                  analyzer: PUNCTUATION_SPLITTER_ANALYZER
                }
              }
            },
            {
              match: {
                name: {
                  query,
                  operator: "AND",
                  analyzer: PUNCTUATION_REMOVER_ANALYZER
                }
              }
            }
          ]
        }
      },
      weight: INSTITUTION_NAME_FIELD_HIT_BOOST
    });
    return requestWithNameQuery;
  }

  private addPrefixQueryToRequest(
    request: estypes.SearchRequest,
    featureFlags: InstitutionSearchFeatureFlags,
    prefixQuery: QueryDslQueryContainer
  ): estypes.SearchRequest {
    const { enablePenalizeAutoCreatedIOLs } = featureFlags;

    if (enablePenalizeAutoCreatedIOLs) {
      request.query!.function_score!.query!.bool!.must = [prefixQuery];
    } else {
      request.query!.bool!.must = [prefixQuery];
    }

    return request;
  }

  /**
   * Builds elastic search request to obtain trial ids for a given institution satisfying optional filters and query.
   * Trials ids returned are sorted by decreasing order of start date.
   * @param input user input must contain the institution id for which trials are requested, may optionally contain other filters
   * @returns elastic search request
   */
  private async buildTrialsSearchByInstitutionRequest(
    input: TrialsSearchByInstitutionInput,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
  ): Promise<estypes.SearchRequest> {
    const indicationsValues = input.indications;
    let indicationsParsedQuery;

    if (indicationsValues?.length) {
      [indicationsParsedQuery] =
        await this.getIndicationSynonymsFromQueryUnderstandingService(
          indicationsValues,
          true
        );
    }
    // trial ids are requested for this institution id
    const musts: QueryDslQueryContainer[] = [];

    if (input.projectId) {
      musts.push(this.buildProjectIdFilter(input.projectId));
    }

    if (input.h1dnInstitutionId) {
      musts.push(this.buildInstitutionH1dnIdFilter([input.h1dnInstitutionId]));
    } else {
      musts.push(buildInstitutionIdFilter([input.institutionId]));
    }

    let trialsNestedQuery: QueryDslQueryContainer;

    if (input.query || input.trialsFilters || indicationsValues?.length) {
      const trialsFilters = input.trialsFilters
        ? this.buildElasticsearchFiltersFromTrialsFilters(input.trialsFilters)
        : undefined;

      const searchQuery =
        getSynonymizedQuery(input.query, queryUnderstandingServiceResponse) ??
        EMPTY_STRING;
      const queryIndicationSeparator = searchQuery.length ? "|" : EMPTY_STRING;
      const searchQueryWithIndications = searchQuery.concat(
        indicationsParsedQuery
          ? queryIndicationSeparator + indicationsParsedQuery
          : EMPTY_STRING
      );
      this.logger.debug(
        { searchQueryWithIndications },
        "This is the synonymized search query with indications"
      );

      trialsNestedQuery = {
        bool: {
          must: searchQueryWithIndications.length
            ? {
                simple_query_string: {
                  query: searchQueryWithIndications,
                  fields: ["trials.trials_info"],
                  default_operator: "and"
                }
              }
            : undefined,
          filter: trialsFilters
        }
      };
    } else {
      // if there are no filters or search query then match all
      trialsNestedQuery = {
        match_all: {}
      };
    }

    const trialsQueryBody: QueryDslNestedQuery = {
      path: "trials",
      query: trialsNestedQuery,
      inner_hits: {
        _source: false,
        docvalue_fields: [
          "trials.id",
          "trials.person_ids",
          "trials.status",
          "trials.source",
          "trials.affiliation_type"
        ],
        from: input.paging?.offset || 0,
        size: input.paging?.limit || 10000,
        sort: [
          {
            "trials.start_date": {
              order: "desc"
            }
          }
        ]
      }
    };

    musts.push({ nested: trialsQueryBody });

    const request: estypes.SearchRequest = {
      index: this.institutionsIndexName,
      _source: {
        include: ["id", MASTER_ORGANIZATION_ID, "trials_count", "person_count"]
      },
      query: {
        bool: {
          must: musts
        }
      }
    };
    return request;
  }

  /**
   * Returns the top trials filter autcomplete options
   * @param input user input
   * @param filter key
   * @param trialsPathKey key
   * @returns trials aggregation buckets
   */
  private async getTrialsAutocompleteOptions(
    input: InstitutionsAutocompleteInput,
    filterKey: TrialsFilterKeys,
    trialsPathKey: TrialsESPathKeys
  ): Promise<InstitutionFilterAggregation[]> {
    this.logger.info(
      { field: trialsPathKey, input: JSON.stringify(input) },
      "direct institution autocomplete"
    );

    const featureFlags = await this.getFeatureFlagValues(input);

    let resolvedIndication: string[] = [];
    if (input.query && !_.isEmpty(input.filters?.trialEnrollmentRate)) {
      resolvedIndication = await this.resolveInputQueryToIndications(input);
    }

    // build trials filter autocomplete query after removing the trials filter if its applied
    const request = await this.buildTrialsFilterAutocompleteQuery(
      _.omit(input, filterKey),
      featureFlags,
      trialsPathKey,
      resolvedIndication
    );

    this.logger.info(
      { query: JSON.stringify(request) },
      "elasticsearch request"
    );

    const data = await this.elasticSearchService.query<never>(request);

    this.logger.info(
      { took: data.took, total: data.hits.total },
      "execution time"
    );

    const trialsOptionsAggregates = data.aggregations![trialsPathKey] as any;
    const buckets = trialsOptionsAggregates["filter_options"]
      .top_options as AggregationsTermsAggregateBase<NestedDocCountBucket>;
    return this.nestedDocCountBucketsToAggregations(buckets);
  }

  /**
   * Builds an elasticsearch request to obtain top options and the number of trials that match the user
   * type ahead for autocomplete and satisfy the search query and other optional filters applied.
   * If the user type ahead is empty then just returns top 5 options.
   * @param input user input
   * @param featureFlags bag of feature flags
   * @param key key to use for aggregation
   * @returns elasticsearch request to obtain top options matching the user type ahead
   */
  private async buildTrialsFilterAutocompleteQuery(
    input: InstitutionsAutocompleteInput,
    featureFlags: InstitutionSearchFeatureFlags,
    key: TrialsESPathKeys,
    resolvedIndication: string[]
  ): Promise<estypes.SearchRequest> {
    // build search request to take care of all other filters and user query
    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);
    const extraFilters: QueryDslQueryContainer[] = [];
    const request = this.buildSearchRequestForTrialsSortAndFilter(
      input,
      input,
      featureFlags,
      false,
      extraFilters,
      resolvedIndication,
      queryUnderstandingServiceResponse
    );
    // source isn't needed for an autocomplete query
    request._source = false;
    // set results returned to 0 for autocomplete query
    request.size = 0;
    request.track_total_hits = false;

    // set the aggregations section in search request
    request.aggs = {
      [key]: {
        nested: {
          path: "trials"
        }
      }
    };

    let trialESFilters: QueryDslQueryContainer[] = [];

    if (input?.filters?.trialsFilters) {
      trialESFilters = this.buildElasticsearchFiltersFromTrialsFilters(
        input.filters.trialsFilters
      );
    }

    const indicationsValues = input.filters?.indications;
    let indicationsParsedQuery;

    if (indicationsValues?.length) {
      [indicationsParsedQuery] =
        await this.getIndicationSynonymsFromQueryUnderstandingService(
          indicationsValues,
          true
        );
    }

    if (input?.query?.length || indicationsParsedQuery) {
      const searchQuery =
        getSynonymizedQuery(input.query, queryUnderstandingServiceResponse) ??
        EMPTY_STRING;
      const queryIndicationSeparator = input?.query?.length
        ? "|"
        : EMPTY_STRING;
      const searchQueryWithIndications = searchQuery.concat(
        indicationsParsedQuery
          ? queryIndicationSeparator + indicationsParsedQuery
          : EMPTY_STRING
      );

      trialESFilters.push({
        simple_query_string: toSimpleQueryString({
          path: "trials",
          fields: ["trials_info"],
          searchQuery: searchQueryWithIndications
        })
      });
    }

    request.aggs[key].aggs = {
      filter_options: {
        filter: {
          bool: {
            must: trialESFilters
          }
        },
        aggs: {
          top_options: {
            terms: {
              field: key,
              size: input.count
            },
            aggs: {
              institutions: {
                reverse_nested: {}
              }
            }
          }
        }
      }
    };

    if (input.prefix) {
      // generate regular expression to match the start of a word in a phrase ignoring the case
      const regularExpressionToMatchStartOfWord: string =
        generateRegularExpressionForAutocompleteMatching(input.prefix);

      // output only those aggregated buckets that match the regular expression
      request.aggs[key].aggs!.filter_options.aggs!.top_options.terms!.include =
        regularExpressionToMatchStartOfWord;
    }

    return request;
  }

  private getClaimsAutocompleteQueryField(path: ClaimType): string {
    let field = "codeAndDescription_eng";

    switch (path) {
      case "prescriptions":
        field = "generic_name";
        break;
      case "ccsr":
        field = "description_eng";
        break;
      case "ccsr_px":
        field = "description_eng";
        break;
      default:
        field = "codeAndDescription_eng";
        break;
    }

    return field;
  }

  private getClaimsAutocompletePathFilterField(path: ClaimType): string {
    let claimCode;

    switch (path) {
      case "diagnoses":
        claimCode = "diagnosesICD";
        break;
      case "procedures":
        claimCode = "proceduresCPT";
        break;
      case "prescriptions":
        claimCode = "genericNames";
        break;
      case "ccsr":
        claimCode = "ccsr";
        break;
      case "ccsr_px":
        claimCode = "ccsr_px";
        break;
      default:
        this.logger.error({ path }, "Invalid claim type");
        throw new Error("Invalid claim type");
    }

    return claimCode;
  }

  private getModifiedInputAfterRemovingCurrentPathsClaimsFilter(
    input: InstitutionsAutocompleteInput,
    path: ClaimType
  ): InstitutionsAutocompleteInput {
    const claimsCode = this.getClaimsAutocompletePathFilterField(path);

    //we remove current path's claims filter as we don't want the filter to interfer with autocomplete results
    input = _.omit(input, `filters.claimsFilters.${claimsCode}`);

    //special treatments:-
    //1. For ccsr and diagnoses, we remove both filters as they act as OR with each other
    //2. For ccsr_px and procedures, we remove both filters as they act as OR with each other
    switch (path) {
      //if path is diagnoses, we remove ccsr filter and vice versa
      case "diagnoses":
        input = _.omit(input, `filters.claimsFilters.ccsr`);
        break;
      case "ccsr":
        input = _.omit(input, `filters.claimsFilters.diagnosesICD`);
        break;

      //if path is procedures, we remove ccsr_px filter and vice versa
      case "procedures":
        input = _.omit(input, `filters.claimsFilters.ccsr_px`);
        break;
      case "ccsr_px":
        input = _.omit(input, `filters.claimsFilters.proceduresCPT`);
        break;
      default:
        break;
    }

    return input;
  }

  private getClaimsAutocompleteAggregationPath(path: ClaimType): string {
    const field = this.getClaimsAutocompleteQueryField(path);

    let aggregationPath = `${path}.${field}`;

    //special treatment to ccsr and ccsr_px because the `description_eng` field is Text field in the index.
    if (path === "ccsr" || path === "ccsr_px") {
      aggregationPath = `${path}.${field}.keyword`;
    }

    return aggregationPath;
  }

  private async invokeBuildClaimsAutocompleteQuery(
    input: InstitutionsAutocompleteInput,
    path: ClaimType
  ): Promise<InstitutionFilterAggregation[]> {
    const field = this.getClaimsAutocompleteQueryField(path);

    this.logger.info(
      { field: `${path}.${field}`, input: JSON.stringify(input) },
      "direct institution autocomplete"
    );

    const aggregationPath = this.getClaimsAutocompleteAggregationPath(path);

    const inputToPassToBuildClaimsFilters =
      this.getModifiedInputAfterRemovingCurrentPathsClaimsFilter(input, path);

    const request = await this.buildClaimsAutocompleteQuery(
      inputToPassToBuildClaimsFilters as InstitutionsAutocompleteInput,
      path,
      aggregationPath
    );

    this.logger.info({ query: request }, "elasticsearch request");

    const data = await this.elasticSearchService.query<never>(request);

    this.logger.info(
      { took: data.took, total: data.hits.total },
      "execution time"
    );

    const claimsCodeAggregates = data.aggregations![aggregationPath] as any;

    const buckets = (
      input.prefix
        ? claimsCodeAggregates["filter_code_and_description_eng"]
            .top_code_and_description_eng
        : claimsCodeAggregates.top_code_and_description_eng
    ) as AggregationsTermsAggregateBase<DocCountBucket>;
    const aggregations = this.docCountBucketsToAggregations(buckets);

    const ccsrDescriptions = await this.extractCcsrForIcdOrCpt(
      aggregations,
      path
    );
    const aggregationsWithCcsrDesc = aggregations.map((bucket) => ({
      id: bucket.id,
      count: bucket.count,
      ccsrDescriptions: ccsrDescriptions.get(
        extractClaimsCode(bucket.id.toLowerCase())
      ),
      ccsrIcdSize: bucket.ccsrIcdSize,
      ccsrSize: bucket.ccsrSize
    }));
    return aggregationsWithCcsrDesc;
  }

  private async extractCcsrForIcdOrCpt(
    aggregations: InstitutionFilterAggregation[],
    path: ClaimType
  ): Promise<Map<string, string[]>> {
    const ccsrMap = new Map<string, string[]>();

    if (path === "diagnoses") {
      const icds = aggregations.map((bucket) => extractClaimsCode(bucket.id));

      if (icds.length) {
        const ccsrDesc = await this.ccsrIcdMappingRepository.getCcsrForIcd(
          icds.map(toLower)
        );
        if (ccsrDesc) {
          for (const { icdCode, ccsr } of ccsrDesc) {
            const icdCodeList = ccsrMap.get(icdCode) ?? [];
            icdCodeList.push(ccsr);
            ccsrMap.set(icdCode, icdCodeList);
          }
        }
      }
    }

    if (path === "procedures") {
      const cpts = aggregations.map((bucket) => extractClaimsCode(bucket.id));
      if (cpts.length) {
        const ccsrDesc =
          await this.ccsrPxMappingRepository.getCcsrForProcedureCodesWithSize(
            cpts.map(toLower)
          );
        if (ccsrDesc) {
          for (const { procedureCode, ccsr } of ccsrDesc) {
            const cptCodeList = ccsrMap.get(procedureCode) ?? [];
            cptCodeList.push(ccsr);
            ccsrMap.set(procedureCode, cptCodeList);
          }
        }
      }
    }

    return ccsrMap;
  }

  private async invokeBuildCcsrClaimsAutocompleteQuery(
    input: InstitutionsAutocompleteInput,
    path: ClaimType
  ): Promise<InstitutionFilterAggregation[]> {
    const field = this.getClaimsAutocompleteQueryField(path);

    this.logger.info(
      { field: `${field}`, input: JSON.stringify(input) },
      "direct institution autocomplete"
    );

    const inputToPassToBuildClaimsFilters =
      this.getModifiedInputAfterRemovingCurrentPathsClaimsFilter(input, path);

    const aggregationPath = this.getClaimsAutocompleteAggregationPath(path);
    // build claims autocomplete query after removing the claims filter if it's applied
    const { request, ccsrCodes } = await this.buildCcsrClaimsAutocompleteQuery(
      inputToPassToBuildClaimsFilters as InstitutionsAutocompleteInput,
      path,
      aggregationPath
    );

    this.logger.info({ query: request }, "elasticsearch request");

    const data = await this.elasticSearchService.query<never>(request);

    this.logger.info(
      { took: data.took, total: data.hits.total },
      "execution time"
    );

    const claimsCodeAggregates = data.aggregations![aggregationPath] as any;

    const ccsrCodeBuckets =
      claimsCodeAggregates["filter_code_and_description_eng"]?.buckets;
    const ccsrCodesAggregations: InstitutionFilterAggregation[] = [];
    if (!!ccsrCodes.length && !!ccsrCodeBuckets) {
      ccsrCodes.forEach((ccsrCode) => {
        const bucket = ccsrCodeBuckets[ccsrCode];
        const count = bucket.doc_count;
        const descriptionExists = count > 0;
        const id = descriptionExists
          ? bucket.matching_desc?.buckets[0].key ?? ccsrCode
          : ccsrCode;
        ccsrCodesAggregations.push({
          id,
          count
        });
      });
    }

    return ccsrCodesAggregations;
  }

  private async buildBaseSearchRequestForClaimsAutocomplete(
    input: InstitutionsAutocompleteInput,
    institutionSearchFeatureFlags?: InstitutionSearchFeatureFlags
  ): Promise<estypes.SearchRequest> {
    const featureFlags =
      institutionSearchFeatureFlags ?? (await this.getFeatureFlagValues(input));
    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);
    const isQueryAnInstitution = this.isQueryAnInstitution(
      input,
      queryUnderstandingServiceResponse
    );
    const extraFilters: QueryDslQueryContainer[] = [];

    let resolvedIndication: string[] = [];
    if (input.query && !_.isEmpty(input.filters?.trialEnrollmentRate)) {
      resolvedIndication = await this.resolveInputQueryToIndications(input);
    }

    if (filtersPresent(input.filters?.trialsFilters)) {
      return this.buildSearchRequestForTrialsSortAndFilter(
        input,
        input,
        featureFlags,
        false,
        extraFilters,
        resolvedIndication,
        queryUnderstandingServiceResponse
      );
    } else if (!input.query || isQueryAnInstitution) {
      const institutionLevelFilters = buildElasticsearchFiltersFromInputFilters(
        input,
        featureFlags,
        extraFilters,
        resolvedIndication
      );
      const musts: QueryDslQueryContainer[] = [];

      if (input.query && isQueryAnInstitution) {
        musts.push({
          match_phrase: {
            name: input.query
          }
        });
      }

      return {
        index: this.institutionsIndexName,
        query: {
          function_score: {
            query: { bool: { filter: institutionLevelFilters, must: musts } }
          }
        }
      };
    } else {
      return this.buildKeywordSearchRequest(
        input,
        input,
        featureFlags,
        false,
        "iol",
        extraFilters,
        resolvedIndication,
        queryUnderstandingServiceResponse
      );
    }
  }

  private getClaimsAutocompleteTermAggregation(
    input: InstitutionsAutocompleteInput,
    path: ClaimType,
    aggregationPath: string
  ) {
    switch (path) {
      case "ccsr":
        return {
          top_code_and_description_eng: {
            terms: {
              field: aggregationPath,
              size: input.count
            },
            aggs: {
              ccsr_size: {
                terms: {
                  field: "ccsr.icdSize"
                }
              }
            }
          }
        };
      case "ccsr_px":
        return {
          top_code_and_description_eng: {
            terms: {
              field: aggregationPath,
              size: input.count
            },
            aggs: {
              ccsr_size: {
                terms: {
                  field: "ccsr_px.cptSize"
                }
              }
            }
          }
        };
      default:
        return {
          top_code_and_description_eng: {
            terms: {
              field: aggregationPath,
              size: input.count
            }
          }
        };
    }
  }

  private async buildClaimsAutocompleteQuery(
    input: InstitutionsAutocompleteInput,
    path: ClaimType,
    aggregationPath: string
  ): Promise<estypes.SearchRequest> {
    // if filters are present include them in the autocomplete request
    const request = await this.addFiltersForAutocomplete(input, path);

    // source isn't needed for an autocomplete query
    request._source = false;

    // set results returned to 0 for autocomplete query
    request.size = 0;
    request.track_total_hits = false;

    const termAggregation = this.getClaimsAutocompleteTermAggregation(
      input,
      path,
      aggregationPath
    );

    // set the aggregations section in search request
    request.aggs = {
      [aggregationPath]: {
        nested: {
          path: path
        }
      }
    };

    if (input.prefix) {
      // generate regular expression to match the start of a word in a phrase ignoring the case
      const regularExpressionToMatchStartOfWord: string =
        generateRegularExpressionForAutocompleteMatching(input.prefix);
      // filter to obtain those documents for aggregations that match the regular expression in codeAndDescription_eng
      request.aggs[aggregationPath].aggs = {
        filter_code_and_description_eng: {
          filter: {
            regexp: {
              [aggregationPath]: {
                value: regularExpressionToMatchStartOfWord
              }
            }
          },
          aggs: termAggregation
        }
      };

      // output only those aggregated buckets that match the regular expression
      request.aggs[
        aggregationPath
      ].aggs!.filter_code_and_description_eng.aggs!.top_code_and_description_eng.terms!.include =
        regularExpressionToMatchStartOfWord;
    } else {
      request.aggs[aggregationPath].aggs = termAggregation;
    }
    return request;
  }

  private async buildCcsrClaimsAutocompleteQuery(
    input: InstitutionsAutocompleteInput,
    path: ClaimType,
    aggregationPath: string
  ): Promise<{ request: SearchRequest; ccsrCodes: string[] }> {
    // if filters are present include them in the autocomplete request
    const request = await this.addFiltersForAutocomplete(input, path);
    const { ccsrToExpand, ccsrOffset, ccsrSize, ccsrIcdSize, ccsrIcdOffset } =
      input;

    //fall back to old params if new params are not provided
    const backwordCompatibleCcseOffset = !_.isNil(ccsrIcdOffset)
      ? ccsrIcdOffset
      : ccsrOffset;
    const backwordCompatibleCcseSize = !_.isNil(ccsrIcdSize)
      ? ccsrIcdSize
      : ccsrSize;

    const codesForCcsr = await this.fetchCcsrClaimCodesFromCacheOrDb(
      path,
      ccsrToExpand,
      backwordCompatibleCcseOffset,
      backwordCompatibleCcseSize
    );
    // source isn't needed for an autocomplete query
    request._source = false;

    // set results returned to 0 for autocomplete query
    request.size = 0;
    request.track_total_hits = false;

    // set the aggregations section in search request
    request.aggs = {
      [aggregationPath]: {
        nested: {
          path: path
        }
      }
    };
    const field = `${path}.code_eng.keyword`;
    if (codesForCcsr.length) {
      // generate regular expression to match the start of a word in a phrase ignoring the case
      const filtersAgg: Record<string, any> = codesForCcsr.reduce(
        (acc, code) => {
          acc[code] = {
            bool: {
              filter: [buildTermQuery(field, code)]
            }
          };
          return acc;
        },
        {} as Record<string, any>
      );

      request.aggs[aggregationPath].aggs = {
        filter_code_and_description_eng: {
          filters: {
            filters: filtersAgg
          },
          aggs: {
            matching_desc: {
              terms: {
                field: aggregationPath,
                size: backwordCompatibleCcseSize
              }
            }
          }
        }
      };
    } else {
      this.logger.error(
        { ccsrToExpand },
        "No cssr codes found for the ccsr selected"
      );
    }
    return { request: request, ccsrCodes: codesForCcsr };
  }

  private async getIcdCodeListForCcsrOrderedById(
    ccsrToExpand: string,
    ccsrIcdOffset: number,
    ccsrIcdSize: number
  ): Promise<string[]> {
    const icdCodeList = await this.ccsrIcdMappingRepository.getIcdCodesForCcsr([
      ccsrToExpand!
    ]);
    const finalIcdList = [];
    if (icdCodeList.length) {
      icdCodeList.sort(sortIcdCodesBySchemeAndAlphabet);
      const lengthToUse = _.min([
        (ccsrIcdOffset ?? 0) + (ccsrIcdSize ?? icdCodeList.length),
        icdCodeList.length
      ]);
      const listToUse = icdCodeList.slice(ccsrIcdOffset ?? 0, lengthToUse);
      for (const { icdCode } of listToUse) {
        finalIcdList.push(icdCode.toUpperCase());
      }
    }
    return finalIcdList;
  }

  private async getCptCodeListForCcsrPxOrderedById(
    ccsrToExpand: string,
    ccsrOffset: number,
    ccsrSize: number
  ): Promise<string[]> {
    const cptCodeList =
      await this.ccsrPxMappingRepository.getProcedureCodesForCcsr([
        ccsrToExpand!
      ]);
    const finalCptList: string[] = [];

    if (cptCodeList.length) {
      cptCodeList.sort(sortProcedureCodesByAlphabet); //NOTE: YASH we might need to change this sort function later
      const lengthToUse = _.min([
        (ccsrOffset ?? 0) + (ccsrSize ?? cptCodeList.length),
        cptCodeList.length
      ]);
      const listToUse = cptCodeList.slice(ccsrOffset ?? 0, lengthToUse);
      for (const { procedureCode } of listToUse) {
        finalCptList.push(procedureCode.toUpperCase());
      }
    }

    return finalCptList;
  }

  private async getFromCache(
    path: ClaimType,
    ccsrToExpand: string,
    ccsrOffset: number,
    ccsrSize: number
  ): Promise<string[] | null> {
    const key = sha1({ path, ccsrToExpand, ccsrOffset, ccsrSize });
    const raw = await this.redisClient.get(key);
    if (raw) {
      const entities = JSON.parse(raw) as string[];
      this.logger.debug(entities, "ccsr code cache hit");
      return entities;
    }
    this.logger.debug(null, "ccsr code cache miss");
    return null;
  }

  private async addToCache(
    path: ClaimType,
    ccsrToExpand: string,
    ccsrOffset: number,
    ccsrSize: number,
    results: Readonly<string[]>
  ) {
    const key = sha1({ path, ccsrToExpand, ccsrOffset, ccsrSize });
    return this.redisClient.set(
      key,
      JSON.stringify(results),
      SECONDS,
      EXPIRATION_PERIOD
    );
  }

  private async fetchCcsrClaimCodesFromCacheOrDb(
    path: ClaimType,
    ccsrToExpand?: string,
    ccsrOffset?: number,
    ccsrSize?: number
  ): Promise<string[]> {
    if (
      _.isUndefined(ccsrToExpand) ||
      _.isUndefined(ccsrOffset) ||
      _.isUndefined(ccsrSize)
    ) {
      return [];
    }

    try {
      const entities = await this.getFromCache(
        path,
        ccsrToExpand!,
        ccsrOffset!,
        ccsrSize
      );
      if (entities) return entities;
    } catch (err) {
      this.logger.error(err, "Error connecting redis for ccsr claim codes");
    }

    let codesToIncludeInAutocomplete: string[] = [];

    switch (path) {
      case "diagnoses":
        codesToIncludeInAutocomplete =
          await this.getIcdCodeListForCcsrOrderedById(
            ccsrToExpand,
            ccsrOffset,
            ccsrSize
          );
        break;

      case "procedures":
        codesToIncludeInAutocomplete =
          await this.getCptCodeListForCcsrPxOrderedById(
            ccsrToExpand,
            ccsrOffset,
            ccsrSize
          );
        break;
      default:
        break;
    }
    if (codesToIncludeInAutocomplete.length > 1) {
      try {
        await this.addToCache(
          path,
          ccsrToExpand,
          ccsrOffset,
          ccsrSize,
          codesToIncludeInAutocomplete
        );
      } catch (err) {
        this.logger.error(err, "Error connecting redis for ccsr claim codes");
      }
    }
    return codesToIncludeInAutocomplete;
  }

  private async buildPatientDiversityAutocompleteQuery(
    autocompleteField: string,
    input: InstitutionsAutocompleteInput,
    autocompleteType: FilterAutocompleteType
  ): Promise<estypes.SearchRequest> {
    // if filters are present include them in the autocomplete request
    const request = await this.addFiltersForAutocomplete(
      input,
      autocompleteType
    );

    // source isn't needed for an autocomplete query
    request._source = false;

    // set results returned to 0 for autocomplete query
    request.size = 0;
    request.track_total_hits = false;

    const diversityFieldAggregation = {
      [`${autocompleteField}`]: {
        terms: {
          field: autocompleteField,
          size: input.count
        }
      }
    };

    // set the aggregations section in search request
    request.aggs = diversityFieldAggregation;

    if (input.prefix) {
      // generate regular expression to match the start of a word in a phrase ignoring the case
      const regularExpressionToMatchStartOfWord: string =
        generateRegularExpressionForAutocompleteMatching(input.prefix);

      // filter to obtain those documents for aggregations that match the regular expression in codeAndDescription_eng
      request.aggs = {
        filtered_diversity_field: {
          filter: {
            regexp: {
              [`${autocompleteField}`]: {
                value: regularExpressionToMatchStartOfWord
              }
            }
          },
          aggs: diversityFieldAggregation
        }
      };

      // output only those aggregated buckets that match the regular expression
      request.aggs!.filtered_diversity_field.aggs![
        `${autocompleteField}`
      ].terms!.include = regularExpressionToMatchStartOfWord;
    }
    return request;
  }

  private async addFiltersForAutocomplete(
    input: InstitutionsAutocompleteInput,
    autocompleteType: FilterAutocompleteType
  ): Promise<estypes.SearchRequest> {
    const featureFlags = await this.getFeatureFlagValues(input);
    let request = await this.buildBaseSearchRequestForClaimsAutocomplete(
      input,
      featureFlags
    );

    // include claims filters in search request if filters present
    // set skipInnerHits to true since we don't need inner_hits for autocomplete
    if (
      filtersPresent(_.omit(input.filters?.claimsFilters, "showUniquePatients"))
    ) {
      request = buildRequestForClaims(
        input,
        request,
        featureFlags,
        featureFlags.enableClaimsFilteringMatchedCountsUpdate
          ? SKIP_INNER_HITS
          : INCLUDE_INNER_HITS
      );
    }

    // include excluded claim filters in search request if filters present
    if (filtersPresent(input.filters?.exclusionClaims)) {
      request = buildRequestForExcludedClaims(
        input.filters!.exclusionClaims!,
        request,
        featureFlags,
        shouldExcludeHcpBasedOnInput(input, featureFlags)
      );
    }

    if (this.isClaimsAutocomplete(autocompleteType)) {
      return this.buildRequestWithRegionFilterForClaims(request, featureFlags);
    }

    return request;
  }

  private isClaimsAutocomplete(autocompleteType: FilterAutocompleteType) {
    return (
      autocompleteType === "diagnoses" || autocompleteType === "procedures"
    );
  }

  diversityAutocompleteField = (field: keyof SearchFieldMap) => {
    const searchFieldObj: SearchFieldMap = {
      sex: "patientsDiversitySex",
      ageRange: "patientsDiversityAgeRanges",
      race: "patientsDiversityRaces"
    };

    return searchFieldObj[field];
  };

  private async invokeBuildPatientDiversityAutocompleteQuery(
    input: InstitutionsAutocompleteInput,
    field: FilterAutocompleteType
  ): Promise<InstitutionFilterAggregation[]> {
    this.logger.info(
      { field: `${field}`, input: JSON.stringify(input) },
      "direct institution autocomplete"
    );

    const autocompleteField = this.diversityAutocompleteField(field);
    const request = await this.buildPatientDiversityAutocompleteQuery(
      autocompleteField,
      _.omit(input, `filters.diversityFilters.${field}`),
      field
    );

    this.logger.info(
      { query: JSON.stringify(request) },
      "elasticsearch request"
    );

    const data = await this.elasticSearchService.query<never>(request);

    this.logger.info(
      { took: data.took, total: data.hits.total },
      "execution time"
    );

    const diversityAggregates = data.aggregations as any;

    const buckets = (
      input.prefix
        ? diversityAggregates["filtered_diversity_field"][
            `${autocompleteField}`
          ]
        : diversityAggregates[`${autocompleteField}`]
    ) as AggregationsTermsAggregateBase<DocCountBucket>;
    return this.docCountBucketsToAggregations(buckets).filter(
      supportedRaceCategories
    );
  }

  private extractTotalValue(total: number | estypes.SearchTotalHits): number {
    if (typeof total === "number") {
      return total;
    }
    return total.value;
  }

  private docCountBucketsToAggregations(
    agg: AggregationsTermsAggregateBase<DocCountBucket> | undefined,
    isClaimUnderCcsr?: boolean
  ): InstitutionFilterAggregation[] {
    if (!agg || !agg.buckets) {
      return [];
    }

    return (agg.buckets as DocCountBucket[]).map((bucket) => {
      return {
        id: isClaimUnderCcsr
          ? bucket.matching_desc?.buckets[0].key ?? bucket.key
          : bucket.key,
        count: bucket.doc_count,
        ccsrIcdSize: bucket.ccsr_size?.buckets[0]?.key,
        ccsrSize: bucket.ccsr_size?.buckets[0]?.key
      };
    });
  }

  private docCountBucketsToLocationFilterAggregations(
    agg: AggregationsTermsAggregateBase<DocCountBucket> | undefined
  ): InstitutionLocationFilterAggregation[] {
    if (!agg || !agg.buckets) {
      return [];
    }

    return (agg.buckets as DocCountBucket[]).map((bucket) => {
      return {
        id: bucket.key,
        count: bucket.doc_count,
        label: bucket.key
      };
    });
  }

  private nestedDocCountBucketsToAggregations(
    agg: AggregationsTermsAggregateBase<NestedDocCountBucket> | undefined
  ): InstitutionFilterAggregation[] {
    if (!agg || !agg.buckets) {
      return [];
    }

    return _.orderBy(
      (agg.buckets as NestedDocCountBucket[]).map((bucket) => {
        return {
          id: bucket.key,
          count: bucket.institutions.doc_count
        };
      }),
      "count",
      "desc"
    );
  }

  private locationRegionsInDocCountBucketsToAggregations(
    agg:
      | AggregationsTermsAggregateBase<LocationRegionsInDocCountBucket>
      | undefined,
    field: keyof typeof queryableAddressFields
  ): InstitutionLocationFilterAggregation[] {
    if (!agg || !agg.buckets) {
      return [];
    }

    return (agg.buckets as LocationRegionsInDocCountBucket[]).map((bucket) => {
      return {
        id: bucket.key,
        count: bucket.doc_count,
        regionsIn: this.formatRegionsIn(bucket, field)
      };
    });
  }

  // Temp fix for data issue with incorrect regions
  private formatRegionsIn(
    bucket: LocationRegionsInDocCountBucket,
    field: keyof typeof queryableAddressFields
  ) {
    if (field === "country") {
      return _.map(bucket.regions_in.buckets, "key");
    }

    if (bucket.key.includes("|")) {
      const locationTokens = bucket.key.split("|");
      const countryCode = locationTokens[0];
      const regionCode = locationTokens[1];
      if (
        countryCode.toLowerCase() !== "us" ||
        !VALID_STATE_CODES.has(regionCode.toUpperCase())
      ) {
        return [];
      }
    } else if (!VALID_STATE_NAMES.has(bucket.key)) {
      return [];
    }

    return _.map(bucket.regions_in.buckets, "key");
  }

  private regionDocCountBucketsToAggregations(
    agg: AggregationsTermsAggregateBase<RegionsDocCountBucket> | undefined
  ): InstitutionLocationFilterAggregation[] {
    if (!agg || !agg.buckets) {
      return [];
    }

    return (agg.buckets as RegionsDocCountBucket[]).map((bucket) => {
      return {
        id: bucket.key,
        count: bucket.doc_count,
        locationsInRegion: this.formatLocationsInRegion(bucket)
      };
    });
  }

  private formatLocationsInRegion(bucket: RegionsDocCountBucket) {
    const validLocationsInRegion = REGION_TO_STATES.get(bucket.key);
    const validLocationsInRegionCodes = REGION_TO_STATE_CODES.get(bucket.key);
    const locationsInRegion = this.docCountBucketsToAggregations(
      bucket.locations_in_region as AggregationsTermsAggregateBase<DocCountBucket>
    );

    if (!locationsInRegion) {
      return undefined;
    }

    // Temp fix for data issue with duplicate locations
    const locationsInRegionMap = new Map<
      string,
      InstitutionFilterAggregation
    >();
    locationsInRegion.forEach((bucket) => {
      const existingBucket = locationsInRegionMap.get(bucket.id);
      if (existingBucket) {
        existingBucket.count += bucket.count;
      } else {
        locationsInRegionMap.set(bucket.id, bucket);
      }
    });

    const locationBuckets = Array.from(locationsInRegionMap.values());

    if (validLocationsInRegion && validLocationsInRegionCodes) {
      return locationBuckets.filter((bucket) => {
        if (bucket.id.includes("|")) {
          const stateCode = bucket.id.split("|")[1];
          return validLocationsInRegionCodes.includes(stateCode);
        }

        return validLocationsInRegion.includes(bucket.id);
      });
    }

    return locationBuckets;
  }

  private isQueryAnInstitution(
    input?: InstitutionsSearchInput,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
  ): boolean {
    if (input?.searchType) {
      return !(input.searchType === SearchTypes.KEYWORD);
    } else {
      const institutionScore =
        queryUnderstandingServiceResponse
          ?.getQueryIntent()
          ?.getInstitutionIntent()
          ?.getScore() || 0;
      return !!(
        queryUnderstandingServiceResponse &&
        queryUnderstandingServiceResponse.hasQueryIntent() &&
        institutionScore >= INSTITUTION_INTENT_THRESHOLD
      );
    }
  }

  /**
   * Decides whether to form institution search request to sort results by matching trials count based on two factors:
   * 1. user explicitly supplied sort by trials in the input
   * 2. user supplied a minimum number of matching trials count filter (in this case results are sorted by matching trials implicitly)
   * @param input user input
   * @returns true if search results should be sorted by institution's matching trials count
   */
  private shouldSortResultsByMatchingTrialsCount(
    input: InstitutionsSearchInput
  ): boolean {
    return input.sortBy === InstitutionSortOptions.TRIALS;
  }

  private inputHasTrialsFiltersOrSortOption(
    input: InstitutionsSearchInput
  ): boolean {
    return (
      input.filters?.trialsFilters !== undefined ||
      input.sortBy === InstitutionSortOptions.TRIALS
    );
  }

  private getPatientClauseWeightBasedOnSort(input: InstitutionsSearchInput) {
    if (
      input.sortBy === InstitutionSortOptions.PATIENT_COUNT ||
      input.sortBy === InstitutionSortOptions.DIVERSITY ||
      _.isEmpty(input.sortBy)
    ) {
      return PATIENT_POPULATION_WEIGHT;
    } else if (input.sortBy === InstitutionSortOptions.H1_DEFAULT) {
      return H1_DEFAULT_WEIGHT;
    } else {
      return 0;
    }
  }

  private patientClauseShouldAffectRanking(input: InstitutionsSearchInput) {
    return (
      input.sortBy === InstitutionSortOptions.PATIENT_COUNT ||
      input.sortBy === InstitutionSortOptions.DIVERSITY ||
      _.isEmpty(input.sortBy)
    );
  }

  private async retrieveQueryUnderstandingServiceResponse(
    input:
      | Readonly<TaggedInstitutionsSearchInput>
      | Readonly<InstitutionsSearchInput>
      | Readonly<TrialsSearchByInstitutionInput>
      | Readonly<IolClaimsInput>
  ): Promise<QueryUnderstandingServiceResponse | undefined> {
    let queryUnderstandingServiceResponse;
    let queryToAnalyze;

    if ("query" in input) {
      queryToAnalyze = input.query;
    } else if ("filters" in input && "keywords" in input.filters!) {
      queryToAnalyze = convertQueryToSimpleQueryStringSyntax(
        _.get(input, "filters.keywords", []).join(" OR ")
      );
    }

    if (queryToAnalyze) {
      try {
        queryUnderstandingServiceResponse =
          await this.queryUnderstandingServiceClient.analyze(
            queryToAnalyze,
            "eng"
          );

        this.logger.info(
          {
            query: queryToAnalyze,
            response: queryUnderstandingServiceResponse.toObject()
          },
          "query understanding service"
        );
      } catch (error) {
        this.logger.error(
          { input, error },
          "Could not retrieve query understanding"
        );
      }
    }

    return queryUnderstandingServiceResponse;
  }

  private buildPatientClaimsFilterForCountQuery(
    input: InstitutionsSearchInput
  ) {
    const claimsFilter = this.buildPatientClaimsFilter(input);
    // if (claimsFilter.length && !_.isEmpty(input.filters?.countries)) {
    //   claimsFilter.push({
    //     terms: {
    //       "patientClaims.country": input.filters!.countries!
    //     }
    //   });
    // }
    return claimsFilter;
  }

  private buildPatientClaimsFilter(input: InstitutionsSearchInput) {
    return this.rulesParserService.parseRulesToEsQueries(input.filters);
  }

  private async resolveInputQueryToIndications(
    input: InstitutionsSearchInput
  ): Promise<string[]> {
    if (input.query) {
      const indicationSearchInput: SearchIndicationsByQueryInput = {
        query: input.query,
        indicationType: [IndicationType.L3],
        size: 5,
        indicationSource: [IndicationSource.ALL],
        sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE,
        projectId: input.projectId
      };
      const indicationNodes =
        await this.indicationsTreeSearchService.searchIndicationsByQuery(
          indicationSearchInput
        );
      const resolvedIndications: string[] = indicationNodes?.map((n) =>
        n.indicationName.trim().toLowerCase()
      );
      if (resolvedIndications) {
        return resolvedIndications;
      }
    }

    return [];
  }

  async searchDirectly(
    originalInput: Readonly<InstitutionsSearchInput>,
    searchContext: SearchContext,
    featureFlags: InstitutionSearchFeatureFlags,
    requestType: RequestType,
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>
  ): Promise<SearchDirectlyResponse | number> {
    this.logger.info({ input: originalInput }, "institution search");
    const isBulkSearch = searchContext === "bulk";

    const input: InstitutionsSearchInput = _.cloneDeep(originalInput);
    const indications = await this.resolveInputQueryToIndications(input);

    if (
      _.isEmpty(input.filters?.indications) &&
      indications &&
      indications.length > 0 &&
      input.filters?.claimsFilters?.showUniquePatients === true &&
      input.query &&
      !indications.includes(input.query.trim().toLowerCase())
    ) {
      input.filters = {
        ...input.filters,
        indications
      };
    }

    /**
     * The Idea behind the BOTH operator is that:-
     * We convert a query like BOTH( Cardiology WITH diabetes ) to Cardiology OR diabetes and add
     * corresponding should clauses based on the new query, but add filter clauses based on  BOTH( Cardiology WITH diabetes )
     * // Nadeem - 1 trial cardiology as well as diabetes:- Use Case A ✅
     * // Yash - 1 trial cardiology and 1 trial diabetes :- Use Case B ✅
     * // Abhishek - 1 trial Cardiology and 1 pub diabetes :- Use Case C ✅
     * // Shreenath - 1 trial Cardiology and 1 pub Cardiology:- ❌
     * // BMP - Has nothing:- Use Case D ❌
     * here essentially should would want to match Shreenath(using the OR query) but filter clause has removed Shreenath
     * from search space.
     */
    let bothParserResponse: BothQueryParseResult | undefined = undefined;
    //BOTH operator right now only availale for HCPU
    if (
      featureFlags.enableBothOperatorForSearch &&
      originalInput.app === Apps.HCPU
    ) {
      bothParserResponse = this.queryParserService.parseBothQuery(input.query);
      if (bothParserResponse.usedBoth) {
        input.query =
          this.queryParserService.rewriteBothQueryToOrExpression(
            bothParserResponse
          );
        if (queryUnderstandingServiceResponse) {
          queryUnderstandingServiceResponse =
            await this.retrieveQueryUnderstandingServiceResponse(input);
        }
      }
    }

    const isQueryAnInstitution = this.isQueryAnInstitution(
      input,
      queryUnderstandingServiceResponse
    );

    const indicationsValues = input.filters?.indications;
    let indicationsParsedQuery, indicationsIcdCodesQuery;

    if (indicationsValues?.length) {
      [indicationsParsedQuery, indicationsIcdCodesQuery] =
        await this.getIndicationSynonymsFromQueryUnderstandingService(
          indicationsValues,
          true,
          input.sortBy == InstitutionSortOptions.UK_DIVERSITY ? "uk" : "us" //TODO: Add for France?
        );
    }
    const icdCodesForCareClusterSelected =
      await this.getClaimCodesForCareCluster(input, "diagnoses");
    let data: estypes.SearchResponse<ElasticsearchInstitutionDoc>;
    let needToGetMatchingHCPAndPublicationCounts = false;
    if (isQueryAnInstitution && featureFlags.enableInstitutionNameSuggest) {
      const extraFilters: QueryDslQueryContainer[] = [];
      const results = await this.performInstitutionNameSearch(
        input,
        featureFlags,
        isBulkSearch,
        requestType,
        extraFilters,
        indications,
        queryUnderstandingServiceResponse
      );

      if (results === null) {
        return this.institutionsSearchResponseAdapterService.adaptToEmptyResponse(
          requestType,
          isQueryAnInstitution
        );
      } else if (typeof results === "number") {
        return results;
      }

      data = results;
    } else {
      const results =
        await this.performInstitutionKeywordSearchOrLegacyNameSearch(
          input,
          originalInput,
          searchContext,
          featureFlags,
          requestType,
          indications,
          queryUnderstandingServiceResponse,
          indicationsIcdCodesQuery,
          indicationsParsedQuery,
          bothParserResponse,
          icdCodesForCareClusterSelected
        );

      if (results === null) {
        return this.institutionsSearchResponseAdapterService.adaptToEmptyResponse(
          requestType
        );
      } else if (typeof results === "number") {
        return results;
      }

      data = results.data;
      needToGetMatchingHCPAndPublicationCounts =
        results.needToGetMatchingHCPAndPublicationCounts;
    }

    this.logger.info(
      { took: data.took, total: data.hits.total },
      "execution time"
    );

    let peopleData: Array<estypes.MsearchResponseItem> = [];
    let peopleDataForIE: PeopleSearchInclusionExclusionAggregationResponse | null =
      null;

    if (needToGetMatchingHCPAndPublicationCounts) {
      const noKeywordSearchResults =
        this.extractTotalValue(data.hits.total ?? 0) === 0;

      if (noKeywordSearchResults) {
        this.logger.info(
          "Zero results for institution keyword search, performing institution name search"
        );
        return this.executeNameSearchRequest(
          input,
          featureFlags,
          isBulkSearch,
          requestType,
          indications,
          queryUnderstandingServiceResponse
        );
      } else if (this.inputHasPatientClaimsFilter(input, featureFlags)) {
        peopleDataForIE = await this.getMatchingHCPForIE(input, data);
      } else {
        peopleData = await this.getMatchingHCPAndPublicationCounts(
          input,
          data,
          queryUnderstandingServiceResponse,
          indicationsParsedQuery,
          indicationsIcdCodesQuery
        );
      }
    }

    let sharedPeopleFromAffiliationCounts: Dictionary<number> | undefined;

    if (this.inputHasAffiliatedPeopleIols(input)) {
      if (this.inputHasPatientClaimsFilter(input, featureFlags)) {
        sharedPeopleFromAffiliationCounts =
          await this.getMatchingAffiliatedHCPCountsForIE(input, data);
      } else {
        sharedPeopleFromAffiliationCounts =
          await this.getMatchingAffiliatedHCPAndPublicationCounts(
            input,
            data,
            queryUnderstandingServiceResponse,
            indicationsParsedQuery,
            indicationsIcdCodesQuery
          );
      }
    }

    const { matchedPatientCountMap, patientDiversityDistributionMap } =
      await this.getMatchingPatientCountAndDiversityDistributionForInstitutions(
        input,
        data,
        featureFlags
      );

    const totalInstitutionsInArea = await this.getTotalInstitutionsInArea(
      input
    );

    const matchedClaimsForInstitutionsMap =
      await this.getMatchedClaimsCountForIols(input, data, featureFlags);
    const { trialsOngoingCountMap, trialsActivelyRecruitingCountMap } =
      await this.getMatchedTrialsCountForIols(
        input,
        data,
        queryUnderstandingServiceResponse,
        indicationsParsedQuery
      );
    const keywordIcdCodeSynonyms =
      queryUnderstandingServiceResponse?.getDiagnosisCodesList() ?? [];
    const indicationSynonyms =
      indicationsParsedQuery?.replace(/[()]/g, "").split("|").map(trim) ?? [];
    const indicationIcdCodeSynonyms =
      indicationsIcdCodesQuery?.replace(/[()]/g, "").split("|").map(trim) ?? [];
    return {
      data,
      peopleData,
      peopleDataForIE,
      isQueryAnInstitution,
      keywordIcdCodeSynonyms,
      indicationIcdCodeSynonyms,
      indicationSynonyms,
      resolvedIndications: indications,
      matchedPatientCountMap,
      matchedClaimsForInstitutionsMap,
      patientDiversityDistributionMap,
      trialsOngoingCountMap,
      trialsActivelyRecruitingCountMap,
      totalInstitutionsInArea,
      sharedPeopleFromAffiliationCounts
    };
  }

  private async performInstitutionNameSearch(
    input: Readonly<InstitutionsSearchInput>,
    featureFlags: InstitutionSearchFeatureFlags,
    isBulkSearch: boolean,
    requestType: RequestType,
    extraFilters: ReadonlyArray<QueryDslQueryContainer>,
    resolvedIndication: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>
  ): Promise<estypes.SearchResponse<ElasticsearchInstitutionDoc> | number> {
    return this.institutionNameSearchResourceService.searchInstitutionsByName(
      input,
      featureFlags,
      isBulkSearch,
      requestType,
      extraFilters,
      resolvedIndication,
      queryUnderstandingServiceResponse
    );
  }

  private async performInstitutionKeywordSearchOrLegacyNameSearch(
    input: Readonly<InstitutionsSearchInput>,
    originalInput: Readonly<InstitutionsSearchInput>,
    searchContext: SearchContext,
    featureFlags: InstitutionSearchFeatureFlags,
    requestType: RequestType,
    resolvedIndication: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    indicationsIcdCodesQuery?: string,
    indicationsParsedQuery?: string,
    bothParserResponse?: BothQueryParseResult,
    icdCodesForCareClusterSelected?: string[]
  ): Promise<
    | {
        data: estypes.SearchResponse<ElasticsearchInstitutionDoc>;
        needToGetMatchingHCPAndPublicationCounts: boolean;
      }
    | number
    | null
  > {
    const { request, needToGetMatchingHCPAndPublicationCounts } =
      await this.buildSearchRequest(
        input,
        originalInput,
        searchContext,
        featureFlags,
        requestType,
        [],
        resolvedIndication,
        queryUnderstandingServiceResponse,
        indicationsIcdCodesQuery,
        indicationsParsedQuery,
        bothParserResponse,
        icdCodesForCareClusterSelected
      );

    if (!request) {
      return null;
    }

    if (requestType === "count") {
      this.logger.info({ query: request }, "elasticsearch count request");
      const count = await this.elasticSearchService.count(request);
      return count.count;
    }

    this.logger.info({ query: request }, "elasticsearch request");

    return {
      data: await this.elasticSearchService.query<ElasticsearchInstitutionDoc>(
        request
      ),
      needToGetMatchingHCPAndPublicationCounts
    };
  }

  private async buildSearchRequest(
    input: Readonly<InstitutionsSearchInput>,
    originalInput: Readonly<InstitutionsSearchInput>,
    searchContext: SearchContext,
    featureFlags: InstitutionSearchFeatureFlags,
    requestType: RequestType,
    extraFilters: Array<QueryDslQueryContainer>,
    resolvedIndication: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    indicationsIcdCodesQuery?: string,
    indicationsParsedQuery?: string,
    bothParserResponse?: BothQueryParseResult,
    icdCodesForCareClusterSelected?: string[]
  ): Promise<{
    request: estypes.SearchRequest | null;
    needToGetMatchingHCPAndPublicationCounts: boolean;
    isQueryAnInstitution: boolean;
  }> {
    const isBulkSearch = searchContext === "bulk";
    // The elastic search request is formed based on desired ranking. There are currently five ranking choices:
    // 1. H1 Ranking : This is the default ranking which is based on the sum of matching diagnoses,procedures and trials.
    // 2. Diversity Ranking : This ranking combines the H1 ranking with the diversity of the institution.
    // 3. Sort By Trials: This forces the results to be sorted by matching trials count or total trials count if there is no trial filter or query
    // 4. Name Search Request:  Whenever name search request is formed, the results are sorted by logarithmic addition of total diagnoses, procedures and trials count to push more relevant HCPs to the top.
    // 5. Trial Performance Ranking: This ranking is based on the CTMS performance data of each institution
    let request: estypes.SearchRequest;
    let needToGetMatchingHCPAndPublicationCounts = false;
    const skipInnerHitsForClaims = false;

    // If the input query is an institution name then its a navigational query instead of a categorical query
    const isQueryAnInstitution = this.isQueryAnInstitution(
      input,
      queryUnderstandingServiceResponse
    );

    const indicationsValues = input.filters?.indications;
    if (this.inputHasTrialsFiltersOrSortOption(input)) {
      request = this.buildSearchRequestForTrialsSortAndFilter(
        input,
        originalInput,
        featureFlags,
        isBulkSearch,
        extraFilters,
        resolvedIndication,
        queryUnderstandingServiceResponse,
        indicationsParsedQuery,
        indicationsIcdCodesQuery,
        bothParserResponse,
        icdCodesForCareClusterSelected
      );

      needToGetMatchingHCPAndPublicationCounts =
        !isQueryAnInstitution && !!input.query;
    } else if (
      (!input.query &&
        !indicationsValues?.length &&
        !this.inputHasPatientClaimsFilter(input, featureFlags) &&
        !(input.sortBy == InstitutionSortOptions.PATIENT_COUNT) &&
        !(
          isEuropeanDiversitySort(input.sortBy) &&
          input?.filters?.claimsFilters?.diagnosesICD?.length
        )) ||
      isQueryAnInstitution
    ) {
      // If the input query is not supplied or if the input query is an institution name (and hence we don't want to search the institution name within trials, diagnosis or procedures)
      // AND if there are no input query, trials filters or sort options applied then this means there is no concept of 'matching fields' out of total fields.
      // In such a scenario just sort the results by sum of total fields (trials, diagnosis and procedures).
      const nameRequest = await this.buildNameSearchRequest(
        input,
        featureFlags,
        isBulkSearch,
        requestType,
        extraFilters,
        resolvedIndication,
        queryUnderstandingServiceResponse,
        icdCodesForCareClusterSelected
      );

      return {
        request: nameRequest,
        needToGetMatchingHCPAndPublicationCounts,
        isQueryAnInstitution
      };
    } else {
      request = this.buildKeywordSearchRequest(
        input,
        originalInput,
        featureFlags,
        isBulkSearch,
        requestType,
        extraFilters,
        resolvedIndication,
        queryUnderstandingServiceResponse,
        indicationsParsedQuery,
        indicationsIcdCodesQuery,
        bothParserResponse,
        icdCodesForCareClusterSelected
      );

      needToGetMatchingHCPAndPublicationCounts = true;
    }

    if (input.exportsSelections || isBulkSearch) {
      // **
      // Exports and bulk search does not need matched HCP and pub counts. Skip it for better performance.

      needToGetMatchingHCPAndPublicationCounts = false;
    }
    const isDiversitySort = input.sortBy === InstitutionSortOptions.DIVERSITY;
    if (this.hasTagFilters(input)) {
      const requestWithTags = await this.buildFiltersForTags(
        input,
        request,
        isDiversitySort,
        featureFlags
      );

      if (requestWithTags) {
        request = requestWithTags;
      } else {
        return {
          request: null,
          needToGetMatchingHCPAndPublicationCounts: false,
          isQueryAnInstitution
        };
      }
    }

    if (request) {
      request.aggs = buildAggregationsForSearchRequest(
        input,
        featureFlags,
        queryUnderstandingServiceResponse,
        indicationsIcdCodesQuery,
        CLAIMS_DESCRIPTION_ANALYZER,
        input.filters?.claimsFilters?.diagnosesICD
      );
    }

    if (filtersPresent(input.filters?.claimsFilters)) {
      request = buildRequestForClaims(
        input,
        request,
        featureFlags,
        skipInnerHitsForClaims,
        indicationsParsedQuery,
        indicationsIcdCodesQuery,
        input.sortBy,
        input.filters?.diversityFilters?.race ?? undefined
      );
    }

    if (filtersPresent(input.filters?.exclusionClaims)) {
      request = buildRequestForExcludedClaims(
        input.filters!.exclusionClaims!,
        request,
        featureFlags,
        shouldExcludeHcpBasedOnInput(input, featureFlags)
      );
    }

    if (requestType === "count") {
      const parentQuery: QueryDslQueryContainer = {
        has_parent: {
          parent_type: "iol",
          query: {
            bool: {
              filter: request.query
            }
          }
        }
      };
      const claimsFilter = this.buildPatientClaimsFilterForCountQuery(input);
      const countQuery: QueryDslQueryContainer = {
        bool: {
          filter: [parentQuery, ...claimsFilter]
        }
      };
      const countRequest: CountRequest = {
        index: this.institutionsIndexName,
        query: countQuery
      };

      return {
        request: countRequest,
        needToGetMatchingHCPAndPublicationCounts,
        isQueryAnInstitution
      };
    }

    return {
      request,
      needToGetMatchingHCPAndPublicationCounts,
      isQueryAnInstitution
    };
  }

  private async getMatchingPatientCountAndDiversityDistributionForInstitutions(
    input: Readonly<InstitutionsSearchInput>,
    data: estypes.SearchResponse<ElasticsearchInstitutionDoc>,
    featureFlags: InstitutionSearchFeatureFlags
  ) {
    let matchedPatientCountMap: Dictionary<number> = {};
    let patientDiversityDistributionMap: Dictionary<{
      race: DocCountBucket[];
      gender: DocCountBucket[];
      age: DocCountBucket[];
    }> = {};

    const iolDocIds = data.hits.hits.map((hit) => hit._id);
    if (iolDocIds.length) {
      const patientPopulationFilter = this.buildPatientPopulationFilter(
        input,
        featureFlags
      );
      const searchRequests: Promise<estypes.SearchResponse>[] = [];
      iolDocIds.forEach((iolDocId) => {
        const query: QueryDslQueryContainer = {
          bool: {
            filter: [
              {
                term: {
                  _routing: iolDocId
                }
              },
              ...patientPopulationFilter
            ]
          }
        };
        const patientDiversityAggregations = {
          patient_race_count: {
            terms: {
              field: "patientClaims.diversity",
              size: Object.keys(PatientDiversityEnum).length
            }
          },
          patient_gender_count: {
            terms: {
              field: "patientClaims.gender",
              size: 10
            }
          },
          patient_age_count: {
            terms: {
              field: "patientClaims.age",
              size: 20
            }
          }
        };
        const searchRequest: SearchRequest = {
          index: this.institutionsIndexName,
          query,
          track_total_hits: true,
          size: 0,
          aggs: patientDiversityAggregations
        };
        searchRequests.push(this.elasticSearchService.query(searchRequest));
      });

      const responses = await Promise.all(searchRequests);

      const counts = responses.map((response) =>
        getHitsTotal(response.hits.total ?? 0)
      );

      matchedPatientCountMap = zipObject(iolDocIds, counts);

      this.logger.info(
        {
          countMap: matchedPatientCountMap
        },
        "Matched patient counts for patientClaimsFilter"
      );
      const diversityDistributions = responses.map((response) => {
        return {
          race: response.aggregations
            ? ((
                response.aggregations
                  .patient_race_count as AggregationsTermsAggregateBase<DocCountBucket>
              )?.buckets as DocCountBucket[])
            : ([] as DocCountBucket[]),
          gender: response.aggregations
            ? ((
                response.aggregations
                  .patient_gender_count as AggregationsTermsAggregateBase<DocCountBucket>
              )?.buckets as DocCountBucket[])
            : ([] as DocCountBucket[]),
          age: response.aggregations
            ? ((
                response.aggregations
                  .patient_age_count as AggregationsTermsAggregateBase<DocCountBucket>
              )?.buckets as DocCountBucket[])
            : ([] as DocCountBucket[])
        };
      });

      patientDiversityDistributionMap = zipObject(
        iolDocIds,
        diversityDistributions
      );
    }
    return { matchedPatientCountMap, patientDiversityDistributionMap };
  }

  private async getTotalInstitutionsInArea(
    input: Readonly<InstitutionsSearchInput>
  ) {
    if (!input.filters) {
      return undefined;
    }
    const geoShapeFilters = buildGeoShapeFilter(input.filters);

    if (!geoShapeFilters.length) {
      return undefined;
    }
    const projectIdFilter = buildTermsQuery(PROJECT_IDS, [input.projectId]);
    const countQuery: QueryDslQueryContainer = {
      bool: {
        filter: [projectIdFilter, ...geoShapeFilters]
      }
    };
    const countRequest: CountRequest = {
      index: this.institutionsIndexName,
      query: countQuery
    };

    const count = await this.elasticSearchService.count(countRequest);
    return count.count;
  }

  private async getMatchedClaimsCountForIols(
    input: Readonly<InstitutionsSearchInput>,
    data: estypes.SearchResponse<ElasticsearchInstitutionDoc>,
    featureFlags: InstitutionSearchFeatureFlags
  ): Promise<InstitutionClaimsMapForExports> {
    const diagnosesCountMap: Dictionary<
      Dictionary<{
        count: number;
        scheme: string | undefined;
        description: string | undefined;
      }>
    > = {};
    const proceduresCountMap: Dictionary<
      Dictionary<{
        count: number;
        scheme: string | undefined;
        description: string | undefined;
      }>
    > = {};
    if (
      this.inputHasPatientClaimsFilter(input, featureFlags) &&
      input.exportsSelections?.matchedClaimsDetails
    ) {
      const iolDocIds = data.hits.hits.map((hit) => hit._id);

      // I/E Claims filters
      const {
        diagnosesCodes: diagnosesCodesFromIE,
        proceduresCodes: proceduresCodesFromIE,
        ccsrDxDescriptions: ccsrDxDescriptionsFromIE,
        ccsrPxDescriptions: ccsrPxDescriptionsFromIE
      } = this.extractInclusionClaimsCodesFromIE(input);

      //Side panel filters
      const sidePanelDiagnosesCodes =
        input.filters?.claimsFilters?.diagnosesICD ?? [];
      const sidePanelProceduresCodes =
        input.filters?.claimsFilters?.proceduresCPT ?? [];
      const sidePanelCcsrDxDescriptions =
        input.filters?.claimsFilters?.ccsr ?? [];
      //NOTE: YASH we don't have ccsrPx in side panel we need to address this later

      //We collect codes from various sources-
      //1. side panel filters- codes, codes in ccsr
      //2. I/E filters - codes, codes in ccsr
      let allCombinedDiagnosesCodes: Array<string> = _.union(
        diagnosesCodesFromIE,
        sidePanelDiagnosesCodes.map(extractClaimsCode)
      );
      let allCombinedProceduresCodes: Array<string> = _.union(
        proceduresCodesFromIE,
        sidePanelProceduresCodes.map(extractClaimsCode)
      );

      //add codes from ccsr
      const ccsrDxDescriptions = _.union(
        ccsrDxDescriptionsFromIE,
        sidePanelCcsrDxDescriptions
      );
      const ccsrPxDescriptions = ccsrPxDescriptionsFromIE;
      if (!_.isEmpty(ccsrDxDescriptions)) {
        const ccsrDxCodes = await this.getClaimCodesForCareClusters(
          ccsrDxDescriptions,
          "ccsr"
        );
        allCombinedDiagnosesCodes = _.union(
          allCombinedDiagnosesCodes,
          ccsrDxCodes
        );
      }
      if (!_.isEmpty(ccsrPxDescriptions)) {
        const ccsrPxCodes = await this.getClaimCodesForCareClusters(
          ccsrPxDescriptions,
          "ccsr_px"
        );
        allCombinedProceduresCodes = _.union(
          allCombinedProceduresCodes,
          ccsrPxCodes
        );
      }

      if (
        iolDocIds.length &&
        (allCombinedDiagnosesCodes.length || allCombinedProceduresCodes.length)
      ) {
        const searchRequests: Promise<SearchResponse>[] = [];

        const diagnosesClaimCodeMetadataMap =
          await this.claimCodeService.getDiagnosesClaimCodeMetadataMap(
            allCombinedDiagnosesCodes
          );
        const procedureClaimCodeMetadataMap =
          await this.claimCodeService.getProcedureClaimCodeMetadataMap(
            allCombinedProceduresCodes
          );
        iolDocIds.forEach((iolDocId) => {
          const shouldClauses = [];
          if (allCombinedDiagnosesCodes.length) {
            shouldClauses.push(
              buildTermsQuery(
                "patientClaims.diagnosisIcdCode",
                allCombinedDiagnosesCodes
              )
            );
          }
          if (allCombinedProceduresCodes.length) {
            shouldClauses.push(
              buildTermsQuery(
                "patientClaims.procedureCode",
                allCombinedProceduresCodes
              )
            );
          }

          const searchQuery: QueryDslQueryContainer = {
            bool: {
              should: shouldClauses,
              minimum_should_match: 1,
              filter: [
                {
                  term: {
                    _routing: iolDocId
                  }
                }
              ]
            }
          };
          const aggregations = {
            by_icdcode: {
              terms: {
                field: "patientClaims.diagnosisIcdCode",
                include: allCombinedDiagnosesCodes,
                size: 50
              }
            },
            by_procedure_code: {
              terms: {
                field: "patientClaims.procedureCode",
                include: allCombinedProceduresCodes,
                size: 50
              }
            }
          };
          const searchRequest: SearchRequest = {
            index: this.institutionsIndexName,
            query: searchQuery,
            _source: false,
            size: 0,
            aggs: aggregations
          };
          searchRequests.push(this.elasticSearchService.query(searchRequest));
        });

        const responses = await Promise.all(searchRequests);
        const diagnosesAggregations = responses.map(
          (response) =>
            response.aggregations
              ?.by_icdcode as AggregationsTermsAggregateBase<DocCountBucket>
        );
        const proceduresAggregations = responses.map(
          (response) =>
            response.aggregations
              ?.by_procedure_code as AggregationsTermsAggregateBase<DocCountBucket>
        );

        diagnosesAggregations.forEach((diagnosesBucket, index) => {
          const iolCountryLocation =
            data.hits.hits[index]?._source?.["address.country"];
          const iolNormalizedCountryName =
            this.normalizeCountryName(iolCountryLocation);
          diagnosesCountMap[iolDocIds[index]] =
            this.buildClaimsCountDictionaryForIOL(
              diagnosesBucket,
              diagnosesClaimCodeMetadataMap,
              iolNormalizedCountryName
            );
        });
        proceduresAggregations.forEach((proceduresBucket, index) => {
          const iolCountryLocation =
            data.hits.hits[index]?._source?.["address.country"];
          const iolNormalizedCountryName =
            this.normalizeCountryName(iolCountryLocation);
          proceduresCountMap[iolDocIds[index]] =
            this.buildClaimsCountDictionaryForIOL(
              proceduresBucket,
              procedureClaimCodeMetadataMap,
              iolNormalizedCountryName
            );
        });

        this.logger.info(
          {
            diagnosesCountMap,
            proceduresCountMap
          },
          "Individual claim codes matched patient counts for patientClaimsFilter for exports"
        );
      }
    }

    return { diagnosesCountMap, proceduresCountMap };
  }

  private async getMatchedTrialsCountForIols(
    input: InstitutionsSearchInput,
    data: estypes.SearchResponse<ElasticsearchInstitutionDoc>,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse,
    indicationsParsedQuery?: string
  ): Promise<{
    trialsOngoingCountMap?: Dictionary<number>;
    trialsActivelyRecruitingCountMap?: Dictionary<number>;
  }> {
    const trialsOngoingCountMap: Dictionary<number> = {};
    const trialsActivelyRecruitingCountMap: Dictionary<number> = {};
    const { trialsLevelFilters } = this.buildTrialFilters(
      input,
      queryUnderstandingServiceResponse,
      indicationsParsedQuery
    );

    if (trialsLevelFilters.length === 0) {
      return {};
    }

    const iolDocIds = data.hits.hits.map((hit) => hit._id);
    const trialsOngoingQuery: SearchRequest = {
      index: this.institutionsIndexName,
      size: iolDocIds.length,
      track_total_hits: false,
      _source_includes: ["id"],
      query: {
        function_score: {
          query: {
            nested: {
              path: "trials",
              score_mode: "sum",
              query: {
                constant_score: {
                  filter: {
                    bool: {
                      filter: [
                        ...trialsLevelFilters,
                        {
                          terms: {
                            _id: iolDocIds
                          }
                        },
                        {
                          terms: {
                            "trials.status": [
                              "Not yet recruiting",
                              "Recruiting",
                              "Enrolling by invitation",
                              "Active, not recruiting",
                              "Active"
                            ]
                          }
                        }
                      ]
                    }
                  }
                }
              }
            }
          }
        }
      }
    };
    const trialsActivelyRecruitingQuery: SearchRequest = {
      index: this.institutionsIndexName,
      size: iolDocIds.length,
      track_total_hits: false,
      _source_includes: ["id"],
      query: {
        function_score: {
          query: {
            nested: {
              path: "trials",
              score_mode: "sum",
              query: {
                constant_score: {
                  filter: {
                    bool: {
                      filter: [
                        ...trialsLevelFilters,
                        {
                          terms: {
                            _id: iolDocIds
                          }
                        },
                        {
                          terms: {
                            "trials.status": [
                              "Recruiting",
                              "Enrolling by invitation"
                            ]
                          }
                        }
                      ]
                    }
                  }
                }
              }
            }
          }
        }
      }
    };

    const [trialsOngoingResponse, trialsActivelyRecruitingResponse] =
      await Promise.all([
        this.elasticSearchService.query(trialsOngoingQuery),
        this.elasticSearchService.query(trialsActivelyRecruitingQuery)
      ]);

    if (this.isErrorResponse(trialsOngoingResponse)) {
      this.logger.error(trialsOngoingResponse);
    } else {
      trialsOngoingResponse.hits.hits.forEach((hit: any) => {
        trialsOngoingCountMap[hit._id] = hit._score ?? 0;
      });
    }

    if (this.isErrorResponse(trialsActivelyRecruitingResponse)) {
      this.logger.error(trialsActivelyRecruitingResponse);
    } else {
      trialsActivelyRecruitingResponse.hits.hits.forEach((hit: any) => {
        trialsActivelyRecruitingCountMap[hit._id] = hit._score ?? 0;
      });
    }

    return { trialsOngoingCountMap, trialsActivelyRecruitingCountMap };
  }

  extractInclusionClaimsCodesFromIE(input: InstitutionsSearchInput) {
    const {
      diagnosesCodes,
      proceduresCodes,
      ccsrDxDescriptions,
      ccsrPxDescriptions
    } = this.rulesParserService.extractClaimsAndProcedureCodesFromRules(
      input.filters
    );
    return {
      diagnosesCodes,
      proceduresCodes,
      ccsrDxDescriptions,
      ccsrPxDescriptions
    };
  }

  buildClaimsCountDictionaryForIOL(
    claimsBucket: AggregationsTermsAggregateBase<DocCountBucket>,
    codeToMetadataMap: Map<string, { scheme: string; description: string }>,
    iolCountry: string | null
  ) {
    const codesDict: Dictionary<{
      count: number;
      scheme: string | undefined;
      description: string | undefined;
    }> = {};
    const buckets = claimsBucket?.buckets
      ? (claimsBucket.buckets as DocCountBucket[])
      : [];
    buckets.forEach((bucket: DocCountBucket) => {
      // ✅ Initialize object if not already
      if (!codesDict[bucket.key]) {
        codesDict[bucket.key] = {
          count: 0,
          scheme: undefined,
          description: undefined
        };
      }
      codesDict[bucket.key].count = bucket.doc_count;
      if (iolCountry) {
        const key = this.claimCodeService.claimCodeMetadataMapKeyGenerator(
          bucket.key,
          iolCountry
        );
        const metadata = codeToMetadataMap.get(key);
        codesDict[bucket.key].description = metadata?.description;
        codesDict[bucket.key].scheme = metadata?.scheme;
      }
    });
    return codesDict;
  }

  buildClaimCodeToMetadataDictionary(
    claimCodes: ClaimCode[]
  ): Map<string, { scheme: string; description: string }> {
    const codeCountryToMetadata = new Map<
      string,
      { scheme: string; description: string }
    >();
    for (const claimCode of claimCodes) {
      for (const countryDesc of claimCode.countryDescriptions) {
        const key = `${claimCode.code}::${countryDesc.Country}`;
        codeCountryToMetadata.set(key, {
          scheme: claimCode.scheme,
          description: countryDesc.Description
        });
      }
    }
    return codeCountryToMetadata;
  }

  /**
   * Normalizes the given country name from the ElasticSearch format to the corresponding
   * country name used in the pipeline database.
   *
   * @param elasticCountry - The country name as returned by ElasticSearch, or `undefined`.
   * @returns The normalized country name as used in the pipeline database, or `null` if the input is `undefined` or not found in the mapping.
   */
  normalizeCountryName(elasticCountry: string | undefined): string | null {
    if (!elasticCountry) return null;
    return (
      elasticCountryFieldToPipelineDBCountryMap[elasticCountry.trim()] ?? null
    );
  }

  private async buildNameSearchRequest(
    input: Readonly<InstitutionsSearchInput>,
    featureFlags: Readonly<InstitutionSearchFeatureFlags>,
    isBulkSearch: boolean,
    requestType: RequestType,
    extraFilters: ReadonlyArray<QueryDslQueryContainer>,
    resolvedIndication: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    icdCodesForCareClusterSelected?: string[]
  ): Promise<estypes.SearchRequest | null> {
    let request = this.codesOrTimeFrameSpecified(input.filters?.claimsFilters)
      ? this.buildSearchRequestWithoutRankByTotal(
          input,
          featureFlags,
          isBulkSearch,
          extraFilters,
          resolvedIndication,
          icdCodesForCareClusterSelected
        )
      : this.buildSearchRequestToRankByTotalFields(
          input,
          featureFlags,
          isBulkSearch,
          extraFilters,
          resolvedIndication
        );

    const isDiversitySort = input.sortBy === InstitutionSortOptions.DIVERSITY;
    if (this.hasTagFilters(input)) {
      const requestWithTags = await this.buildFiltersForTags(
        input,
        request,
        isDiversitySort,
        featureFlags
      );

      if (requestWithTags) {
        request = requestWithTags;
      } else {
        return null;
      }
    }

    if (request) {
      request.aggs = buildAggregationsForSearchRequest(
        input,
        featureFlags,
        queryUnderstandingServiceResponse
      );
    }

    if (
      filtersPresent(_.omit(input.filters?.claimsFilters, "showUniquePatients"))
    ) {
      request = buildRequestForClaims(
        input,
        request,
        featureFlags,
        undefined,
        undefined,
        undefined,
        input.sortBy,
        input.filters?.diversityFilters?.race ?? undefined
      );
    }

    if (filtersPresent(input.filters?.exclusionClaims)) {
      request = buildRequestForExcludedClaims(
        input.filters!.exclusionClaims!,
        request,
        featureFlags,
        shouldExcludeHcpBasedOnInput(input, featureFlags)
      );
    }

    if (requestType === "count") {
      const parentQuery: QueryDslQueryContainer = {
        has_parent: {
          parent_type: "iol",
          query: {
            bool: {
              filter: request.query
            }
          }
        }
      };
      const claimsFilter = this.buildPatientClaimsFilterForCountQuery(input);
      const countQuery: QueryDslQueryContainer = {
        bool: {
          filter: [parentQuery, ...claimsFilter]
        }
      };
      const countRequest: CountRequest = {
        index: this.institutionsIndexName,
        query: countQuery
      };

      return countRequest;
    }

    return request;
  }

  private async executeNameSearchRequest(
    input: Readonly<InstitutionsSearchInput>,
    featureFlags: Readonly<InstitutionSearchFeatureFlags>,
    isBulkSearch: boolean,
    requestType: RequestType,
    resolvedIndications: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>
  ) {
    const isQueryAnInstitution = true;
    const enableInstitutionNameSuggest =
      featureFlags.enableInstitutionNameSuggest;
    const extraFilters: QueryDslQueryContainer[] = [];
    let data: estypes.SearchResponse<
      ElasticsearchInstitutionDoc,
      Record<string, estypes.AggregationsAggregate>
    >;

    if (enableInstitutionNameSuggest) {
      const results = await this.performInstitutionNameSearch(
        input,
        featureFlags,
        isBulkSearch,
        requestType,
        extraFilters,
        resolvedIndications,
        queryUnderstandingServiceResponse
      );

      if (results === null) {
        return this.institutionsSearchResponseAdapterService.adaptToEmptyResponse(
          requestType,
          isQueryAnInstitution
        );
      } else if (typeof results === "number") {
        return results;
      }

      data = results;
    } else {
      const request = await this.buildNameSearchRequest(
        input,
        featureFlags,
        isBulkSearch,
        requestType,
        extraFilters,
        resolvedIndications,
        queryUnderstandingServiceResponse
      );

      if (!request) {
        return this.institutionsSearchResponseAdapterService.adaptToEmptyResponse(
          requestType,
          isQueryAnInstitution
        );
      }

      if (requestType === "count") {
        this.logger.info({ query: request }, "elasticsearch count request");

        const count = await this.elasticSearchService.count(request);
        return count.count;
      }

      this.logger.info({ query: request }, "elasticsearch request");

      data = await this.elasticSearchService.query<ElasticsearchInstitutionDoc>(
        request
      );
    }

    this.logger.info(
      { took: data.took, total: data.hits.total },
      "execution time"
    );

    const iolFoundByName = this.extractTotalValue(data.hits.total ?? 0) > 0;
    const peopleData: Array<estypes.MsearchResponseItem> = [];
    let peopleDataForIE: PeopleSearchInclusionExclusionAggregationResponse | null =
      null;

    if (
      this.inputHasPatientClaimsFilter(input, featureFlags) &&
      this.buildPatientClaimsFilter(input).length > 0
    ) {
      peopleDataForIE = await this.getMatchingHCPForIE(input, data);
    }

    const keywordIcdCodeSynonyms =
      queryUnderstandingServiceResponse?.getDiagnosisCodesList() ?? [];

    return {
      data,
      peopleData,
      isQueryAnInstitution: iolFoundByName,
      keywordIcdCodeSynonyms,
      resolvedIndications,
      peopleDataForIE
    };
  }

  private codesOrTimeFrameSpecified(claimsFilters?: ClaimFiltersInput | null) {
    if (!claimsFilters) {
      return false;
    }

    return _.some(
      [
        claimsFilters.diagnosesICD,
        claimsFilters.ccsr,
        claimsFilters.proceduresCPT,
        claimsFilters.genericNames,
        claimsFilters.timeFrame
      ],
      (value) => !_.isNil(value)
    );
  }

  private async getMatchingHCPAndPublicationCounts(
    input: Readonly<InstitutionsSearchInput>,
    iolSearchResponse: Readonly<
      estypes.SearchResponse<ElasticsearchInstitutionDoc>
    >,
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    indicationsParsedQuery?: string,
    indicationsIcdCodesQuery?: string
  ): Promise<Array<estypes.MsearchResponseItem>> {
    const queries: Array<MSearchRequest> = iolSearchResponse.hits.hits.map(
      (hit) => {
        const iolFilters: QueryDslQueryContainer[] =
          buildAffiliationFiltersForMatchingHcp(
            INSTITUTION_ID_FIELD_IN_PEOPLE,
            [hit._source!.masterOrganizationId as number]
          );

        const institutionsFilters: QueryDslQueryContainer[] = [
          {
            nested: {
              path: "affiliations",
              query: {
                bool: {
                  filter: iolFilters
                }
              }
            }
          }
        ];

        return this.buildMultiSearchRequestBodyToGetMatchingHCPsAndPublications(
          input,
          institutionsFilters,
          queryUnderstandingServiceResponse,
          indicationsParsedQuery,
          indicationsIcdCodesQuery
        );
      }
    );

    if (!queries.length) {
      return [];
    }

    const multiSearchRequest: estypes.MsearchRequest = {
      index: this.peopleIndexName,
      searches: queries.flat()
    };
    this.logger.info(
      { query: JSON.stringify(multiSearchRequest) },
      "elasticsearch multi search request"
    );

    const { took, responses }: estypes.MsearchResponse =
      await this.elasticSearchService.msearch(multiSearchRequest);

    this.logger.info({ took: took }, "execution time");

    return responses;
  }

  private async getMatchingAffiliatedHCPAndPublicationCounts(
    input: Readonly<InstitutionsSearchInput>,
    iolSearchResponse: Readonly<
      estypes.SearchResponse<ElasticsearchInstitutionDoc>
    >,
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    indicationsParsedQuery?: string,
    indicationsIcdCodesQuery?: string
  ): Promise<Dictionary<number>> {
    let matchedHCPSCountMap: Dictionary<number> = {};
    const institutionIds: number[] = iolSearchResponse.hits.hits
      .map((hit) => {
        return hit._source!.masterOrganizationId;
      })
      .filter((id) => !!id) as number[];

    const affiliatedPeopleIolsIds =
      input.filters?.affiliatedPeopleIolsIds ?? [];

    const queries = institutionIds.map((id) => {
      const institutionsFilters: QueryDslQueryContainer[] =
        buildAffiliationFiltersForMatchingIOLAffiliatedHcps(
          INSTITUTION_ID_FIELD_IN_PEOPLE,
          [id, ...this.affiliatedPeopleIolsIdsToInt(affiliatedPeopleIolsIds)]
        );

      const { query } =
        this.buildMultiSearchRequestBodyToGetMatchingHCPsAndPublications(
          input,
          institutionsFilters,
          queryUnderstandingServiceResponse,
          indicationsParsedQuery,
          indicationsIcdCodesQuery
        )[1];

      return this.elasticSearchService.count({
        index: this.peopleIndexName,
        query
      });
    });

    const responses = await Promise.all(queries);

    const counts = responses.map((response) => response.count ?? 0);

    matchedHCPSCountMap = zipObject(institutionIds, counts);

    this.logger.info(
      matchedHCPSCountMap,
      "elasticsearch search counts for matching affiliated HCPs without IE"
    );

    return matchedHCPSCountMap;
  }

  private async getMatchingHCPForIE(
    input: Readonly<InstitutionsSearchInput>,
    iolSearchResponse: Readonly<
      estypes.SearchResponse<ElasticsearchInstitutionDoc>
    >
  ): Promise<PeopleSearchInclusionExclusionAggregationResponse | null> {
    if (!input.paging?.limit) {
      return null;
    }
    const institutionIds: number[] = iolSearchResponse.hits.hits
      .map((hit) => {
        return hit._source!.masterOrganizationId;
      })
      .filter((id) => !!id) as number[];

    const institutionsFilters: QueryDslQueryContainer = {
      nested: {
        path: "affiliations",
        query: {
          bool: {
            filter: buildAffiliationFiltersForMatchingHcp(
              INSTITUTION_ID_FIELD_IN_PEOPLE,
              institutionIds
            )
          }
        }
      }
    };

    const searchRequestBodyForIE =
      this.buildSearchRequestBodyToGetMatchingHCPsForInclusionExclusion(
        input,
        institutionsFilters
      );

    const iolFilters: QueryDslQueryContainer[] =
      buildAffiliationFiltersForMatchingHcp(
        INSTITUTION_ID_FIELD_IN_PEOPLE,
        institutionIds
      );

    const filteredAggregationOverInstitutionIds = {
      filtered_institutions: {
        nested: {
          path: "affiliations"
        },
        aggs: {
          filtered_ids: {
            filter: {
              bool: {
                filter: iolFilters
              }
            },
            aggs: {
              by_institution: {
                terms: {
                  field: INSTITUTION_ID_FIELD_IN_PEOPLE,
                  size: input.paging?.limit ?? 15
                },
                aggs: {
                  to_parent: {
                    reverse_nested: {}
                  }
                }
              }
            }
          }
        }
      }
    };

    const searchRequest: SearchRequest = {
      index: this.peopleIndexName,
      _source: false,
      query: searchRequestBodyForIE,
      aggs: filteredAggregationOverInstitutionIds
    };

    this.logger.info(
      { query: JSON.stringify(searchRequest) },
      "elasticsearch search request to get matching HCPs when inclusion/exclusion criteria applied"
    );
    const searchResponse =
      (await this.elasticSearchService.query<PeopleSearchInclusionExclusionAggregationResponse>(
        searchRequest
      )) as unknown as PeopleSearchInclusionExclusionAggregationResponse;

    return searchResponse;
  }

  private async getMatchingAffiliatedHCPCountsForIE(
    input: Readonly<InstitutionsSearchInput>,
    iolSearchResponse: Readonly<
      estypes.SearchResponse<ElasticsearchInstitutionDoc>
    >
  ): Promise<Dictionary<number>> {
    let matchedHCPSCountMap: Dictionary<number> = {};
    const institutionIds: number[] = iolSearchResponse.hits.hits
      .map((hit) => {
        return hit._source!.masterOrganizationId;
      })
      .filter((id) => !!id) as number[];

    const affiliatedPeopleIolsIds =
      input.filters?.affiliatedPeopleIolsIds ?? [];

    const searchRequests = institutionIds.map((id) => {
      const institutionsFilters: QueryDslQueryContainer = {
        bool: {
          filter: buildAffiliationFiltersForMatchingIOLAffiliatedHcps(
            INSTITUTION_ID_FIELD_IN_PEOPLE,
            [id, ...this.affiliatedPeopleIolsIdsToInt(affiliatedPeopleIolsIds)]
          )
        }
      };

      const searchRequestBodyForIE =
        this.buildSearchRequestBodyToGetMatchingHCPsForInclusionExclusion(
          input,
          institutionsFilters
        );

      const searchRequest: SearchRequest = {
        index: this.peopleIndexName,
        query: searchRequestBodyForIE
      };

      return this.elasticSearchService.count(searchRequest);
    });

    const responses = await Promise.all(searchRequests);

    const counts = responses.map((response) => response.count ?? 0);

    matchedHCPSCountMap = zipObject(institutionIds, counts);

    this.logger.info(
      matchedHCPSCountMap,
      "elasticsearch search counts for matching affiliated HCPs with IE"
    );

    return matchedHCPSCountMap;
  }

  private buildTrialFilters(
    input: InstitutionsSearchInput,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse,
    indicationsParsedQuery?: string
  ): {
    trialsLevelFilters: estypes.QueryDslQueryContainer[];
    anyTrialsFilterAppliedExcludingQuery: boolean;
  } {
    const isQueryAnInstitution = this.isQueryAnInstitution(
      input,
      queryUnderstandingServiceResponse
    );
    const searchQuery = getSynonymizedQuery(
      input.query,
      queryUnderstandingServiceResponse
    );
    const queryIndicationSeparator = searchQuery?.length ? "|" : EMPTY_STRING;
    const searchQueryWithIndications = searchQuery?.concat(
      indicationsParsedQuery
        ? queryIndicationSeparator + indicationsParsedQuery
        : EMPTY_STRING
    );

    const trialsLevelFilters: QueryDslQueryContainer[] = [];

    //This includes all trial filters except min/max count
    if (input.filters?.trialsFilters) {
      trialsLevelFilters.push(
        ...this.buildElasticsearchFiltersFromTrialsFilters(
          input.filters.trialsFilters
        )
      );
    }

    const anyTrialsFilterAppliedExcludingQuery = trialsLevelFilters.length > 0;

    //Query added as a simple_query_string filter
    if (searchQueryWithIndications?.length && !isQueryAnInstitution) {
      trialsLevelFilters.push({
        simple_query_string: toSimpleQueryString({
          path: "trials",
          fields: ["trials_info"],
          searchQuery: searchQueryWithIndications
        })
      });
    }

    return { trialsLevelFilters, anyTrialsFilterAppliedExcludingQuery };
  }

  private buildSearchRequestForTrialsSortAndFilter(
    input: InstitutionsSearchInput,
    originalInput: InstitutionsSearchInput,
    featureFlags: InstitutionSearchFeatureFlags,
    isBulkSearch: boolean,
    extraFilters: ReadonlyArray<QueryDslQueryContainer> = [],
    resolvedIndication: string[],
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse,
    indicationsParsedQuery?: string,
    indicationsIcdCodesQuery?: string,
    bothParserResponse?: BothQueryParseResult,
    icdCodesForCareClusterSelected?: string[]
  ): estypes.SearchRequest {
    const institutionLevelFilters = buildElasticsearchFiltersFromInputFilters(
      input,
      featureFlags,
      extraFilters,
      resolvedIndication,
      bothParserResponse
    );

    const isDiversitySort = input.sortBy === InstitutionSortOptions.DIVERSITY;

    const searchQueryForDiagnoses = input.query
      ? constructDiagnosesQueryString(
          input.query,
          queryUnderstandingServiceResponse
        )
      : EMPTY_STRING;
    const searchQuery = getSynonymizedQuery(
      input.query,
      queryUnderstandingServiceResponse
    );
    const queryIndicationSeparator = searchQuery?.length ? "|" : EMPTY_STRING;
    const searchQueryWithIndications = searchQuery?.concat(
      indicationsParsedQuery
        ? queryIndicationSeparator + indicationsParsedQuery
        : EMPTY_STRING
    );
    const defaultIndicationsParsedQuery = indicationsParsedQuery
      ? queryIndicationSeparator + indicationsParsedQuery
      : EMPTY_STRING;
    const searchQueryWithIndicationIcdCodes = searchQueryForDiagnoses?.concat(
      indicationsIcdCodesQuery
        ? queryIndicationSeparator + indicationsIcdCodesQuery
        : defaultIndicationsParsedQuery
    );

    const isQueryAnInstitution = this.isQueryAnInstitution(
      input,
      queryUnderstandingServiceResponse
    );

    const { trialsLevelFilters, anyTrialsFilterAppliedExcludingQuery } =
      this.buildTrialFilters(
        input,
        queryUnderstandingServiceResponse,
        indicationsParsedQuery
      );

    const shoulds: QueryDslQueryContainer[] = [];

    const sortByMatchingTrials =
      this.shouldSortResultsByMatchingTrialsCount(input);

    if (
      !searchQueryWithIndications?.length &&
      input.exportsSelections?.matchedClaimsDetails
    ) {
      let diagnosesPath: nestedPath = "diagnoses";
      let diagnosesCountField = internalCountFieldForNestedClaimsQuery(
        input.filters!.claimsFilters!,
        "diagnoses",
        featureFlags
      );

      let diagnosesFields = ["description_eng", "code_eng"];

      if (isEuropeanDiversitySort(input.sortBy)) {
        const europeanDiagnosesFields = getEuropeanDiagnosesFields(
          input.sortBy
        );
        if (europeanDiagnosesFields) {
          diagnosesPath = europeanDiagnosesFields.diagnosesPath;
          diagnosesCountField = europeanDiagnosesFields.diagnosesCountField;
          diagnosesFields = ["code", "description"];
        }
      }

      const diagnosesICDFilterApplied =
        input.filters?.claimsFilters?.diagnosesICD;
      let diagnosesCodesToCheck: string[] = [];
      if (
        diagnosesICDFilterApplied?.length ||
        !!icdCodesForCareClusterSelected?.length
      ) {
        diagnosesCodesToCheck = (
          diagnosesICDFilterApplied?.map(extractClaimsCode) ?? []
        ).concat(icdCodesForCareClusterSelected ?? []);
      }
      if (diagnosesCodesToCheck.length) {
        const diagnosesDetailsQueryArguments: QueryBuilderArguments = {
          path: diagnosesPath,
          fields: diagnosesFields,
          searchQuery: diagnosesCodesToCheck.join("|"),
          innerHits: {
            name: "diagnoses_collection",
            _source: true,
            docvalue_fields: [diagnosesCountField],
            sort: [
              {
                [diagnosesCountField]: {
                  order: "desc"
                }
              }
            ],
            size: 5
          },
          analyzer: CLAIMS_DESCRIPTION_ANALYZER
        };
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            diagnosesDetailsQueryArguments,
            featureFlags
          )
        );
      }
      const proceduresCountField = internalCountFieldForNestedClaimsQuery(
        input.filters!.claimsFilters!,
        "procedures",
        featureFlags
      );
      const proceduresCPTFilterApplied =
        input.filters?.claimsFilters?.proceduresCPT;
      if (proceduresCPTFilterApplied?.length) {
        const procedureDetailsQueryArguments: QueryBuilderArguments = {
          path: "procedures",
          fields: ["description_eng", "code_eng"],
          searchQuery: proceduresCPTFilterApplied!
            .map(extractClaimsCode)
            .join("|"),
          innerHits: {
            name: "procedures_collection",
            _source: true,
            docvalue_fields: [proceduresCountField],
            sort: [
              {
                [proceduresCountField]: {
                  order: "desc"
                }
              }
            ],
            size: 5
          },
          analyzer: CLAIMS_DESCRIPTION_ANALYZER
        };
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            procedureDetailsQueryArguments,
            featureFlags
          )
        );
      }
      if (
        !diagnosesCodesToCheck.length &&
        !proceduresCPTFilterApplied?.length
      ) {
        shoulds.push(
          ...toNestedMatchAllClaimsQuery(featureFlags, input.sortBy)
        );
      }
    }
    const musts: QueryDslQueryContainer[] = [];

    if (searchQueryWithIndications?.length && !isQueryAnInstitution) {
      shoulds.push(
        ...this.buildShouldsForKeywordSearch(
          searchQueryWithIndications,
          featureFlags,
          sortByMatchingTrials,
          false,
          input,
          originalInput,
          searchQueryWithIndicationIcdCodes,
          icdCodesForCareClusterSelected
        )
      );
    }

    if (this.inputHasPatientClaimsFilter(input, featureFlags)) {
      musts.push(...this.patientPopulationHasChildQuery(input, featureFlags));
    } else if (
      input.sortBy === InstitutionSortOptions.PATIENT_COUNT &&
      !searchQueryWithIndications?.length &&
      input.filters?.claimsFilters?.showUniquePatients
    ) {
      shoulds.push(...this.patientPopulationHasChildQuery(input, featureFlags));
    }

    const isTrialFilterPresentIncludingQuery = trialsLevelFilters.length > 0;
    const scriptForMinMaxCount = this.buildScriptForMinMaxTrialsCount(
      input,
      isDiversitySort,
      isTrialFilterPresentIncludingQuery,
      featureFlags
    );

    const trialsFunctionScoreQuery: QueryDslQueryContainer = {
      function_score: {
        query: isTrialFilterPresentIncludingQuery
          ? this.toNestedConstantScoreFilterQuery("trials", trialsLevelFilters)
          : MATCH_ALL
      }
    };
    const isMinMaxTrialCountSupplied: boolean =
      !_.isUndefined(input.filters?.trialsFilters?.minCount) ||
      !_.isUndefined(input.filters?.trialsFilters?.maxCount);
    if (isMinMaxTrialCountSupplied) {
      const defaultMin = input.filters?.trialsFilters?.maxCount === 0 ? 0 : 1;
      trialsFunctionScoreQuery!.function_score!.boost_mode! = "replace";
      trialsFunctionScoreQuery!.function_score!.functions! = [
        {
          script_score: {
            script: {
              params: {
                minValue: input.filters?.trialsFilters?.minCount ?? defaultMin,
                maxValue: input.filters?.trialsFilters?.maxCount,
                saturation_param: isDiversitySort
                  ? TRIALS_SATURATION
                  : undefined
              },
              source: scriptForMinMaxCount
            }
          }
        }
      ];

      trialsFunctionScoreQuery!.function_score!.min_score! = isDiversitySort
        ? MIN_DIVERSITY_SCORE
        : 1;
    } else if (isDiversitySort) {
      const raceFilterValues = input.filters?.diversityFilters?.race;
      const diversityWeight = buildDiversityRaceRankingScript(raceFilterValues);
      const weightScriptWithRace = `saturation(_score, params['saturation_param']) * ${diversityWeight}`;
      const weightScriptNoRace = `${SCRIPT_IS_DIVERSITY_ZERO_OR_NULL}? saturation(_score, params['saturation_param']) * ${diversityWeight}: 0`;
      trialsFunctionScoreQuery!.function_score!.boost_mode! = "replace";
      trialsFunctionScoreQuery!.function_score!.functions! = [
        {
          script_score: {
            script: {
              params: {
                saturation_param: isDiversitySort
                  ? TRIALS_SATURATION
                  : undefined
              },
              source: raceFilterValues?.length
                ? weightScriptWithRace
                : weightScriptNoRace
            }
          }
        }
      ];
    }
    const shouldUseTrialsCountFromRootLevel =
      input.sortBy === InstitutionSortOptions.TRIALS &&
      !isTrialFilterPresentIncludingQuery;
    const [isAgeSort, ageRangesSelected, ageSortWeight] =
      getAgeSortValues(input);
    let minimum_should_match = 0;

    if (!anyTrialsFilterAppliedExcludingQuery && !isMinMaxTrialCountSupplied) {
      shoulds.push(trialsFunctionScoreQuery);
      minimum_should_match = 1;
    } else if (isAgeSort) {
      institutionLevelFilters.push(trialsFunctionScoreQuery);
      musts.push(buildAgeSortQuery(ageRangesSelected, ageSortWeight));
    } else if (
      input.sortBy === InstitutionSortOptions.PATIENT_COUNT ||
      isEuropeanDiversitySort(input.sortBy)
    ) {
      institutionLevelFilters.push(trialsFunctionScoreQuery);
    } else {
      musts.push(trialsFunctionScoreQuery);
    }
    const functions: Array<QueryDslFunctionScoreContainer> = [];
    if (isQueryAnInstitution) {
      musts.push({
        match_phrase: {
          name: input.query
        }
      });
    } else if (musts.length == 0) {
      musts.push({
        match_all: {}
      });
    }
    //When there is no query or filter to search within trials object, we want to use global trial count when trial sort is applied
    if (shouldUseTrialsCountFromRootLevel)
      functions.push({
        field_value_factor: {
          field: "trials_count",
          missing: 0
        }
      });

    const request: estypes.SearchRequest = {
      index: this.institutionsIndexName,
      track_total_hits: true,
      _source: {
        include: !isBulkSearch
          ? SOURCE_INCLUDES
          : SOURCE_INCLUDES_FOR_BULK_ENTITY_SEARCH
      },
      query: {
        function_score: {
          query: {
            bool: {
              must: musts,
              should: shoulds.length ? shoulds : undefined,
              minimum_should_match,
              filter: institutionLevelFilters
            }
          },
          functions: functions.length ? functions : undefined
        }
      }
    };

    if (input.paging?.limit != null && input.paging.limit >= 0) {
      request.size = input.paging.limit;
    }
    if (input.paging?.offset) {
      request.from = input.paging.offset;
    }

    return request;
  }

  private buildScriptForMinMaxTrialsCount(
    input: InstitutionsSearchInput,
    isDiversitySort: boolean,
    isTrialFilterPresent: boolean,
    featureFlags: InstitutionSearchFeatureFlags
  ): string {
    const minMaxConditions: string[] = [];
    const raceFilterValues = input.filters?.diversityFilters?.race;
    const isRaceFilterPresent = raceFilterValues?.length;
    const weight = buildDiversityRaceRankingScript(raceFilterValues);
    const scoreValue = isTrialFilterPresent
      ? "_score"
      : "(doc['trials_count'].size()!=0?doc['trials_count'].value:0)";

    minMaxConditions.push(`(${scoreValue} >= params.minValue)`);
    //We are adding a default score of 1 when maxCount is 0 so that institutions with 0 trials can qualify the min_score 1 clause
    let addedScore = "";
    if (!_.isUndefined(input.filters?.trialsFilters?.maxCount)) {
      minMaxConditions.push(`(${scoreValue} <= params.maxValue)`);
      if (input.filters?.trialsFilters?.maxCount === 0) {
        addedScore = " +1";
      }
    }
    const scoreScript = isDiversitySort
      ? `saturation(${scoreValue}${addedScore}, params['saturation_param']) * ${weight}`
      : `${scoreValue}${addedScore}`;
    const diversityCheckScript = isRaceFilterPresent
      ? `${weight} > 0`
      : `${SCRIPT_IS_DIVERSITY_ZERO_OR_NULL}`;
    const minMaxConditionScript = `${minMaxConditions.join("&&")}`;
    const nonDiversitySortWithMinMaxConditionScript = `${minMaxConditionScript}? ${scoreScript}:0`;
    const diversitySortScript = `(${diversityCheckScript} ? ${scoreScript} : ${MIN_DIVERSITY_SCORE})`;
    const diversitySortWithMinMaxConditionScript = `${minMaxConditionScript}? ${diversitySortScript}:0`;

    const finalScoreScript = isDiversitySort
      ? diversitySortWithMinMaxConditionScript
      : nonDiversitySortWithMinMaxConditionScript;

    return finalScoreScript;
  }

  private buildKeywordSearchRequest(
    input: Readonly<InstitutionsSearchInput>,
    originalInput: Readonly<InstitutionsSearchInput>,
    featureFlags: InstitutionSearchFeatureFlags,
    isBulkSearch: boolean,
    requestType: RequestType,
    extraFilters: ReadonlyArray<QueryDslQueryContainer>,
    resolvedIndication: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    indicationsParsedQuery?: string,
    indicationsIcdCodesQuery?: string,
    bothParserResponse?: BothQueryParseResult,
    icdCodesForCareClusterSelected?: string[]
  ): estypes.SearchRequest {
    const institutionLevelFilters = buildElasticsearchFiltersFromInputFilters(
      input,
      featureFlags,
      extraFilters,
      resolvedIndication,
      bothParserResponse
    );

    const searchQueryForDiagnoses = input.query
      ? constructDiagnosesQueryString(
          input.query,
          queryUnderstandingServiceResponse
        )
      : EMPTY_STRING;

    const searchQuery =
      getSynonymizedQuery(input.query, queryUnderstandingServiceResponse) ??
      EMPTY_STRING;
    const queryIndicationSeparator = searchQuery?.length ? "|" : EMPTY_STRING;
    const searchQueryWithIndications = searchQuery?.concat(
      indicationsParsedQuery
        ? queryIndicationSeparator + indicationsParsedQuery
        : EMPTY_STRING
    );
    const defaultIndicationsParsedQuery = indicationsParsedQuery
      ? queryIndicationSeparator + indicationsParsedQuery
      : EMPTY_STRING;
    const searchQueryWithIndicationIcdCodes = searchQueryForDiagnoses?.concat(
      indicationsIcdCodesQuery
        ? queryIndicationSeparator + indicationsIcdCodesQuery
        : defaultIndicationsParsedQuery ?? EMPTY_STRING
    );
    const shoulds: QueryDslQueryContainer[] = [];
    const musts: QueryDslQueryContainer[] = [];
    if (searchQueryWithIndications?.length) {
      shoulds.push(
        ...this.buildShouldsForKeywordSearch(
          searchQueryWithIndications,
          featureFlags,
          false,
          true,
          input,
          originalInput,
          searchQueryWithIndicationIcdCodes,
          icdCodesForCareClusterSelected
        )
      );
    }

    if (
      this.inputHasPatientClaimsFilter(input, featureFlags) &&
      requestType === "iol"
    ) {
      musts.push(...this.patientPopulationHasChildQuery(input, featureFlags));
    } else if (
      input.sortBy === InstitutionSortOptions.PATIENT_COUNT &&
      !searchQueryWithIndications.length &&
      input.filters?.claimsFilters?.showUniquePatients
    ) {
      shoulds.push(...this.patientPopulationHasChildQuery(input, featureFlags));
    }

    const [isAgeSort, ageRangesSelected, ageSortWeight] =
      getAgeSortValues(input);
    const isTrialPerformanceSort =
      input.sortBy === InstitutionSortOptions.TRIAL_PERFORMANCE;

    let query: QueryDslQueryContainer;
    if (isTrialPerformanceSort) {
      query = {
        function_score: {
          query: {
            bool: {
              must: musts.length ? musts : undefined,
              should: shoulds.length ? shoulds : undefined,
              minimum_should_match: shoulds.length
                ? AT_LEAST_ONE_ASSET
                : ALL_ASSETS_OPTIONAL,
              filter: institutionLevelFilters
            }
          },
          functions: [
            ...SCORING_FUNCTIONS_FOR_TRIAL_PERFORMANCE_RANKING_WITHOUT_TRIAL_COUNT,
            ...SCORING_FUNCTIONS_FOR_PENALIZING_AUTO_CREATED_IOL
          ]
        }
      };
    } else {
      if (isAgeSort) {
        musts.push(buildAgeSortQuery(ageRangesSelected, ageSortWeight));
      }
      query = {
        bool: {
          must: musts.length ? musts : undefined,
          should: shoulds.length ? shoulds : undefined,
          minimum_should_match: shoulds.length
            ? AT_LEAST_ONE_ASSET
            : ALL_ASSETS_OPTIONAL,
          filter: institutionLevelFilters
        }
      };

      if (featureFlags.enablePenalizeAutoCreatedIOLs) {
        query = {
          function_score: {
            query: {
              bool: {
                must: musts.length ? musts : undefined,
                should: shoulds.length ? shoulds : undefined,
                minimum_should_match: shoulds.length
                  ? AT_LEAST_ONE_ASSET
                  : ALL_ASSETS_OPTIONAL,
                filter: institutionLevelFilters
              }
            },
            functions: [...SCORING_FUNCTIONS_FOR_PENALIZING_AUTO_CREATED_IOL]
          }
        };
      }
    }

    const request: estypes.SearchRequest = {
      index: this.institutionsIndexName,
      track_total_hits: true,
      _source: {
        include: !isBulkSearch
          ? SOURCE_INCLUDES
          : SOURCE_INCLUDES_FOR_BULK_ENTITY_SEARCH
      },
      query
    };

    if (input.paging?.limit != null && input.paging.limit >= 0) {
      request.size = input.paging.limit;
    }
    if (input.paging?.offset) {
      request.from = input.paging.offset;
    }

    return request;
  }

  buildShouldsForKeywordSearch(
    searchQuery: string,
    featureFlags: InstitutionSearchFeatureFlags,
    sortByMatchingTrials: boolean,
    addTrialsToShoulds = false,
    input: InstitutionsSearchInput,
    originalInput: Readonly<InstitutionsSearchInput>,
    searchQueryForDiagnoses?: string,
    icdCodesForCareClusterSelected?: string[]
  ) {
    const {
      enableEnhancedHasCTMSDataFilterBehavior,
      enableNewIolH1DefaultRanking
    } = featureFlags;
    const diagnosesICDFilterApplied =
      input?.filters?.claimsFilters?.diagnosesICD;

    const ccsrIcdFilterApplied = !!input?.filters?.claimsFilters?.ccsr?.length;
    const proceduresCPTFilterApplied =
      input?.filters?.claimsFilters?.proceduresCPT;
    let innerHitsForDiagnoses = getNestedInnerHitsForClaims(
      "diagnoses",
      featureFlags,
      input?.filters?.claimsFilters ?? undefined
    );

    const isSortByNewH1DefaultRanking =
      enableNewIolH1DefaultRanking &&
      input.sortBy === InstitutionSortOptions.H1_DEFAULT;

    let diagnosesPath: nestedPath = "diagnoses";
    let diagnosesCountField = `diagnoses.${innerHitsForDiagnoses}`;

    let diagnosesFields = ["description_eng", "code_eng"];

    if (isEuropeanDiversitySort(input.sortBy)) {
      const europeanDiagnosesFields = getEuropeanDiagnosesFields(input.sortBy);
      if (europeanDiagnosesFields) {
        diagnosesPath = europeanDiagnosesFields.diagnosesPath;
        diagnosesCountField = europeanDiagnosesFields.diagnosesCountField;
        diagnosesFields = ["code", "description"];
        innerHitsForDiagnoses = "count";
      }
    }

    const innerHitsForProcedures = getNestedInnerHitsForClaims(
      "procedures",
      featureFlags,
      input?.filters?.claimsFilters ?? undefined
    );
    const innerHitsForPrescriptions = getNestedInnerHitsForClaims(
      "prescriptions",
      featureFlags,
      input?.filters?.claimsFilters ?? undefined
    );
    const shoulds: QueryDslQueryContainer[] = [];

    const diagnosesQueryArguments: ClaimsQueryBuilderArguments = {
      path: diagnosesPath,
      fields: diagnosesFields,
      searchQuery: searchQueryForDiagnoses,
      innerHits: {
        _source: false,
        docvalue_fields: [diagnosesCountField],
        size: 1000
      },
      analyzer: CLAIMS_DESCRIPTION_ANALYZER,
      saturation: DIAGNOSES_SATURATION,
      raceValues: input?.filters?.diversityFilters?.race ?? undefined,
      countField: innerHitsForDiagnoses
    };

    const proceduresQueryArguments: ClaimsQueryBuilderArguments = {
      path: "procedures",
      fields: ["description_eng", "code_eng"],
      searchQuery,
      innerHits: {
        _source: false,
        docvalue_fields: [`procedures.${innerHitsForProcedures}`],
        size: 1000
      },
      analyzer: CLAIMS_DESCRIPTION_ANALYZER,
      saturation: PROCEDURES_SATURATION,
      raceValues: input?.filters?.diversityFilters?.race ?? undefined,
      countField: innerHitsForProcedures
    };
    const prescriptionsQueryArguments: ClaimsQueryBuilderArguments = {
      path: "prescriptions",
      fields: ["generic_name.text"],
      searchQuery: searchQuery,
      innerHits: {
        _source: false,
        docvalue_fields: [`prescriptions.${innerHitsForPrescriptions}`],
        size: 1000
      },
      analyzer: CLAIMS_DESCRIPTION_ANALYZER,
      saturation: PRESCRIPTIONS_SATURATION,
      raceValues: input?.filters?.diversityFilters?.race ?? undefined,
      countField: innerHitsForPrescriptions
    };
    const congressesQueryArguments: QueryBuilderArguments = {
      path: "congresses",
      fields: ["congress_info_eng"],
      searchQuery,
      innerHits: {
        _source: false
      }
    };
    const paymentsQueryArguments: QueryBuilderArguments = {
      path: "payments",
      fields: ["payment_info"],
      searchQuery,
      innerHits: {
        _source: false,
        docvalue_fields: ["payments.amount"],
        size: 10000
      }
    };
    const trialsQueryArguments: QueryBuilderArguments = {
      path: "trials",
      fields: ["trials_info"],
      searchQuery,
      innerHits: {
        _source: false
      },
      saturation: TRIALS_SATURATION,
      raceValues: input?.filters?.diversityFilters?.race ?? undefined
    };

    if (originalInput.filters?.indications?.length) {
      trialsQueryArguments.fields = ["kgIndications"];
      trialsQueryArguments.searchQuery = originalInput.filters.indications
        .map((indication) => `"${indication}"`)
        .join("|");
    }

    const diagnosesDetailsQueryArguments: ClaimsQueryBuilderArguments = {
      path: diagnosesPath,
      fields: diagnosesFields,
      searchQuery: searchQueryForDiagnoses,
      innerHits: {
        name: "diagnoses_collection",
        _source: true,
        docvalue_fields: [diagnosesCountField],
        sort: [
          {
            [diagnosesCountField]: {
              order: "desc"
            }
          }
        ],
        size: 5
      },
      analyzer: CLAIMS_DESCRIPTION_ANALYZER,
      countField: innerHitsForDiagnoses
    };
    if (
      diagnosesICDFilterApplied?.length ||
      !!icdCodesForCareClusterSelected?.length
    ) {
      const diagnosesCodesToCheck = (
        diagnosesICDFilterApplied?.map(extractClaimsCode) ?? []
      ).concat(icdCodesForCareClusterSelected ?? []);
      diagnosesDetailsQueryArguments.searchQuery =
        diagnosesCodesToCheck!.join("|");
    }

    const procedureDetailsQueryArguments: ClaimsQueryBuilderArguments = {
      path: "procedures",
      fields: ["description_eng", "code_eng"],
      searchQuery,
      innerHits: {
        name: "procedures_collection",
        _source: true,
        docvalue_fields: [`procedures.${innerHitsForProcedures}`],
        sort: [
          {
            [`procedures.${innerHitsForProcedures}`]: {
              order: "desc"
            }
          }
        ],
        size: 5
      },
      analyzer: CLAIMS_DESCRIPTION_ANALYZER,
      countField: innerHitsForProcedures
    };
    if (proceduresCPTFilterApplied?.length) {
      procedureDetailsQueryArguments.searchQuery = proceduresCPTFilterApplied!
        .map(extractClaimsCode)
        .join("|");
    }

    let shouldAddDefaultNestedDiagnosis = true;
    if (sortByMatchingTrials) {
      if (input?.filters?.claimsFilters?.showUniquePatients) {
        shouldAddDefaultNestedDiagnosis = false;
        if (
          !this.inputHasPatientClaimsFilter(input, featureFlags) &&
          !diagnosesICDFilterApplied &&
          !ccsrIcdFilterApplied
        ) {
          shoulds.push(
            ...this.patientPopulationHasChildQuery(input, featureFlags)
          );
        } else if (!this.inputHasPatientClaimsFilter(input, featureFlags)) {
          shouldAddDefaultNestedDiagnosis = true;
        }
      }

      if (
        shouldAddDefaultNestedDiagnosis &&
        !input?.filters?.claimsFilters?.diagnosesICD?.length &&
        !ccsrIcdFilterApplied
      ) {
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            diagnosesQueryArguments,
            featureFlags
          )
        );
      }

      if (!input?.filters?.claimsFilters?.proceduresCPT?.length) {
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            proceduresQueryArguments,
            featureFlags
          )
        );
      }

      if (!input.filters?.claimsFilters?.genericNames?.length) {
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            prescriptionsQueryArguments,
            featureFlags
          )
        );
      }
      shoulds.push(
        toNestedConstantScoreQuery(congressesQueryArguments),
        toNestedConstantScoreQuery(paymentsQueryArguments)
      );

      if (input?.exportsSelections?.matchedClaimsDetails) {
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            diagnosesDetailsQueryArguments,
            featureFlags
          ),
          toNestedClaimsConstantScoreQuery(
            procedureDetailsQueryArguments,
            featureFlags
          )
        );
      }
    } else if (input?.sortBy == InstitutionSortOptions.DIVERSITY) {
      let shouldAddDefaultNestedDiagnosis = true;
      if (input?.filters?.claimsFilters?.showUniquePatients) {
        shouldAddDefaultNestedDiagnosis = false;
        if (
          !this.inputHasPatientClaimsFilter(input, featureFlags) &&
          !diagnosesICDFilterApplied &&
          !ccsrIcdFilterApplied
        ) {
          shoulds.push(
            ...this.patientPopulationHasChildQuery(input, featureFlags)
          );
        } else if (!this.inputHasPatientClaimsFilter(input, featureFlags)) {
          shouldAddDefaultNestedDiagnosis = true;
        }
      }

      if (
        shouldAddDefaultNestedDiagnosis &&
        !input?.filters?.claimsFilters?.diagnosesICD?.length &&
        !ccsrIcdFilterApplied
      ) {
        shoulds.push(
          this.toNestedClaimsFunctionScoreQueryForDiversity(
            diagnosesQueryArguments,
            featureFlags
          )
        );
      }

      if (!input?.filters?.claimsFilters?.proceduresCPT?.length) {
        shoulds.push(
          this.toNestedClaimsFunctionScoreQueryForDiversity(
            proceduresQueryArguments,
            featureFlags
          )
        );
      }

      if (!input?.filters?.claimsFilters?.genericNames?.length) {
        shoulds.push(
          this.toNestedClaimsFunctionScoreQueryForDiversity(
            prescriptionsQueryArguments,
            featureFlags
          )
        );
      }

      if (addTrialsToShoulds) {
        shoulds.push(
          this.toNestedTrialsFunctionScoreQueryForDiversity(
            trialsQueryArguments,
            featureFlags
          )
        );
      }
      shoulds.push(
        toNestedConstantScoreQuery(congressesQueryArguments),
        toNestedConstantScoreQuery(paymentsQueryArguments)
      );

      if (input?.exportsSelections?.matchedClaimsDetails) {
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            diagnosesDetailsQueryArguments,
            featureFlags
          ),
          toNestedClaimsConstantScoreQuery(
            procedureDetailsQueryArguments,
            featureFlags
          )
        );
      }
    } else if (isEuropeanDiversitySort(input.sortBy)) {
      if (!input?.filters?.claimsFilters?.diagnosesICD?.length) {
        const europeanDiversityQuery = getEuropeanDiversityRankingQuery(
          input.sortBy,
          input.filters?.diversityFilters?.race,
          searchQueryForDiagnoses
        );

        if (europeanDiversityQuery) {
          shoulds.push(europeanDiversityQuery);
        }
      }

      if (!input?.filters?.claimsFilters?.proceduresCPT?.length) {
        shoulds.push(toNestedConstantScoreQuery(proceduresQueryArguments));
      }

      if (!input?.filters?.claimsFilters?.genericNames?.length) {
        shoulds.push(toNestedConstantScoreQuery(prescriptionsQueryArguments));
      }

      if (addTrialsToShoulds) {
        shoulds.push(toNestedConstantScoreQuery(trialsQueryArguments));
      }
      shoulds.push(
        toNestedConstantScoreQuery(congressesQueryArguments),
        toNestedConstantScoreQuery(paymentsQueryArguments)
      );

      if (input?.exportsSelections?.matchedClaimsDetails) {
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            diagnosesDetailsQueryArguments,
            featureFlags
          ),
          toNestedClaimsConstantScoreQuery(
            procedureDetailsQueryArguments,
            featureFlags
          )
        );
      }
    } else if (input?.sortBy == InstitutionSortOptions.AGE) {
      shoulds.push(
        ...this.buildZeroWeightShouldsForKeywordSearch(
          diagnosesQueryArguments,
          proceduresQueryArguments,
          prescriptionsQueryArguments,
          congressesQueryArguments,
          paymentsQueryArguments,
          trialsQueryArguments,
          diagnosesDetailsQueryArguments,
          procedureDetailsQueryArguments,
          featureFlags,
          addTrialsToShoulds,
          input
        )
      );
    } else if (input?.sortBy === InstitutionSortOptions.TRIAL_PERFORMANCE) {
      shoulds.push(
        ...this.buildZeroWeightShouldsForKeywordSearch(
          diagnosesQueryArguments,
          proceduresQueryArguments,
          prescriptionsQueryArguments,
          congressesQueryArguments,
          paymentsQueryArguments,
          trialsQueryArguments,
          diagnosesDetailsQueryArguments,
          procedureDetailsQueryArguments,
          featureFlags,
          addTrialsToShoulds,
          input
        )
      );
    } else if (input?.sortBy === InstitutionSortOptions.PATIENT_COUNT) {
      shoulds.push(
        ...this.buildPatientCountSortingShouldsForKeywordSearch(
          diagnosesQueryArguments,
          proceduresQueryArguments,
          prescriptionsQueryArguments,
          congressesQueryArguments,
          paymentsQueryArguments,
          trialsQueryArguments,
          diagnosesDetailsQueryArguments,
          procedureDetailsQueryArguments,
          featureFlags,
          addTrialsToShoulds,
          input
        )
      );
    } else if (
      input?.sortBy === InstitutionSortOptions.DIAGNOSES ||
      input?.sortBy === InstitutionSortOptions.PROCEDURES ||
      input?.sortBy === InstitutionSortOptions.PRESCRIPTIONS
    ) {
      shoulds.push(
        ...this.buildClaimsShouldsForKeywordSearch(
          diagnosesQueryArguments,
          proceduresQueryArguments,
          prescriptionsQueryArguments,
          congressesQueryArguments,
          paymentsQueryArguments,
          trialsQueryArguments,
          diagnosesDetailsQueryArguments,
          procedureDetailsQueryArguments,
          featureFlags,
          addTrialsToShoulds,
          input
        )
      );
    } else if (isSortByNewH1DefaultRanking) {
      shoulds.push(
        ...this.buildNewH1DefaultShouldsForKeywordSearch(
          diagnosesQueryArguments,
          trialsQueryArguments,
          paymentsQueryArguments,
          proceduresQueryArguments,
          prescriptionsQueryArguments,
          congressesQueryArguments,
          featureFlags,
          input
        )
      );
    } else {
      shoulds.push(
        ...this.buildDefaultShouldsForKeywordSearch(
          diagnosesQueryArguments,
          proceduresQueryArguments,
          prescriptionsQueryArguments,
          congressesQueryArguments,
          paymentsQueryArguments,
          trialsQueryArguments,
          diagnosesDetailsQueryArguments,
          procedureDetailsQueryArguments,
          featureFlags,
          addTrialsToShoulds,
          input
        )
      );
    }

    if (
      input?.sortBy === InstitutionSortOptions.TRIAL_PERFORMANCE ||
      (input?.filters?.hasCTMSData &&
        enableEnhancedHasCTMSDataFilterBehavior &&
        input?.sortBy === InstitutionSortOptions.DIVERSITY)
    ) {
      const ctmsTrialsQueryArguments: QueryBuilderArguments = {
        path: "trials",
        fields: ["trials_info"],
        searchQuery,
        innerHits: {
          name: "CTMSTrials",
          _source: false
        },
        boost: CTMS_TRIALS_BOOST
      };
      const ctmsTrialsQueryArgumentsForDiversity: QueryBuilderArguments = {
        ...ctmsTrialsQueryArguments,
        saturation: CTMS_TRIALS_SATURATION,
        raceValues: input?.filters?.diversityFilters?.race ?? undefined,
        boost: CTMS_TRIALS_BOOST_FOR_DIVERSITY
      };

      if (input?.sortBy === InstitutionSortOptions.DIVERSITY) {
        shoulds.push(
          this.toNestedCTMSTrialsFunctionScoreQueryForDiversity(
            ctmsTrialsQueryArgumentsForDiversity,
            featureFlags
          )
        );
      } else {
        shoulds.push(
          this.toNestedCTMSTrialsConstantScoreQuery(ctmsTrialsQueryArguments)
        );
      }
    }

    return shoulds;
  }

  private buildNewH1DefaultShouldsForKeywordSearch(
    diagnosesQueryArguments: ClaimsQueryBuilderArguments,
    trialsQueryArguments: QueryBuilderArguments,
    paymentsQueryArguments: QueryBuilderArguments,
    proceduresQueryArguments: ClaimsQueryBuilderArguments,
    prescriptionsQueryArguments: ClaimsQueryBuilderArguments,
    congressesQueryArguments: QueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags,
    input?: InstitutionsSearchInput
  ) {
    const shoulds: QueryDslQueryContainer[] = [];

    const diagnosesICDFilterApplied =
      input?.filters?.claimsFilters?.diagnosesICD?.length;
    const ccsrICDFilterApplied = !!input?.filters?.claimsFilters?.ccsr?.length;
    const isShowUniquePatientsFlagOn =
      input?.filters?.claimsFilters?.showUniquePatients;
    const isTrialFilterPresent = !!input?.filters?.trialsFilters;
    let shouldAddDefaultNestedDiagnosis = true;

    if (isShowUniquePatientsFlagOn) {
      shouldAddDefaultNestedDiagnosis = false;
      if (
        !this.inputHasPatientClaimsFilter(input, featureFlags) &&
        !diagnosesICDFilterApplied &&
        !ccsrICDFilterApplied
      ) {
        //Has child query added only when show unique patient is true
        const patientPopulationHasChildQuery =
          this.patientPopulationHasChildQuery(input, featureFlags);
        // toMultiQueryWrappedFunctionScore
        shoulds.push(
          this.toMultiQueryWrappedFunctionScore(
            patientPopulationHasChildQuery,
            "totalPatientDocs"
          )
        );
      } else if (!this.inputHasPatientClaimsFilter(input, featureFlags)) {
        shouldAddDefaultNestedDiagnosis = true;
      }
    }

    if (
      shouldAddDefaultNestedDiagnosis &&
      !diagnosesICDFilterApplied &&
      !ccsrICDFilterApplied
    ) {
      //drg_diagnoses query added only when show unique patient is false
      shoulds.push(
        this.toNestedClaimsFunctionScoreQueryForH1DefaultRanking(
          diagnosesQueryArguments,
          featureFlags
        )
      );
    }

    if (!isTrialFilterPresent) {
      shoulds.push(
        this.toNestedTrialsFunctionScoreQueryForH1DefaultRanking(
          trialsQueryArguments,
          featureFlags
        )
      );
    }

    shoulds.push(
      this.toNestedPaymentsFunctionScoreQueryForH1DefaultRanking(
        paymentsQueryArguments,
        featureFlags
      )
    );
    shoulds.push(
      this.toNestedTrialsInvestigatorFunctionScoreQueryForH1DefaultRanking(
        trialsQueryArguments,
        featureFlags
      )
    );

    //These below should clauses are added so that all the ranking have the same search space
    if (!input?.filters?.claimsFilters?.proceduresCPT?.length) {
      shoulds.push(
        toNestedClaimsConstantScoreQuery(proceduresQueryArguments, featureFlags)
      );
    }

    if (!input?.filters?.claimsFilters?.genericNames?.length) {
      shoulds.push(
        toNestedClaimsConstantScoreQuery(
          prescriptionsQueryArguments,
          featureFlags
        )
      );
    }

    shoulds.push(toNestedConstantScoreQuery(congressesQueryArguments));

    return shoulds;
  }

  private buildDefaultShouldsForKeywordSearch(
    diagnosesQueryArguments: ClaimsQueryBuilderArguments,
    proceduresQueryArguments: ClaimsQueryBuilderArguments,
    prescriptionsQueryArguments: ClaimsQueryBuilderArguments,
    congressesQueryArguments: QueryBuilderArguments,
    paymentsQueryArguments: QueryBuilderArguments,
    trialsQueryArguments: QueryBuilderArguments,
    diagnosesDetailsQueryArguments: ClaimsQueryBuilderArguments,
    procedureDetailsQueryArguments: ClaimsQueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags,
    addTrialsToShoulds = false,
    input?: InstitutionsSearchInput
  ) {
    const shoulds: QueryDslQueryContainer[] = [];
    const diagnosesICDFilterApplied =
      input?.filters?.claimsFilters?.diagnosesICD;
    const ccsrICDFilterApplied = !!input?.filters?.claimsFilters?.ccsr?.length;
    const proceduresCPTFilterApplied =
      input?.filters?.claimsFilters?.proceduresCPT;
    let shouldAddDefaultNestedDiagnosis = true;
    if (input?.filters?.claimsFilters?.showUniquePatients) {
      shouldAddDefaultNestedDiagnosis = false;
      if (
        input &&
        !this.inputHasPatientClaimsFilter(input, featureFlags) &&
        !diagnosesICDFilterApplied &&
        !ccsrICDFilterApplied
      ) {
        shoulds.push(
          ...this.patientPopulationHasChildQuery(input, featureFlags)
        );
      } else if (!this.inputHasPatientClaimsFilter(input, featureFlags)) {
        shouldAddDefaultNestedDiagnosis = true;
      }
    }
    if (
      shouldAddDefaultNestedDiagnosis &&
      !diagnosesICDFilterApplied?.length &&
      !ccsrICDFilterApplied
    ) {
      shoulds.push(
        this.toNestedClaimsFunctionScoreQuery(
          diagnosesQueryArguments,
          featureFlags
        )
      );
    }
    if (!proceduresCPTFilterApplied?.length) {
      shoulds.push(
        this.toNestedClaimsFunctionScoreQuery(
          proceduresQueryArguments,
          featureFlags
        )
      );
    }

    if (!input?.filters?.claimsFilters?.genericNames?.length) {
      shoulds.push(
        this.toNestedClaimsFunctionScoreQuery(
          prescriptionsQueryArguments,
          featureFlags
        )
      );
    }

    shoulds.push(
      this.toNestedSimpleQueryString(congressesQueryArguments),
      this.toNestedSimpleQueryString(paymentsQueryArguments)
    );

    if (addTrialsToShoulds) {
      shoulds.push(this.toNestedSimpleQueryString(trialsQueryArguments));
    }

    if (input?.exportsSelections?.matchedClaimsDetails) {
      shoulds.push(
        toNestedClaimsConstantScoreQuery(
          diagnosesDetailsQueryArguments,
          featureFlags
        ),
        toNestedClaimsConstantScoreQuery(
          procedureDetailsQueryArguments,
          featureFlags
        )
      );
    }

    if (input?.query) {
      shoulds.push({
        dis_max: {
          queries: [
            {
              match: {
                name: {
                  query: input.query,
                  operator: "AND",
                  analyzer: PUNCTUATION_SPLITTER_ANALYZER,
                  boost: NAME_HIT_BOOST
                }
              }
            },
            {
              match: {
                name: {
                  query: input.query,
                  operator: "AND",
                  analyzer: PUNCTUATION_REMOVER_ANALYZER,
                  boost: NAME_HIT_BOOST
                }
              }
            }
          ]
        }
      });
    }

    return shoulds;
  }

  private buildClaimsShouldsForKeywordSearch(
    diagnosesQueryArguments: ClaimsQueryBuilderArguments,
    proceduresQueryArguments: ClaimsQueryBuilderArguments,
    prescriptionsQueryArguments: ClaimsQueryBuilderArguments,
    congressesQueryArguments: QueryBuilderArguments,
    paymentsQueryArguments: QueryBuilderArguments,
    trialsQueryArguments: QueryBuilderArguments,
    diagnosesDetailsQueryArguments: ClaimsQueryBuilderArguments,
    procedureDetailsQueryArguments: ClaimsQueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags,
    addTrialsToShoulds = false,
    input?: InstitutionsSearchInput
  ) {
    const shoulds: QueryDslQueryContainer[] = [];
    const diagnosesICDFilterApplied =
      input?.filters?.claimsFilters?.diagnosesICD;
    const ccsrICDFilterApplied = !!input?.filters?.claimsFilters?.ccsr?.length;
    const proceduresCPTFilterApplied =
      input?.filters?.claimsFilters?.proceduresCPT;
    const prescriptionsFilterApplied =
      input?.filters?.claimsFilters?.genericNames;

    let shouldAddDefaultNestedDiagnosis = true;
    if (input?.filters?.claimsFilters?.showUniquePatients) {
      shouldAddDefaultNestedDiagnosis = false;
      if (
        input &&
        !this.inputHasPatientClaimsFilter(input, featureFlags) &&
        !diagnosesICDFilterApplied &&
        !ccsrICDFilterApplied
      ) {
        shoulds.push(
          ...this.patientPopulationHasChildQuery(input, featureFlags)
        );
      } else if (!this.inputHasPatientClaimsFilter(input, featureFlags)) {
        shouldAddDefaultNestedDiagnosis = true;
      }
    }
    if (
      shouldAddDefaultNestedDiagnosis &&
      !diagnosesICDFilterApplied?.length &&
      !ccsrICDFilterApplied
    ) {
      if (input?.sortBy === InstitutionSortOptions.DIAGNOSES) {
        shoulds.push(
          this.toNestedClaimsFunctionScoreQuery(
            diagnosesQueryArguments,
            featureFlags
          )
        );
      } else {
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            diagnosesQueryArguments,
            featureFlags
          )
        );
      }
    }
    if (!proceduresCPTFilterApplied?.length) {
      if (input?.sortBy === InstitutionSortOptions.PROCEDURES) {
        shoulds.push(
          this.toNestedClaimsFunctionScoreQuery(
            proceduresQueryArguments,
            featureFlags
          )
        );
      } else {
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            proceduresQueryArguments,
            featureFlags
          )
        );
      }
    }

    if (!prescriptionsFilterApplied?.length) {
      if (input?.sortBy === InstitutionSortOptions.PRESCRIPTIONS) {
        shoulds.push(
          this.toNestedClaimsFunctionScoreQuery(
            prescriptionsQueryArguments,
            featureFlags
          )
        );
      } else {
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            prescriptionsQueryArguments,
            featureFlags
          )
        );
      }
    }

    if (addTrialsToShoulds) {
      shoulds.push(toNestedConstantScoreQuery(trialsQueryArguments));
    }
    shoulds.push(
      toNestedConstantScoreQuery(congressesQueryArguments),
      toNestedConstantScoreQuery(paymentsQueryArguments)
    );

    if (input?.exportsSelections?.matchedClaimsDetails) {
      shoulds.push(
        toNestedClaimsConstantScoreQuery(
          diagnosesDetailsQueryArguments,
          featureFlags
        ),
        toNestedClaimsConstantScoreQuery(
          procedureDetailsQueryArguments,
          featureFlags
        )
      );
    }

    return shoulds;
  }

  private buildZeroWeightShouldsForKeywordSearch(
    diagnosesQueryArguments: ClaimsQueryBuilderArguments,
    proceduresQueryArguments: ClaimsQueryBuilderArguments,
    prescriptionsQueryArguments: ClaimsQueryBuilderArguments,
    congressesQueryArguments: QueryBuilderArguments,
    paymentsQueryArguments: QueryBuilderArguments,
    trialsQueryArguments: QueryBuilderArguments,
    diagnosesDetailsQueryArguments: ClaimsQueryBuilderArguments,
    procedureDetailsQueryArguments: ClaimsQueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags,
    addTrialsToShoulds = false,
    input?: InstitutionsSearchInput
  ) {
    const shoulds: QueryDslQueryContainer[] = [];

    const diagnosesICDFilterApplied =
      input?.filters?.claimsFilters?.diagnosesICD;
    const ccsrICDFilterApplied = !!input?.filters?.claimsFilters?.ccsr?.length;

    let shouldAddDefaultNestedDiagnosis = true;
    if (input?.filters?.claimsFilters?.showUniquePatients) {
      shouldAddDefaultNestedDiagnosis = false;
      if (
        input &&
        !this.inputHasPatientClaimsFilter(input, featureFlags) &&
        !diagnosesICDFilterApplied &&
        !ccsrICDFilterApplied
      ) {
        shoulds.push(
          ...this.patientPopulationHasChildQuery(input, featureFlags)
        );
      } else if (!this.inputHasPatientClaimsFilter(input, featureFlags)) {
        shouldAddDefaultNestedDiagnosis = true;
      }
    }
    if (
      shouldAddDefaultNestedDiagnosis &&
      !input?.filters?.claimsFilters?.diagnosesICD?.length &&
      !ccsrICDFilterApplied
    ) {
      shoulds.push(
        toNestedClaimsConstantScoreQuery(diagnosesQueryArguments, featureFlags)
      );
    }

    if (!input?.filters?.claimsFilters?.proceduresCPT?.length) {
      shoulds.push(
        toNestedClaimsConstantScoreQuery(proceduresQueryArguments, featureFlags)
      );
    }

    if (!input?.filters?.claimsFilters?.genericNames?.length) {
      shoulds.push(
        toNestedClaimsConstantScoreQuery(
          prescriptionsQueryArguments,
          featureFlags
        )
      );
    }

    if (addTrialsToShoulds) {
      shoulds.push(toNestedConstantScoreQuery(trialsQueryArguments));
    }
    shoulds.push(
      toNestedConstantScoreQuery(congressesQueryArguments),
      toNestedConstantScoreQuery(paymentsQueryArguments)
    );

    if (input?.exportsSelections?.matchedClaimsDetails) {
      shoulds.push(
        toNestedClaimsConstantScoreQuery(
          diagnosesDetailsQueryArguments,
          featureFlags
        ),
        toNestedClaimsConstantScoreQuery(
          procedureDetailsQueryArguments,
          featureFlags
        )
      );
    }

    return shoulds;
  }

  private buildPatientCountSortingShouldsForKeywordSearch(
    diagnosesQueryArguments: ClaimsQueryBuilderArguments,
    proceduresQueryArguments: ClaimsQueryBuilderArguments,
    prescriptionsQueryArguments: ClaimsQueryBuilderArguments,
    congressesQueryArguments: QueryBuilderArguments,
    paymentsQueryArguments: QueryBuilderArguments,
    trialsQueryArguments: QueryBuilderArguments,
    diagnosesDetailsQueryArguments: ClaimsQueryBuilderArguments,
    procedureDetailsQueryArguments: ClaimsQueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags,
    addTrialsToShoulds = false,
    input?: InstitutionsSearchInput
  ) {
    const shoulds: QueryDslQueryContainer[] = [];
    const diagnosesICDFilterApplied =
      !!input?.filters?.claimsFilters?.diagnosesICD?.length;
    const ccsrICDFilterApplied = !!input?.filters?.claimsFilters?.ccsr?.length;

    if (
      input &&
      !this.inputHasPatientClaimsFilter(input, featureFlags) &&
      !diagnosesICDFilterApplied &&
      !ccsrICDFilterApplied
    ) {
      if (input?.filters?.claimsFilters?.showUniquePatients) {
        shoulds.push(
          ...this.patientPopulationHasChildQuery(input, featureFlags)
        );
      } else {
        //We add this clause to ensure patient sort does not filter results out
        shoulds.push(
          toNestedClaimsConstantScoreQuery(
            diagnosesQueryArguments,
            featureFlags
          )
        );
        // if there is a diagnosis/ccsr filter then we have this clause in the must
      }
    }

    if (!input?.filters?.claimsFilters?.proceduresCPT?.length) {
      shoulds.push(
        toNestedClaimsConstantScoreQuery(proceduresQueryArguments, featureFlags)
      );
    }

    if (!input?.filters?.claimsFilters?.genericNames?.length) {
      shoulds.push(
        toNestedClaimsConstantScoreQuery(
          prescriptionsQueryArguments,
          featureFlags
        )
      );
    }

    if (addTrialsToShoulds) {
      shoulds.push(toNestedConstantScoreQuery(trialsQueryArguments));
    }
    shoulds.push(
      toNestedConstantScoreQuery(congressesQueryArguments),
      toNestedConstantScoreQuery(paymentsQueryArguments)
    );

    if (input?.exportsSelections?.matchedClaimsDetails) {
      shoulds.push(
        toNestedClaimsConstantScoreQuery(
          diagnosesDetailsQueryArguments,
          featureFlags
        ),
        toNestedClaimsConstantScoreQuery(
          procedureDetailsQueryArguments,
          featureFlags
        )
      );
    }

    return shoulds;
  }

  private buildMultiSearchRequestBodyToGetMatchingHCPsAndPublications(
    input: Readonly<InstitutionsSearchInput>,
    institutionsFilters: QueryDslQueryContainer[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    indicationsParsedQuery?: string,
    indicationsIcdCodesQuery?: string
  ): MSearchRequest {
    const projectIdFilter = buildTermsQuery(PROJECT_IDS, [input.projectId]);

    const searchQuery =
      getSynonymizedQuery(input.query, queryUnderstandingServiceResponse) ??
      EMPTY_STRING;
    const searchQueryForDiagnoses = input.query
      ? constructDiagnosesQueryString(
          input.query,
          queryUnderstandingServiceResponse
        )
      : EMPTY_STRING;

    const queryIndicationSeparator = searchQuery?.length ? "|" : EMPTY_STRING;

    const defaultIndicationsParsedQuery = indicationsParsedQuery
      ? queryIndicationSeparator + indicationsParsedQuery
      : EMPTY_STRING;
    const searchQueryWithIndicationIcdCodes = searchQueryForDiagnoses?.concat(
      indicationsIcdCodesQuery
        ? queryIndicationSeparator + indicationsIcdCodesQuery
        : defaultIndicationsParsedQuery ?? EMPTY_STRING
    );

    const publicationsNestedSimpleQueryString = this.toNestedSimpleQueryString({
      path: "publications",
      fields: ["publicationAbstract_eng", "keywords_eng", "title_eng"],
      searchQuery: searchQuery
    });
    const shoulds: QueryDslQueryContainer[] = [
      this.toNestedSimpleQueryString({
        path: "congress",
        fields: ["keywords_eng", "title_eng"],
        searchQuery: searchQuery
      }),
      this.toNestedSimpleQueryString({
        name: "diagnoses",
        path: "DRG_diagnoses",
        fields: ["codeAndDescription_eng"],
        searchQuery: searchQueryWithIndicationIcdCodes
      }),
      this.toNestedSimpleQueryString({
        name: "procedures",
        path: "DRG_procedures",
        fields: ["codeAndDescription_eng"],
        searchQuery: searchQuery
      }),
      this.toNestedSimpleQueryString({
        name: "prescriptions",
        path: "prescriptions",
        fields: ["generic_name.text"],
        searchQuery: searchQuery
      }),
      this.toNestedSimpleQueryString({
        path: "trials",
        fields: [
          "officialTitle_eng",
          "briefTitle_eng",
          "conditions_eng",
          "interventions_eng",
          "keywords_eng",
          "summary_eng"
        ],
        searchQuery: searchQuery
      }),
      publicationsNestedSimpleQueryString
    ];

    const aggs: AggregationsAggregationContainer = {
      aggs: {
        publications: {
          nested: {
            path: "publications"
          },
          aggs: {
            matching: {
              filter: publicationsNestedSimpleQueryString,
              aggs: {
                total: {
                  cardinality: {
                    field: "publications.id"
                  }
                }
              }
            }
          }
        }
      }
    };

    const filters = [projectIdFilter, ...institutionsFilters];

    return [
      HEADER,
      {
        size: 0,
        query: {
          bool: {
            filter: filters,
            minimum_should_match:
              input.query || indicationsParsedQuery?.length ? 1 : undefined,
            should:
              input.query || indicationsParsedQuery?.length
                ? shoulds
                : undefined
          }
        },
        aggs: aggs.aggs,
        track_total_hits: true
      }
    ];
  }

  private buildSearchRequestBodyToGetMatchingHCPsForInclusionExclusion(
    input: Readonly<InstitutionsSearchInput>,
    institutionsFilters: QueryDslQueryContainer
  ): QueryDslQueryContainer {
    const projectIdFilter = buildTermsQuery(PROJECT_IDS, [input.projectId]);
    //Diagnoses and CCSR filter should be ORed with each other
    const diagnosesOrCcsrFilters = [];
    const claimsFilter = this.buildPatientClaimsFilter(input);
    // if (claimsFilter.length && !_.isEmpty(input.filters?.countries)) {
    //   claimsFilter.push({
    //     terms: {
    //       "patientClaims.country": input.filters!.countries!
    //     }
    //   });
    // }
    if (input.filters?.claimsFilters?.diagnosesICD?.length) {
      diagnosesOrCcsrFilters.push(
        this.toTermsFilter(
          "patientClaims.diagnosisIcdCode",
          input.filters?.claimsFilters?.diagnosesICD?.map(extractClaimsCode)
        )
      );
    }

    if (input.filters?.claimsFilters?.ccsr?.length) {
      diagnosesOrCcsrFilters.push(
        this.toTermsFilter(
          "patientClaims.ccsrDescriptions",
          input.filters?.claimsFilters?.ccsr
        )
      );
    }

    if (diagnosesOrCcsrFilters.length == 2) {
      claimsFilter.push({
        bool: {
          should: diagnosesOrCcsrFilters,
          minimum_should_match: 1
        }
      });
    } else {
      claimsFilter.push(...diagnosesOrCcsrFilters);
    }

    if (input.filters?.claimsFilters?.proceduresCPT?.length) {
      claimsFilter.push(
        this.toTermsFilter(
          "patientClaims.procedureCode",
          input.filters?.claimsFilters?.proceduresCPT?.map(extractClaimsCode)
        )
      );
    }

    const filters = [projectIdFilter, institutionsFilters];
    const isInclusionExclusion = claimsFilter.length;
    if (isInclusionExclusion) {
      const childQuery = {
        has_child: {
          type: "claim",
          query: {
            bool: {
              filter: claimsFilter
            }
          }
        }
      };
      filters.push(childQuery);
    }

    return {
      bool: {
        filter: filters
      }
    };
  }

  private toNestedTrialsFunctionScoreQueryForH1DefaultRanking(
    {
      path,
      name,
      fields,
      searchQuery,
      innerHits,
      analyzer
    }: QueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags
  ): QueryDslQueryContainer {
    const shoulds: QueryDslQueryContainer[] = [];
    const musts: QueryDslQueryContainer[] = [];

    //priority to completed trials
    shoulds.push(TRIAL_IS_COMPLETED);

    //filter matchin records
    musts.push({
      simple_query_string: toSimpleQueryString({
        path,
        name,
        fields,
        searchQuery,
        analyzer
      })
    });

    const nestedQuery: QueryDslQueryContainer = {
      nested: {
        path: path,
        score_mode: "sum",
        query: {
          function_score: {
            boost_mode: "replace",
            query: {
              bool: {
                must: musts,
                should: shoulds
              }
            }
          }
        },
        inner_hits: innerHits
      }
    };
    return {
      function_score: {
        query: nestedQuery,
        boost_mode: "replace",
        functions: [
          {
            script_score:
              this.toFunctionScriptScoreForH1DefaultRanking("trials_count")
          }
        ]
      }
    };
  }

  private toNestedTrialsInvestigatorFunctionScoreQueryForH1DefaultRanking(
    { path, name, fields, searchQuery, analyzer }: QueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags
  ): QueryDslQueryContainer {
    const filters: QueryDslQueryContainer[] = [];
    const functions: QueryDslFunctionScoreContainer[] = [];

    //filter matchin records
    filters.push({
      simple_query_string: toSimpleQueryString({
        path,
        name,
        fields,
        searchQuery,
        analyzer
      })
    });

    //use investigator count to score documents
    functions.push({
      script_score: {
        script: SCRIPT_SCORE_FOR_TRIAL_INVESIGATOR_COUNT
      }
    });

    const nestedQuery: QueryDslQueryContainer = {
      nested: {
        path: path,
        score_mode: "sum",
        inner_hits: {
          name: "trialInvestigatorCount",
          _source: false
        },
        query: {
          function_score: {
            boost_mode: "replace",
            query: {
              bool: {
                filter: filters
              }
            },
            functions: functions
          }
        }
      }
    };
    return {
      function_score: {
        query: nestedQuery,
        boost_mode: "replace",
        functions: [
          {
            script_score: this.toFunctionScriptScoreForH1DefaultRanking(
              "trialInvestigatorCount"
            )
          }
        ]
      }
    };
  }

  private toNestedPaymentsFunctionScoreQueryForH1DefaultRanking(
    {
      path,
      name,
      fields,
      searchQuery,
      innerHits,
      analyzer
    }: QueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags
  ): QueryDslQueryContainer {
    const filters: QueryDslQueryContainer[] = [];
    const functions: QueryDslFunctionScoreContainer[] = [];

    //filter matchin records
    filters.push({
      simple_query_string: toSimpleQueryString({
        path,
        name,
        fields,
        searchQuery,
        analyzer
      })
    });

    //use only research payments to score
    const functionFilter: QueryDslQueryContainer = {
      term: {
        "payments.payment_type": PaymentType.research
      }
    };

    //score documents based on the amount in payments
    functions.push({
      filter: functionFilter,
      field_value_factor: {
        field: "payments.amount",
        missing: 0
      }
    });
    const nestedQuery: QueryDslQueryContainer = {
      nested: {
        path: path,
        score_mode: "sum",
        query: {
          function_score: {
            boost_mode: "replace",
            query: {
              bool: {
                filter: filters
              }
            },
            functions: functions
          }
        },
        inner_hits: innerHits
      }
    };
    return {
      function_score: {
        query: nestedQuery,
        boost_mode: "replace",
        functions: [
          {
            script_score: this.toFunctionScriptScoreForH1DefaultRanking(
              "research_institution_payment_total"
            )
          }
        ]
      }
    };
  }

  private toNestedClaimsFunctionScoreQueryForH1DefaultRanking(
    {
      path,
      name,
      fields,
      searchQuery,
      innerHits,
      analyzer,
      countField
    }: ClaimsQueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags
  ): QueryDslQueryContainer {
    const nestedQuery: QueryDslQueryContainer = {
      nested: {
        path: path,
        score_mode: "sum",
        query: {
          function_score: {
            boost_mode: "replace",
            query: {
              simple_query_string: toSimpleQueryString({
                path,
                name,
                fields,
                searchQuery,
                analyzer
              })
            },
            functions: [
              {
                field_value_factor: {
                  field: `${path}.${countField}`,
                  missing: 0
                }
              }
            ]
          }
        },
        inner_hits: innerHits
      }
    };

    const functionScoreQuery: QueryDslQueryContainer = {
      function_score: {
        query: nestedQuery,
        boost_mode: "replace",
        functions: [
          {
            script_score: this.toFunctionScriptScoreForH1DefaultRanking(
              "totalDiagnosesCount"
            )
          }
        ]
      }
    };

    return functionScoreQuery;
  }

  private toNestedFunctionScoreQuery({
    path,
    name,
    fields,
    searchQuery,
    innerHits,
    analyzer,
    countField
  }: ClaimsQueryBuilderArguments): QueryDslQueryContainer {
    return {
      nested: {
        path,
        score_mode: "sum",
        query: {
          function_score: {
            boost_mode: "replace",
            query: {
              simple_query_string: toSimpleQueryString({
                path,
                name,
                fields,
                searchQuery,
                analyzer
              })
            },
            functions: [
              {
                field_value_factor: {
                  field: `${path}.${countField}`,
                  missing: 0
                }
              }
            ]
          }
        },
        inner_hits: innerHits
      }
    };
  }

  private toNestedClaimsFunctionScoreQuery(
    args: ClaimsQueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags
  ): QueryDslQueryContainer {
    const claimsRegionFilter =
      buildClaimsRegionFilterForInstitutionsSearch(featureFlags);
    const functionScoreQuery = this.toNestedFunctionScoreQuery(args);

    if (claimsRegionFilter) {
      return {
        bool: {
          must_not: claimsRegionFilter,
          must: functionScoreQuery
        }
      };
    }

    return functionScoreQuery;
  }

  private toNestedClaimsFunctionScoreQueryForDiversity(
    {
      path,
      name,
      fields,
      searchQuery,
      innerHits,
      analyzer,
      saturation,
      raceValues,
      countField
    }: ClaimsQueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags
  ): QueryDslQueryContainer {
    const claimsRegionFilter =
      buildClaimsRegionFilterForInstitutionsSearch(featureFlags);

    const nestedQuery: QueryDslQueryContainer = {
      nested: {
        path: path,
        score_mode: "sum",
        query: {
          function_score: {
            boost_mode: "replace",
            query: {
              simple_query_string: toSimpleQueryString({
                path,
                name,
                fields,
                searchQuery,
                analyzer
              })
            },
            functions: [
              {
                field_value_factor: {
                  field: `${path}.${countField}`,
                  missing: 0
                }
              }
            ]
          }
        },
        inner_hits: innerHits
      }
    };

    const functionScoreQuery: QueryDslQueryContainer = {
      function_score: {
        query: nestedQuery,
        boost_mode: "replace",
        functions: [h1DiversityScoring(saturation!, raceValues)]
      }
    };

    if (claimsRegionFilter) {
      return {
        bool: {
          must_not: claimsRegionFilter,
          must: functionScoreQuery
        }
      };
    }

    return functionScoreQuery;
  }

  private toNestedTrialsFunctionScoreQueryForDiversity(
    {
      path,
      name,
      fields,
      searchQuery,
      innerHits,
      analyzer,
      saturation,
      raceValues
    }: QueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags
  ): QueryDslQueryContainer {
    const nestedQuery: QueryDslQueryContainer = {
      nested: {
        path: path,
        score_mode: "sum",
        query: {
          function_score: {
            boost_mode: "replace",
            query: {
              simple_query_string: toSimpleQueryString({
                path,
                name,
                fields,
                searchQuery,
                analyzer
              })
            }
          }
        },
        inner_hits: innerHits
      }
    };
    return {
      function_score: {
        query: nestedQuery,
        boost_mode: "replace",
        functions: [h1DiversityScoring(saturation!, raceValues)]
      }
    };
  }

  private toNestedCTMSTrialsConstantScoreQuery({
    path,
    name,
    fields,
    searchQuery,
    innerHits,
    analyzer,
    boost
  }: QueryBuilderArguments): QueryDslQueryContainer {
    return {
      nested: {
        path,
        query: {
          constant_score: {
            filter: {
              bool: {
                filter: {
                  simple_query_string: toSimpleQueryString({
                    path,
                    name,
                    fields,
                    searchQuery,
                    analyzer
                  })
                },
                must_not: buildTermQuery("trials.source", "h1")
              }
            },
            boost
          }
        },
        inner_hits: innerHits
      }
    };
  }

  private toNestedCTMSTrialsFunctionScoreQueryForDiversity(
    args: QueryBuilderArguments,
    featureFlags: InstitutionSearchFeatureFlags
  ): QueryDslQueryContainer {
    const { saturation, raceValues } = args;
    const saturationToUse = saturation ?? CTMS_TRIALS_SATURATION;
    const nestedQuery: QueryDslQueryContainer =
      this.toNestedCTMSTrialsConstantScoreQuery(args);

    return {
      function_score: {
        query: nestedQuery,
        boost_mode: "replace",
        functions: [h1DiversityScoring(saturationToUse, raceValues)]
      }
    };
  }

  private inputHasAffiliatedPeopleIols(
    input: InstitutionsSearchInput | undefined
  ) {
    return !!input?.filters?.affiliatedPeopleIolsIds?.length;
  }

  private affiliatedPeopleIolsIdsToInt(affiliatedPeopleIolsIds: string[]) {
    return affiliatedPeopleIolsIds
      .map((id) => parseInt(id))
      .filter((id) => !!id);
  }

  private toMultiQueryWrappedFunctionScore(
    queriesToWrap: QueryDslQueryContainer[],
    field: RootFieldsForScoringForH1Default
  ): QueryDslQueryContainer {
    return {
      function_score: {
        query: {
          bool: {
            should: [...queriesToWrap]
          }
        },
        boost_mode: "replace",
        functions: [
          {
            script_score: this.toFunctionScriptScoreForH1DefaultRanking(field)
          }
        ]
      }
    };
  }

  private toFunctionScriptScoreForH1DefaultRanking(
    key: RootFieldsForScoringForH1Default
  ): QueryDslScriptScoreFunction {
    const priority = fieldPriorityMapForNewTLH1DefaultRanking[key];
    const { targetMax, targetMin } = priorityBasedScoreScaleRanges[priority];
    const { max, mean, min } = assetAggregationValues[key];

    return {
      script: {
        source: `
            double score = _score;

            // Define the original minimum and maximum score
            double originalMin = params.min;
            double originalMax = params.max;

            // Define the target range
            double targetMin = params.targetMin;
            double targetMax = params.targetMax;

            // Scale the score to the range [targetMin, targetMax]
            double scaledScore = targetMin + ((score - originalMin) / (originalMax - originalMin)) * (targetMax - targetMin);

            // Return the scaled score
            return scaledScore;
        `,
        params: {
          mean,
          max,
          min,
          targetMax,
          targetMin
        }
      }
    };
  }

  private inputHasPatientClaimsFilter(
    input: InstitutionsSearchInput | undefined,
    featureFlags: InstitutionSearchFeatureFlags
  ) {
    return (
      !!(
        input?.filters?.patientClaimsFilter ??
        input?.filters?.patientClaimsFilterV2
      ) ||
      (((input?.filters?.diversityFilters?.race?.length ?? 0) > 0 ||
        (input?.filters?.diversityFilters?.ageRange?.length ?? 0) > 0 ||
        (input?.filters?.diversityFilters?.sex?.length ?? 0) > 0 ||
        !!input?.filters?.claimsFilters?.diagnosesICD?.length ||
        !!input?.filters?.claimsFilters?.ccsr?.length ||
        input?.filters?.claimsFilters?.diagnosesICDMaxCount ||
        input?.filters?.claimsFilters?.diagnosesICDMinCount) &&
        input?.filters?.claimsFilters?.showUniquePatients &&
        input.sortBy !== InstitutionSortOptions.UK_DIVERSITY) ||
      ((!!input?.filters?.exclusionClaims?.ccsr?.length ||
        !!input?.filters?.exclusionClaims?.diagnosesICD?.length) &&
        featureFlags.enableCcsrExclusionForMatchedCounts)
    );
  }

  private patientPopulationHasChildQuery(
    input: InstitutionsSearchInput,
    featureFlags: InstitutionSearchFeatureFlags
  ): QueryDslQueryContainer[] {
    if (!input.filters?.claimsFilters?.showUniquePatients) {
      return [];
    }

    const patientPopulationFilter = this.buildPatientPopulationFilter(
      input,
      featureFlags
    );
    if (
      patientPopulationFilter.length == 0 &&
      input.sortBy !== InstitutionSortOptions.PATIENT_COUNT
    ) {
      return [];
    }

    const raceFilterValues = input.filters?.diversityFilters?.race ?? [];
    const scoringFilters =
      raceFilterValues.length > 0 ||
      input.sortBy != InstitutionSortOptions.DIVERSITY
        ? []
        : [
            {
              filter: {
                bool: {
                  must_not: {
                    exists: {
                      field: "patientClaims.diversity" // not all patients docs have a value for diversity field
                    }
                  }
                }
              },
              weight: 0
            },
            {
              filter: {
                term: {
                  "patientClaims.diversity": "White Non-Hispanic" // USA, Brazil: if the patient race is whiteNonHispanic weight is 0
                }
              },
              weight: 0
            },
            {
              filter: {
                term: {
                  "patientClaims.diversity": "Other" // USA: if the patient race is Other weight is 0
                }
              },
              weight: 0
            },
            {
              filter: {
                term: {
                  "patientClaims.diversity": "Not Disclosed" // Brazil: if the patient race is Not Disclosed weight is 0
                }
              },
              weight: 0
            }
          ];

    // the weight is a big number so that in case of diagnosis and patient sort only this clause can effect the scoring, as due to the saturation function the score of other clauses are bounded at 1
    // In case of h1 default weight is 1 so these child docs have effect on ranking
    const weight = this.getPatientClauseWeightBasedOnSort(input);
    let patientPopulationQueryClause: QueryDslQueryContainer = {
      has_child: {
        type: "claim",
        score_mode: "sum",
        query: {
          function_score: {
            query: {
              bool: {
                filter: [...patientPopulationFilter]
              }
            },
            functions: [
              {
                weight
              },
              ...scoringFilters
            ],
            score_mode: "multiply",
            boost_mode: "replace"
          }
        },
        inner_hits: {
          name: `patient_claims_matching_count`,
          size: 0,
          _source: false
        }
      }
    };

    if (
      input.sortBy === InstitutionSortOptions.PATIENT_COUNT &&
      patientPopulationFilter.length === 0
    ) {
      if (input.sortBy === InstitutionSortOptions.PATIENT_COUNT) {
        patientPopulationQueryClause = {
          function_score: {
            functions: [
              {
                field_value_factor: {
                  field: "totalPatientDocs",
                  factor: 1,
                  modifier: "none",
                  missing: 0
                }
              }
            ]
          }
        };
      }
    }
    const maxPatientCount = input?.filters?.claimsFilters?.diagnosesICDMaxCount;
    const minPatientCount =
      input?.filters?.claimsFilters?.diagnosesICDMinCount ?? 0;
    if (maxPatientCount || minPatientCount > 0) {
      return [
        {
          function_score: {
            query: patientPopulationQueryClause,
            functions: [
              {
                script_score: {
                  script: {
                    params: {
                      maxValue: maxPatientCount ?? Number.MAX_VALUE,
                      minValue: minPatientCount,
                      weight
                    },
                    source:
                      "((_score <= params.maxValue * params.weight && _score >= params.minValue * params.weight ) ? 1 : 0)"
                  }
                }
              }
            ],
            // boost mode multiply is going to return the score of original query (_score x 1) or zero (_score x 0) depending on whether the patient count bounds are satisfied or not
            // boost mode replace is goign to return 1 or 0, this is used when we are sorting by trials, where we don't want patient clause to contribute to scoring.
            boost_mode: this.patientClauseShouldAffectRanking(input)
              ? "multiply"
              : "replace",
            min_score: 1 // so that only those HCPs qualify that satisfy the min and max patient count filter.
          }
        }
      ];
    }
    return [patientPopulationQueryClause];
  }

  private buildPatientPopulationFilter(
    input: InstitutionsSearchInput,
    featureFlags: InstitutionSearchFeatureFlags
  ): QueryDslQueryContainer[] {
    if (!input.filters?.claimsFilters?.showUniquePatients) {
      return [];
    }
    const suppliedFilters = input.filters;
    const patientPopulationFilter: QueryDslQueryContainer[] =
      this.rulesParserService.parseRulesToEsQueries(input.filters);
    const diagnosesOrCcsrFilters = [];
    if (patientPopulationFilter.length === 0) {
      if ((suppliedFilters?.claimsFilters?.diagnosesICD ?? []).length > 0) {
        diagnosesOrCcsrFilters.push({
          terms: {
            "patientClaims.diagnosisIcdCode":
              suppliedFilters!.claimsFilters!.diagnosesICD!.map(
                extractClaimsCode
              )
          }
        });
      }
      if (suppliedFilters?.claimsFilters?.ccsr?.length) {
        diagnosesOrCcsrFilters.push({
          terms: {
            "patientClaims.ccsrDescriptions":
              suppliedFilters!.claimsFilters!.ccsr
          }
        });
      }
      const mustNotClauses = [];
      if (
        !!suppliedFilters.exclusionClaims?.diagnosesICD?.length &&
        featureFlags.enableCcsrExclusionForMatchedCounts
      ) {
        mustNotClauses.push({
          terms: {
            "patientClaims.diagnosisIcdCode":
              suppliedFilters.exclusionClaims.diagnosesICD.map(
                (codeAndDescription: string) =>
                  codeAndDescription.split(/\s+/)[0]
              )
          }
        });
      }

      if (
        !!suppliedFilters.exclusionClaims?.ccsr?.length &&
        featureFlags.enableCcsrExclusionForMatchedCounts
      ) {
        mustNotClauses.push({
          terms: {
            "patientClaims.ccsrDescriptions":
              suppliedFilters.exclusionClaims.ccsr
          }
        });
      }

      if (diagnosesOrCcsrFilters.length == 2) {
        patientPopulationFilter.push({
          bool: {
            should: diagnosesOrCcsrFilters,
            minimum_should_match: 1
          }
        });
      } else {
        patientPopulationFilter.push(...diagnosesOrCcsrFilters);
      }

      // if claims filter is not applied only then we want to apply indication filter
      if (
        patientPopulationFilter.length === 0 &&
        (suppliedFilters?.indications ?? []).length > 0
      ) {
        patientPopulationFilter.push(
          this.toTermsFilter(
            "patientClaims.diagnosisIndications",
            suppliedFilters!.indications!
          )
        );
      }
      // if claims or indication filter is not applied then only apply the searched keyword as a filter in diagnosis indications
      if (patientPopulationFilter.length === 0 && input.query) {
        patientPopulationFilter.push(
          this.toTermsFilter("patientClaims.diagnosisIndications", [
            input.query.trim().toLowerCase()
          ])
        );
      }

      const diversityUserFilters =
        this.buildPatientDiversityFiltersForPatientPopulation(input);
      patientPopulationFilter.push(...diversityUserFilters);
      if (mustNotClauses.length) {
        patientPopulationFilter.push({
          bool: {
            must_not: mustNotClauses
          }
        });
      }
    }

    // if (
    //   patientPopulationFilter.length &&
    //   !_.isEmpty(input.filters?.countries)
    // ) {
    //   patientPopulationFilter.push({
    //     terms: {
    //       "patientClaims.country": input.filters!.countries!
    //     }
    //   });
    // }

    return patientPopulationFilter;
  }

  private buildPatientDiversityFiltersForPatientPopulation(
    input: InstitutionsSearchInput
  ): QueryDslQueryContainer[] {
    const filters: QueryDslQueryContainer[] = [];
    const raceFilterValues = input.filters?.diversityFilters?.race ?? [];
    if (raceFilterValues.length > 0) {
      filters.push(
        this.toTermsFilter("patientClaims.diversity", raceFilterValues)
      );
    }
    const ageFiltervalues = input.filters?.diversityFilters?.ageRange ?? [];
    if (ageFiltervalues.length > 0) {
      filters.push(this.toTermsFilter("patientClaims.age", ageFiltervalues));
    }
    const sexFilterValues = input.filters?.diversityFilters?.sex ?? [];
    if (sexFilterValues.length > 0) {
      filters.push(
        this.toTermsFilter(
          "patientClaims.gender",
          sexFilterValues.map((sex: string) => {
            return sex === "male" ? "M" : "F";
          })
        )
      );
    }
    return filters;
  }

  private toTermsFilter(
    fieldName: string,
    values: string[]
  ): QueryDslQueryContainer {
    return {
      terms: {
        [fieldName]: values
      }
    };
  }

  private toNestedConstantScoreFilterQuery(
    path: string,
    filters: QueryDslQueryContainer[]
  ): QueryDslQueryContainer {
    return {
      nested: {
        path: path,
        score_mode: "sum",
        query: {
          constant_score: {
            filter: {
              bool: {
                filter: filters
              }
            },
            boost: 1.0
          }
        },
        inner_hits: {
          _source: false
        }
      }
    };
  }

  private toNestedSimpleQueryString(
    {
      path,
      name,
      fields,
      searchQuery,
      innerHits,
      analyzer
    }: QueryBuilderArguments,
    claimsCodes?: string[] | null
  ): QueryDslQueryContainer {
    return {
      nested: {
        path: path,
        query: claimsCodes
          ? {
              bool: {
                should: claimsCodes.map((code) => ({
                  simple_query_string: toSimpleQueryString({
                    path,
                    name,
                    fields,
                    searchQuery: code,
                    analyzer
                  })
                }))
              }
            }
          : {
              simple_query_string: toSimpleQueryString({
                path,
                name,
                fields,
                searchQuery,
                analyzer
              })
            },
        inner_hits: innerHits
      }
    };
  }

  private isErrorResponse(
    response: Readonly<estypes.MsearchResponseItem>
  ): response is ErrorResponseBase {
    return !!(response as ErrorResponseBase).error;
  }

  private buildAutoCompleteQueryForOrgTypes(
    field: string,
    input: Readonly<InstitutionsAutocompleteInput>,
    featureFlags: InstitutionSearchFeatureFlags,
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>
  ): MSearchRequest {
    this.logger.info({ field, input }, "institution orgTypes autocomplete");

    const extraFilters: QueryDslQueryContainer[] = [];
    let request;
    if (
      input.searchType === SearchTypes.NAME ||
      input.searchType === SearchTypes.INSTITUTION
    ) {
      const institutionLevelFilters = buildElasticsearchFiltersFromInputFilters(
        input,
        featureFlags,
        extraFilters,
        []
      );
      const musts: QueryDslQueryContainer[] = [];

      if (input.query) {
        musts.push({
          match_phrase: {
            name: input.query
          }
        });
      }

      request = {
        index: this.institutionsIndexName,
        query: {
          function_score: {
            query: { bool: { filter: institutionLevelFilters, must: musts } }
          }
        },
        aggs: buildOrgTypeAggregates(field)
      };
    } else {
      request = this.buildKeywordSearchRequest(
        input,
        input,
        featureFlags,
        false,
        "iol",
        extraFilters,
        [],
        queryUnderstandingServiceResponse
      );
    }

    // NOTE: Needed because elasticsearch throws error
    request.index = undefined;
    request.track_total_hits = false;
    request._source = false;
    request.aggs = buildOrgTypeAggregates(field);

    if (input.prefix) {
      const phrasePrefixQuery: QueryDslQueryContainer = {
        multi_match: {
          type: "phrase_prefix",
          query: input.prefix,
          fields: field
        }
      };

      this.addPrefixQueryToRequest(request, featureFlags, phrasePrefixQuery);
    }

    return [HEADER, request];
  }

  private async autocomplete(
    field: keyof typeof queryableFields,
    input: Readonly<InstitutionsAutocompleteInput>,
    resolvedIndications: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    regionField?: keyof typeof queryableRegionFields
  ) {
    const featureFlags = await this.getFeatureFlagValues(input);
    const { enableLocationFilterRegionRollup } = featureFlags;
    const shouldAggregateRegions =
      regionField && enableLocationFilterRegionRollup;

    this.logger.info({ field, input }, "institution autocomplete");

    const extraFilters: QueryDslQueryContainer[] = [];
    let request;

    const fields = _.flatten([queryableFields[field]]);

    if (shouldAggregateRegions) {
      fields.push(queryableRegionFields[regionField]);
    }

    const phrasePrefixQuery: QueryDslQueryContainer = {
      multi_match: {
        type: "phrase_prefix",
        query: input.prefix,
        fields
      }
    };

    if (
      input.searchType === SearchTypes.NAME ||
      input.searchType === SearchTypes.INSTITUTION
    ) {
      const institutionLevelFilters = buildElasticsearchFiltersFromInputFilters(
        input,
        featureFlags,
        extraFilters,
        resolvedIndications
      );
      const musts: QueryDslQueryContainer[] = [];

      if (input.query) {
        musts.push({
          match_phrase: {
            name: input.query
          }
        });
      }

      if (input.prefix) {
        musts.push(phrasePrefixQuery);
      }

      request = {
        index: this.institutionsIndexName,
        query: {
          function_score: {
            query: { bool: { filter: institutionLevelFilters, must: musts } }
          }
        }
      };
    } else {
      request = this.buildKeywordSearchRequest(
        input,
        input,
        featureFlags,
        false,
        "iol",
        extraFilters,
        resolvedIndications,
        queryUnderstandingServiceResponse
      );

      if (input.prefix) {
        this.addPrefixQueryToRequest(request, featureFlags, phrasePrefixQuery);
      }
    }
    request.size = IGNORE_HITS;
    request._source = false;
    request.track_total_hits = false;
    //TODO: Just build aggs for the field we need
    request.aggs = buildFilterAggregatesForTopOptions(
      input.consistentLocationFilter,
      featureFlags,
      input.prefix
    );

    const aggregationToKeep = request.aggs![field];
    if (shouldAggregateRegions) {
      const regionAggregation = request.aggs![regionField];

      request.aggs = {
        [field]: aggregationToKeep,
        [regionField]: regionAggregation
      };
    } else {
      request.aggs = {
        [field]: aggregationToKeep
      };
    }

    this.logger.info({ query: request }, "elasticsearch request");

    const data = await this.elasticSearchService.query<never>(request);

    this.logger.info(
      { took: data.took, total: data.hits.total },
      "execution time"
    );

    if (
      enableLocationFilterRegionRollup &&
      this.isQueryableAddressField(field)
    ) {
      return this.parseLocationAggregations(
        data,
        field,
        regionField,
        shouldAggregateRegions
      );
    }

    const buckets = data.aggregations![
      field
    ] as AggregationsTermsAggregateBase<DocCountBucket>;
    return this.docCountBucketsToAggregations(buckets);
  }

  private async autocompleteAdminLevelLocation(
    zoomLevel: HeatMapZoomLevelFields,
    country: string,
    input: string
  ): Promise<InstitutionLocationFilterAggregation[]> {
    const start = performance.now();

    this.logger.debug(
      JSON.stringify({ zoomLevel, country, input }, null, 2),
      "institution heatmap location autocomplete"
    );

    const request = {
      index: this.institutionsIndexName,
      query: {
        bool: {
          filter: [
            INSTITUTION_IS_IOL,
            {
              term: {
                "address.country.keyword": country
              }
            }
          ]
        }
      },
      size: IGNORE_HITS,
      _source: false,
      track_total_hits: false,
      aggs: {
        heatMapZoomLevel: {
          terms: {
            field: `heatMapZoomLevel.${zoomLevel}`,
            size: 100,
            include: input
              ? generateRegularExpressionForAutocompleteMatching(input)
              : undefined
          }
        }
      }
    };

    this.logger.debug(
      JSON.stringify(request, null, 2),
      "institution heatmap location autocomplete elasticsearch request"
    );

    const data = await this.elasticSearchService.query<never>(request);

    this.logger.debug(
      JSON.stringify(data, null, 2),
      "institution heatmap location autocomplete result"
    );

    const end = performance.now();

    this.logger.info({
      msg: "institution heatmap location autocomplete",
      duration: end - start,
      input
    });

    const buckets = data.aggregations!
      .heatMapZoomLevel as AggregationsTermsAggregateBase<DocCountBucket>;
    return this.docCountBucketsToLocationFilterAggregations(buckets);
  }

  private parseLocationAggregations(
    data: estypes.SearchResponse<
      never,
      Record<string, estypes.AggregationsAggregate>
    >,
    field: keyof typeof queryableAddressFields,
    regionField?: keyof typeof queryableRegionFields,
    shouldAggregateRegions?: boolean
  ) {
    if (field === "postal_code") {
      const postalCodeAggregationBuckets = data.aggregations![
        field
      ] as AggregationsTermsAggregateBase<LocationRegionsInDocCountBucket>;

      return this.locationRegionsInDocCountBucketsToAggregations(
        postalCodeAggregationBuckets,
        field
      );
    }

    const locationAggregation = data.aggregations![
      field
    ] as LocationFilteredMatchingAggregation;

    let regionBuckets;
    if (shouldAggregateRegions && regionField) {
      regionBuckets = data.aggregations![
        regionField
      ] as AggregationsTermsAggregateBase<RegionsDocCountBucket>;
    }

    return [
      ...this.locationRegionsInDocCountBucketsToAggregations(
        locationAggregation?.filtered_matching,
        field
      ),
      ...this.regionDocCountBucketsToAggregations(regionBuckets)
    ];
  }

  private async getIndicationSynonymsFromQueryUnderstandingService(
    indications: string[],
    or: boolean,
    country = "us"
  ): Promise<[string, string]> {
    const queryUnderstandingServiceResponse =
      await this.queryUnderstandingServiceClient.getIndicationSynonymsAndIcdCodes(
        indications,
        "eng",
        or,
        country
      );

    return [
      queryUnderstandingServiceResponse
        .getIndicationsParsedQuery()
        .replace(ALL_ORs, OR)
        .replace(ALL_ANDs, EMPTY_STRING)
        .replace(ALL_UNICODE_DOUBLE_QUOTES, ASCII_DOUBLE_QUOTES),
      queryUnderstandingServiceResponse.getIndicationsIcdCodesQuery()
    ];
  }

  @Trace("h1-search.institutions.getFeatureFlagValues")
  private async getFeatureFlagValues({
    userId,
    projectId
  }:
    | TaggedInstitutionsSearchInput
    | InstitutionsSearchInput
    | IolClaimsInput): Promise<InstitutionSearchFeatureFlags> {
    const featureFlags: Partial<InstitutionSearchFeatureFlags> = {};

    const user = userId && projectId ? { userId, projectId } : undefined;
    const flagsState = await this.featureFlagsService.getAllFlags(user);

    for (const flag of institutionSearchFeatureFlagTypes) {
      featureFlags[flag] =
        flagsState.getFlagValue(featureFlagDefaults[flag].key) ??
        featureFlagDefaults[flag].default;
    }

    return featureFlags as InstitutionSearchFeatureFlags;
  }

  private hasTagFilters(input: InstitutionsSearchInput) {
    return (
      !_.isEmpty(input.filters?.tags) || !_.isEmpty(input.filters?.excludeTags)
    );
  }

  private isQueryableAddressField(
    field: keyof typeof queryableFields
  ): field is keyof typeof queryableAddressFields {
    return field in queryableAddressFields;
  }
  private async getClaimCodesForCareCluster(
    input: InstitutionsSearchInput,
    claimType: ClaimType
  ): Promise<string[]> {
    if (claimType == "diagnoses") {
      const diagnosesCareClusterApplied = input.filters?.claimsFilters?.ccsr;
      const isExport = !!input.exportsSelections?.matchedClaimsDetails;
      if (!!diagnosesCareClusterApplied?.length && isExport) {
        const codes = await this.ccsrIcdMappingRepository.getIcdCodesForCcsr(
          diagnosesCareClusterApplied
        );
        return codes.map((code) => code.icdCode.toUpperCase());
      }
    }
    return [];
  }

  private async getClaimCodesForCareClusters(
    careClusters: string[],
    claimType: ClaimType
  ): Promise<string[]> {
    if (claimType === "ccsr") {
      const codes = await this.ccsrIcdMappingRepository.getIcdCodesForCcsr(
        careClusters
      );
      return codes.map((code) => code.icdCode.toUpperCase());
    } else if (claimType === "ccsr_px") {
      const codes = await this.ccsrPxMappingRepository.getProcedureCodesForCcsr(
        careClusters
      );
      return codes.map((code) => code.procedureCode.toUpperCase());
    }
    return [];
  }
}

function toNestedSimpleQueryString({
  path,
  name,
  fields,
  searchQuery,
  innerHits,
  analyzer
}: QueryBuilderArguments): QueryDslQueryContainer {
  return {
    nested: {
      path: path,
      query: {
        simple_query_string: toSimpleQueryString({
          path,
          name,
          fields,
          searchQuery,
          analyzer
        })
      },
      inner_hits: innerHits
    }
  };
}
function buildShouldClausesForQuery(query: string): QueryDslQueryContainer {
  const shoulds: QueryDslQueryContainer[] = [];

  //trials
  shoulds.push(
    toNestedSimpleQueryString({
      path: "trials",
      fields: [
        "officialTitle_eng",
        "briefTitle_eng",
        "conditions_eng",
        "interventions_eng",
        "keywords_eng",
        "summary_eng"
      ],
      searchQuery: query
    })
  );

  //congresses
  shoulds.push(
    toNestedSimpleQueryString({
      path: "congresses",
      fields: ["keywords_eng", "title_eng"],
      searchQuery: query
    })
  );

  //payments
  shoulds.push(
    toNestedSimpleQueryString({
      path: "payments",
      fields: ["payment_info"],
      searchQuery: query
    })
  );

  //procedures
  shoulds.push(
    toNestedSimpleQueryString({
      name: "procedures",
      path: "procedures",
      fields: ["codeAndDescription_eng"],
      searchQuery: query
    })
  );

  //prescriptions
  shoulds.push(
    toNestedSimpleQueryString({
      name: "prescriptions",
      path: "prescriptions",
      fields: ["generic_name.text"],
      searchQuery: query
    })
  );

  const finalQuery: QueryDslQueryContainer = {
    bool: {
      should: shoulds,
      minimum_should_match: 1
    }
  };

  return finalQuery;
}

function buildBothOperatorFilters(
  bothParserResponse: BothQueryParseResult
): QueryDslQueryContainer[] {
  const queryList = bothParserResponse.terms;
  const queryWiseClauses: QueryDslQueryContainer[] = queryList.map((query) => {
    return buildShouldClausesForQuery(query);
  });

  return queryWiseClauses;
}

function buildNestedFunctionScoreQueryForClaims(
  claimsFilters: ClaimFiltersInput,
  claimsType: ClaimType,
  featureFlags: InstitutionSearchFeatureFlags,
  skipInnerHits = false,
  indicationsParsedQuery?: string,
  indicationsIcdCodesQuery?: string,
  sortBy?: InstitutionSortOptions,
  raceFilterValues?: string[]
): QueryDslQueryContainer {
  const { path, claimCodesProperty, claimsMinCount, claimsMaxCount } =
    NESTED_CLAIMS_FUNCTION_SCORE_TYPE_MAPPING[claimsType];

  const minCount = getMinClaimsCountForNestedFunctionScore(
    claimsFilters.timeFrame ?? null,
    (claimsFilters[claimsMinCount as keyof ClaimFiltersInput] as
      | number
      | null
      | undefined) ?? null
  );

  const maxCount =
    (claimsFilters[claimsMaxCount as keyof ClaimFiltersInput] as
      | number
      | null
      | undefined) ?? null;

  const internalCountField = internalCountFieldForNestedClaimsQuery(
    claimsFilters,
    path,
    featureFlags
  );

  const filters = filtersForNestedClaimsQuery(
    claimsFilters,
    claimCodesProperty,
    path,
    indicationsParsedQuery,
    path === "diagnoses" ? indicationsIcdCodesQuery : undefined
  );

  return toNestedFunctionScoreQueryForClaims(
    path,
    filters,
    internalCountField,
    skipInnerHits,
    featureFlags,
    minCount,
    maxCount,
    sortBy,
    raceFilterValues
  );
}

function filtersForNestedClaimsQuery(
  claimsFilters: ClaimFiltersInput,
  claimCodesProperty: string,
  path: ClaimType,
  indicationsParsedQuery?: string,
  indicationsIcdCodesQuery?: string
) {
  const filters: QueryDslQueryContainer[] = [];
  const claimCodes = claimsFilters[
    claimCodesProperty as keyof ClaimFiltersInput
  ] as ClaimsCode;

  if (claimCodes && !_.isEmpty(claimCodes)) {
    if (path === "prescriptions") {
      filters.push(buildTermsQuery("prescriptions.generic_name", claimCodes));
    } else if (path === "ccsr") {
      addIndicationsToFilters(filters, path, indicationsParsedQuery, undefined);
      filters.push(
        buildTermsQuery(`${path}.description_eng.keyword`, claimCodes)
      );
    } else if (path === "ccsr_px") {
      filters.push(
        buildTermsQuery(`${path}.description_eng.keyword`, claimCodes)
      );
    } else {
      addIndicationsToFilters(
        filters,
        path,
        indicationsParsedQuery,
        indicationsIcdCodesQuery
      );

      filters.push(
        buildTermsQuery(
          `${path}.code_eng.keyword`,
          claimCodes.map(extractClaimsCode)
        )
      );
    }
  }

  return filters;
}

// aggregate the patientsDiversityRatio for every race selected in the filter by the user
function buildDiversityRaceRankingScript(
  raceFilterValues?: string[] | null,
  minDiversityScore = 0
) {
  const raceDiversityRatios: string[] = [];
  if (raceFilterValues?.length) {
    for (const raceFilterValue of raceFilterValues) {
      const raceField =
        mapRaceFilterValueToPatientsDiversityRatioField(raceFilterValue);

      if (raceField) {
        raceDiversityRatios.push(
          `(doc['patientsDiversityRatio.${raceField}'].empty ? ${minDiversityScore}: doc['patientsDiversityRatio.${raceField}'].value)`
        );
      }
    }
  }
  let weight: string;
  if (raceDiversityRatios.length === 0) {
    // weight in case of no race filter
    weight = `(${PATIENT_DIVERSITY_RATIO.join(" + ")})`;
  } else {
    weight = "( " + raceDiversityRatios.join(" + ") + " )";
  }
  return weight;
}

/**
 * This function returns the script score that is used to score trials, procedures and diagnoses for H1 Diversity Scoring
 * @param saturationParam constant in the saturation function score
 * @returns The Function Score Container containing the script score
 */
function h1DiversityScoring(
  saturationParam: number,
  raceFilterValues?: string[],
  minDiversityScore = 0
): QueryDslFunctionScoreContainer {
  const weight = buildDiversityRaceRankingScript(
    raceFilterValues,
    minDiversityScore
  );
  const weightScriptNoRace = `doc['patientsDiversityRatio.whiteNonHispanic'].size() == 0 ? ${minDiversityScore} : saturation(_score, params['saturation_param']) * ${weight}`;
  return {
    script_score: {
      script: {
        source: !raceFilterValues?.length
          ? weightScriptNoRace
          : `saturation(_score, params['saturation_param']) * ${weight}`,
        params: {
          saturation_param: saturationParam
        }
      }
    }
  };
}

function addIndicationsToFilters(
  filters: QueryDslQueryContainer[],
  path: string | nestedPath,
  indicationsParsedQuery?: string,
  indicationsIcdCodesQuery?: string
) {
  if (indicationsIcdCodesQuery) {
    filters.push({
      simple_query_string: toSimpleQueryString({
        path: path as nestedPath,
        fields: [`indications`],
        searchQuery: indicationsParsedQuery
      })
    });
  } else if (indicationsParsedQuery) {
    filters.push({
      simple_query_string: toSimpleQueryString({
        path: path as nestedPath,
        fields: [`code_eng`, `description_eng`],
        searchQuery: indicationsParsedQuery
      })
    });
  }
}

function buildElasticsearchQueryForClaimFilters(
  claimsFilters: ClaimFiltersInput,
  type: ClaimType,
  skipInnerHits = false,
  featureFlags: InstitutionSearchFeatureFlags,
  indicationsParsedQuery?: string,
  indicationsIcdCodesQuery?: string,
  rangeFilters?: QueryDslQueryContainer,
  sortBy?: InstitutionSortOptions,
  raceFilterValues?: string[],
  shouldExcludeHcps?: boolean,
  isExclusion?: boolean
): QueryDslQueryContainer {
  const esFilters: QueryDslQueryContainer[] = [];
  type MappingObject = {
    path: ClaimType;
    claimCodesProperty: string;
  };

  type TypeMapping = {
    [key: string]: MappingObject;
  };
  const typeMapping: TypeMapping = {
    diagnoses: {
      path: "diagnoses",
      claimCodesProperty: "diagnosesICD"
    },
    procedures: {
      path: "procedures",
      claimCodesProperty: "proceduresCPT"
    },
    prescriptions: {
      path: "prescriptions",
      claimCodesProperty: "genericNames"
    },
    ccsr: {
      path: "ccsr",
      claimCodesProperty: "ccsr"
    },
    ccsr_px: {
      path: "ccsr_px",
      claimCodesProperty: "ccsr_px"
    }
  };

  const { path, claimCodesProperty } = typeMapping[type];
  const claimCodes = claimsFilters[
    claimCodesProperty as keyof ClaimFiltersInput
  ] as string[];

  if (!_.isEmpty(claimCodes)) {
    if (path === "prescriptions") {
      esFilters.push(buildTermsQuery(`${path}.generic_name`, claimCodes));
    } else if (
      (path === "ccsr" || path === "ccsr_px") &&
      !!isExclusion &&
      shouldExcludeHcps
    ) {
      esFilters.push(
        buildTermsQuery(`${path}.description_eng.keyword`, claimCodes)
      );
    } else if ((isExclusion && shouldExcludeHcps) || !isExclusion) {
      const claim_codes = claimCodes.map(extractClaimsCode);

      addIndicationsToFilters(
        esFilters,
        path,
        indicationsParsedQuery,
        indicationsIcdCodesQuery
      );

      esFilters.push(buildTermsQuery(`${path}.code_eng.keyword`, claim_codes));
    }
  }

  if (rangeFilters) {
    esFilters.push(rangeFilters);
  }

  const innerHitsCount = internalCountFieldForNestedClaimsQuery(
    claimsFilters,
    path,
    featureFlags
  );

  const query = {
    path,
    query: {
      bool: {
        filter: esFilters
      }
    },
    ...(!skipInnerHits
      ? {
          inner_hits: {
            _source: false,
            docvalue_fields: [innerHitsCount],
            size: 1000
          }
        }
      : {})
  };
  const isDiversitySort = sortBy === InstitutionSortOptions.DIVERSITY;
  const isDiversityClauseRequired = isDiversitySort && !_.isEmpty(claimCodes);
  const queryWithDiversityClause: QueryDslQueryContainer = {
    function_score: {
      query: {
        nested: query
      },
      boost_mode: "replace",
      functions: [
        h1DiversityScoring(
          type === "diagnoses" || type === "ccsr"
            ? DIAGNOSES_SATURATION
            : type === "procedures" || type === "ccsr_px"
            ? PROCEDURES_SATURATION
            : PRESCRIPTIONS_SATURATION,
          raceFilterValues
        )
      ]
    }
  };

  return isDiversityClauseRequired
    ? queryWithDiversityClause
    : { nested: query };
}

function mapRaceFilterValueToPatientsDiversityRatioField(
  raceFilter: string
): string | undefined {
  return raceFilterValueToPatientsDiversityRatioFieldMap.get(raceFilter);
}

export function buildRequestForClaims(
  input: InstitutionsSearchInput,
  request: estypes.SearchRequest,
  featureFlags: InstitutionSearchFeatureFlags,
  skipInnerHits = false,
  indicationsParsedQuery?: string,
  indicationsIcdCodesQuery?: string,
  sortBy?: InstitutionSortOptions,
  raceFilterValues?: string[]
): estypes.SearchRequest {
  const claimsFilters = input.filters!.claimsFilters!;

  if (featureFlags.enableClaimsFilteringMatchedCountsUpdate) {
    const nestedClaimQueries: QueryDslQueryContainer[] = [];

    const {
      diagnosesICD,
      proceduresCPT,
      genericNames,
      ccsr,
      timeFrame,
      ccsr_px
    } = claimsFilters;

    const filterApplied = (
      claimsFilters: ClaimFiltersInput,
      filterCodeType: "diagnosesICD" | "proceduresCPT" | "prescriptions"
    ) => {
      const minCount = claimsFilters[`${filterCodeType}MinCount`];
      const maxCount = claimsFilters[`${filterCodeType}MaxCount`];

      return _.some(
        [minCount, maxCount, timeFrame],
        (value) => !_.isNil(value)
      );
    };

    const nestedDiagnosesQuery = processClaims(
      DIAGNOSES,
      diagnosesICD,
      filterApplied(claimsFilters, "diagnosesICD"),
      claimsFilters,
      skipInnerHits,
      featureFlags,
      input.query,
      indicationsParsedQuery,
      indicationsIcdCodesQuery,
      sortBy,
      raceFilterValues
    );

    const nestedProceduresQuery = processClaims(
      PROCEDURES,
      proceduresCPT,
      filterApplied(claimsFilters, "proceduresCPT"),
      claimsFilters,
      skipInnerHits,
      featureFlags,
      input.query,
      indicationsParsedQuery,
      undefined,
      sortBy,
      raceFilterValues
    );

    const nestedPrescriptionsQuery = processClaims(
      PRESCRIPTIONS,
      genericNames,
      filterApplied(claimsFilters, "prescriptions"),
      claimsFilters,
      skipInnerHits,
      featureFlags,
      input.query,
      indicationsParsedQuery,
      undefined,
      sortBy,
      raceFilterValues
    );

    const nestedCcsrQuery = processClaims(
      CCSR,
      ccsr,
      false,
      claimsFilters,
      skipInnerHits,
      featureFlags,
      input.query,
      indicationsParsedQuery,
      undefined,
      sortBy,
      raceFilterValues
    );

    const nestedCcsrPxQuery = processClaims(
      CCSR_PX,
      ccsr_px,
      false,
      claimsFilters,
      skipInnerHits,
      featureFlags,
      input.query,
      indicationsParsedQuery,
      undefined,
      sortBy,
      raceFilterValues
    );

    // As Diagnoses filters is ORed with CCSR filters
    // Also Procedures filters is ORed with CCSR_PX filters
    // we need to apply a bool operator with a should clause for it
    const clauseForORedDiagnosesAndCcsr: QueryDslQueryContainer[] =
      createBooleanOrClauseForTwoQueries(nestedDiagnosesQuery, nestedCcsrQuery);

    const clauseForORedProceduresAndCcsrPx: QueryDslQueryContainer[] =
      createBooleanOrClauseForTwoQueries(
        nestedProceduresQuery,
        nestedCcsrPxQuery
      );

    nestedClaimQueries.push(
      ...clauseForORedDiagnosesAndCcsr,
      ...clauseForORedProceduresAndCcsrPx,
      ...nestedPrescriptionsQuery,
      ...nestedCcsrPxQuery
    );

    if (nestedClaimQueries.length === 0) return request;
    const [isAgeSort] = getAgeSortValues(input);
    const isTrialSort = input.sortBy === InstitutionSortOptions.TRIALS;
    //We do not need to use must clause unless count/code filter is specified.
    const determineClauseForClaimType = (
      claimType: ClaimType,
      claimsFilters: any
    ) => {
      // Check if current claim type has either code or count filters
      const hasCodeOrCountFilters =
        !areCodesEmpty(claimType, claimsFilters) ||
        hasCountFilters(claimType, claimsFilters);
      // If either claim type has code or count filters, use 'must'
      if (hasCodeOrCountFilters) {
        return "must";
      }
      // default to should
      return "should";
    };
    const scoreClauseChosenForDiagnoses =
      !isAgeSort && !isTrialSort
        ? determineClauseForClaimType("diagnoses", claimsFilters)
        : "filter";
    const scoreClauseChosenForProcedures =
      !isAgeSort && !isTrialSort
        ? determineClauseForClaimType("procedures", claimsFilters)
        : "filter";
    const scoreClauseChosenForPrescriptions =
      !isAgeSort && !isTrialSort
        ? determineClauseForClaimType("prescriptions", claimsFilters)
        : "filter";
    const scoreClauseChosenForCcsr =
      !isAgeSort && !isTrialSort
        ? ccsr?.length
          ? "must"
          : "should"
        : "filter";
    const scoreClauseChosenForCcsrPx =
      !isAgeSort && !isTrialSort
        ? ccsr_px?.length
          ? "must"
          : "should"
        : "filter";
    if (
      scoreClauseChosenForDiagnoses === scoreClauseChosenForProcedures &&
      scoreClauseChosenForDiagnoses === scoreClauseChosenForPrescriptions &&
      scoreClauseChosenForDiagnoses === scoreClauseChosenForCcsr &&
      scoreClauseChosenForDiagnoses === scoreClauseChosenForCcsrPx
    ) {
      return buildFiltersForClaims(
        nestedClaimQueries,
        request,
        scoreClauseChosenForDiagnoses
      );
    } else {
      const mustClauses = [];
      if (
        scoreClauseChosenForDiagnoses === "must" ||
        scoreClauseChosenForCcsr === "must"
      ) {
        if (!input.filters?.claimsFilters?.showUniquePatients) {
          mustClauses.push(...clauseForORedDiagnosesAndCcsr);
        }
      }

      if (
        scoreClauseChosenForProcedures === "must" ||
        scoreClauseChosenForCcsrPx === "must"
      ) {
        mustClauses.push(...clauseForORedProceduresAndCcsrPx);
      }

      if (scoreClauseChosenForPrescriptions === "must") {
        mustClauses.push(...nestedPrescriptionsQuery);
      }

      return buildFiltersForClaims(mustClauses, request, "must");
    }
  } else {
    // Original approach to claims filtering
    const diagnosisClaimsQuery = buildElasticsearchQueryForClaimFilters(
      claimsFilters,
      "diagnoses",
      skipInnerHits,
      featureFlags,
      indicationsParsedQuery,
      indicationsIcdCodesQuery,
      undefined,
      sortBy,
      raceFilterValues
    );
    const proceduresClaimsQuery = buildElasticsearchQueryForClaimFilters(
      claimsFilters,
      "procedures",
      skipInnerHits,
      featureFlags,
      indicationsParsedQuery,
      undefined,
      undefined,
      sortBy,
      raceFilterValues
    );

    const prescriptionsClaimsQuery = buildElasticsearchQueryForClaimFilters(
      claimsFilters,
      "prescriptions",
      skipInnerHits,
      featureFlags,
      indicationsParsedQuery,
      undefined,
      undefined,
      sortBy,
      raceFilterValues
    );

    const nestedClaimQueries = [
      diagnosisClaimsQuery,
      proceduresClaimsQuery,
      prescriptionsClaimsQuery
    ].filter(
      (query) =>
        (isArray(query?.nested?.query?.bool?.filter) &&
          query!.nested!.query!.bool!.filter.length > 0) ||
        (isArray(query?.function_score?.query?.nested?.query?.bool?.filter) &&
          query!.function_score!.query!.nested!.query!.bool!.filter.length > 0)
    );

    const countOrTimeFrameFilterApplied = _.some(
      [
        claimsFilters.diagnosesICDMinCount,
        claimsFilters.diagnosesICDMaxCount,
        claimsFilters.proceduresCPTMinCount,
        claimsFilters.proceduresCPTMaxCount,
        claimsFilters.prescriptionsMinCount,
        claimsFilters.prescriptionsMaxCount,
        claimsFilters.timeFrame
      ],
      (value) => !_.isNil(value)
    );

    if (
      countOrTimeFrameFilterApplied &&
      (!_.isEmpty(claimsFilters.diagnosesICD) ||
        !_.isEmpty(claimsFilters.proceduresCPT) ||
        !_.isEmpty(claimsFilters.genericNames))
    ) {
      addClaimsTimeFrameRangeFilter(
        nestedClaimQueries,
        claimsFilters,
        featureFlags
      );
    }

    const claimsRegionFilter =
      buildClaimsRegionFilterForInstitutionsSearch(featureFlags);

    if (claimsRegionFilter) {
      nestedClaimQueries.forEach((query, index) => {
        nestedClaimQueries[index] = {
          bool: {
            must_not: claimsRegionFilter,
            must: query
          }
        };
      });
    }

    if (nestedClaimQueries.length === 0) return request;

    return buildFiltersForClaims(nestedClaimQueries, request, "must");
  }
}

function createBooleanOrClauseForTwoQueries(
  query1: QueryDslQueryContainer[],
  query2: QueryDslQueryContainer[]
): QueryDslQueryContainer[] {
  if (query1.length && query2.length) {
    return [
      {
        bool: {
          should: [...query1, ...query2],
          minimum_should_match: 1
        }
      }
    ];
  } else if (query1.length) {
    return query1;
  } else if (query2.length) {
    return query2;
  } else return [];
}

function addClaimsTimeFrameRangeFilter(
  nestedClaimQueries: QueryDslQueryContainer[],
  claimsFilters: ClaimFiltersInput,
  featureFlags: InstitutionSearchFeatureFlags
) {
  // If there are no codes (nestedClaimQueries is empty) create nested queries for the timeFrame range.
  // Otherwise, find the existing nested queries and add the timeFrame range.
  if (nestedClaimQueries.length === 0) {
    const claimsFields: string[] = [
      ...(claimsFilters.diagnosesICDMinCount ||
      claimsFilters.diagnosesICDMaxCount
        ? ["diagnoses"]
        : []),
      ...(claimsFilters.proceduresCPTMinCount ||
      claimsFilters.proceduresCPTMaxCount
        ? ["procedures"]
        : []),
      ...(claimsFilters.prescriptionsMinCount ||
      claimsFilters.prescriptionsMaxCount
        ? ["prescriptions"]
        : [])
    ];

    const nestedTimeFrameQueries = nestedClaimsFilterRange(
      claimsFields,
      claimsFilters,
      featureFlags
    );

    nestedClaimQueries.push(...nestedTimeFrameQueries);
  } else {
    nestedClaimQueries.forEach((nestedClaim) => {
      const filters: QueryDslQueryContainer[] = [];

      if (Array.isArray(nestedClaim?.nested?.query?.bool?.filter)) {
        filters.push(...nestedClaim.nested!.query!.bool!.filter!);
      } else if (
        Array.isArray(
          nestedClaim?.function_score?.query?.nested?.query?.bool?.filter
        )
      ) {
        filters.push(
          ...nestedClaim.function_score!.query!.nested!.query!.bool!.filter!
        );
      }

      const doesNotContainRangeFilter = !filters.some(
        (obj: QueryDslQueryContainer) => "range" in obj
      );

      if (doesNotContainRangeFilter) {
        Array.isArray(nestedClaim.nested?.query?.bool?.filter) &&
          nestedClaim!.nested!.query!.bool!.filter!.push(
            buildRangeFilterForClaims(
              claimsFilters!,
              nestedClaim!.nested!.path as ClaimType,
              featureFlags,
              true
            )
          );

        Array.isArray(
          nestedClaim?.function_score?.query?.nested?.query?.bool?.filter
        ) &&
          nestedClaim!.function_score!.query!.nested!.query!.bool!.filter.push(
            buildRangeFilterForClaims(
              claimsFilters!,
              nestedClaim!.function_score!.query!.nested!.path as ClaimType,
              featureFlags,
              true
            )
          );
      }
    });
  }
}

function nestedClaimsFilterRange(
  claimsFields: string[],
  claimsFilters: ClaimFiltersInput,
  featureFlags: InstitutionSearchFeatureFlags
) {
  return claimsFields.map((path) => {
    return {
      nested: {
        path: path,
        query: {
          bool: {
            filter: [
              buildRangeFilterForClaims(
                claimsFilters!,
                path as ClaimType,
                featureFlags,
                true
              )
            ]
          }
        }
      }
    };
  });
}

export function mapRaceFilterValueToUkPatientsDiversityField(
  raceFilter: string
): UkPatientCountField {
  if (!raceFilterValueToUkPatientsDiversityRatioFieldMap.has(raceFilter)) {
    throw new Error("Invalid value for race filter: " + raceFilter);
  }
  return raceFilterValueToUkPatientsDiversityRatioFieldMap.get(
    raceFilter
  )! as UkPatientCountField;
}

export function mapNationalityFilterValueToFrancePatientsDiversityField(
  nationalityFilter: string
): FrancePatientCountField {
  if (
    !nationalityFilterValueToFrancePatientsDiversityRatioFieldMap.has(
      nationalityFilter
    )
  ) {
    throw new Error(
      "Invalid value for nationality filter: " + nationalityFilter
    );
  }
  return nationalityFilterValueToFrancePatientsDiversityRatioFieldMap.get(
    nationalityFilter
  )! as FrancePatientCountField;
}

export function mapNationalityFilterValueToSpainPatientsDiversityField(
  nationalityFilter: string
): SpainPatientCountField {
  if (
    !nationalityFilterValueToSpainPatientsDiversityRatioFieldMap.has(
      nationalityFilter
    )
  ) {
    throw new Error(
      "Invalid value for nationality filter: " + nationalityFilter
    );
  }
  return nationalityFilterValueToSpainPatientsDiversityRatioFieldMap.get(
    nationalityFilter
  )! as SpainPatientCountField;
}

// aggregate the patientsDiversityRatio for every race selected in the filter by the user
function buildUkDiversityWeightedClaimsScript(
  raceFilterValues?: string[] | null
) {
  const diversityWeightedClaims: string[] = [];
  if (raceFilterValues?.length) {
    for (const raceFilterValue of raceFilterValues) {
      const raceField: string =
        mapRaceFilterValueToUkPatientsDiversityField(raceFilterValue);
      diversityWeightedClaims.push(
        `(doc['patientClaimsUk.${raceField}'].size() == 0 ? 0 : doc['patientClaimsUk.${raceField}'].value)`
      );
    }
  }

  if (diversityWeightedClaims.length === 0) {
    // weighted claims in case of no race filter
    return `(${DEFAULT_UK_PATIENT_DIVERSITY_WEIGHTED_CLAIMS.join(" + ")})`;
  }

  return "(" + diversityWeightedClaims.join(" + ") + ")";
}

function getUkDiversityFilterClause(
  searchQuery?: string | undefined,
  analyzer?: string,
  claimsCode?: ClaimsCode
): QueryDslQueryContainer | undefined {
  let query: QueryDslQueryContainer | undefined = undefined;
  if (claimsCode?.length) {
    query = {
      terms: {
        "patientClaimsUk.code.keyword": claimsCode.map(extractClaimsCode)
      }
    };
  } else if (searchQuery) {
    query = {
      simple_query_string: toSimpleQueryString({
        path: "patientClaimsUk",
        fields: ["code", "description"],
        searchQuery,
        analyzer
      })
    };
  }

  return query;
}

function buildFranceDiversityWeightedClaimsScript(
  nationalityFilterValues?: string[] | null
) {
  const diversityWeightedClaims: string[] = [];
  if (nationalityFilterValues?.length) {
    for (const nationalityFilterValue of nationalityFilterValues) {
      const nationalityField: string =
        mapNationalityFilterValueToFrancePatientsDiversityField(
          nationalityFilterValue
        );
      diversityWeightedClaims.push(
        `(doc['patientClaimsFrance.${nationalityField}'].size() == 0 ? 0 : doc['patientClaimsFrance.${nationalityField}'].value)`
      );
    }
  }

  if (diversityWeightedClaims.length === 0) {
    // weighted claims in case of no race filter
    return `(${DEFAULT_FRANCE_PATIENT_DIVERSITY_WEIGHTED_CLAIMS.join(" + ")})`;
  }

  return "(" + diversityWeightedClaims.join(" + ") + ")";
}

function getFranceDiversityFilterClause(
  searchQuery?: string | undefined,
  analyzer?: string,
  claimsCode?: ClaimsCode
): QueryDslQueryContainer | undefined {
  let query: QueryDslQueryContainer | undefined = undefined;
  if (claimsCode?.length) {
    query = {
      terms: {
        "patientClaimsFrance.code.keyword": claimsCode.map(extractClaimsCode)
      }
    };
  } else if (searchQuery) {
    query = {
      simple_query_string: toSimpleQueryString({
        path: "patientClaimsFrance",
        fields: ["code", "description"],
        searchQuery,
        analyzer
      })
    };
  }

  return query;
}

function buildSpainDiversityWeightedClaimsScript(
  nationalityFilterValues?: string[] | null
) {
  const diversityWeightedClaims: string[] = [];
  if (nationalityFilterValues?.length) {
    for (const nationalityFilterValue of nationalityFilterValues) {
      const nationalityField: string =
        mapNationalityFilterValueToSpainPatientsDiversityField(
          nationalityFilterValue
        );
      diversityWeightedClaims.push(
        `(doc['patientClaimsSpain.${nationalityField}'].size() == 0 ? 0 : doc['patientClaimsSpain.${nationalityField}'].value)`
      );
    }
  }

  if (diversityWeightedClaims.length === 0) {
    // weighted claims in case of no race filter
    return `(${DEFAULT_SPAIN_PATIENT_DIVERSITY_WEIGHTED_CLAIMS.join(" + ")})`;
  }

  return "(" + diversityWeightedClaims.join(" + ") + ")";
}

function getSpainDiversityFilterClause(
  searchQuery?: string | undefined,
  analyzer?: string,
  claimsCode?: ClaimsCode
): QueryDslQueryContainer | undefined {
  let query: QueryDslQueryContainer | undefined = undefined;
  if (claimsCode?.length) {
    query = {
      terms: {
        "patientClaimsSpain.code.keyword": claimsCode.map(extractClaimsCode)
      }
    };
  } else if (searchQuery) {
    query = {
      simple_query_string: toSimpleQueryString({
        path: "patientClaimsSpain",
        fields: ["code", "description"],
        searchQuery,
        analyzer
      })
    };
  }

  return query;
}

function getUkDiversityRankingQuery(
  races?: string[] | null | undefined,
  searchQuery?: string | undefined,
  innerHits?: estypes.SearchInnerHits,
  analyzer?: string,
  claimsCode?: ClaimsCode
): QueryDslQueryContainer {
  const diversityWeightedClaimsScript =
    buildUkDiversityWeightedClaimsScript(races);

  const query = getUkDiversityFilterClause(searchQuery, analyzer, claimsCode);

  return {
    nested: {
      score_mode: "sum",
      path: "patientClaimsUk",
      query: {
        function_score: {
          boost_mode: "replace",
          query,
          functions: [
            {
              script_score: {
                script: {
                  source: diversityWeightedClaimsScript
                }
              }
            }
          ]
        }
      },
      inner_hits: innerHits
    }
  };
}

function getFranceDiversityRankingQuery(
  nationalities?: string[] | null | undefined,
  searchQuery?: string | undefined,
  innerHits?: estypes.SearchInnerHits,
  analyzer?: string,
  claimsCode?: ClaimsCode
): QueryDslQueryContainer {
  const diversityWeightedClaimsScript =
    buildFranceDiversityWeightedClaimsScript(nationalities);

  const query = getFranceDiversityFilterClause(
    searchQuery,
    analyzer,
    claimsCode
  );

  return {
    nested: {
      score_mode: "sum",
      path: "patientClaimsFrance",
      query: {
        function_score: {
          boost_mode: "replace",
          query,
          functions: [
            {
              script_score: {
                script: {
                  source: diversityWeightedClaimsScript
                }
              }
            }
          ]
        }
      },
      inner_hits: innerHits
    }
  };
}

function getSpainDiversityRankingQuery(
  nationalities?: string[] | null | undefined,
  searchQuery?: string | undefined,
  innerHits?: estypes.SearchInnerHits,
  analyzer?: string,
  claimsCode?: ClaimsCode
): QueryDslQueryContainer {
  const diversityWeightedClaimsScript =
    buildSpainDiversityWeightedClaimsScript(nationalities);

  const query = getSpainDiversityFilterClause(
    searchQuery,
    analyzer,
    claimsCode
  );

  return {
    nested: {
      score_mode: "sum",
      path: "patientClaimsSpain",
      query: {
        function_score: {
          boost_mode: "replace",
          query,
          functions: [
            {
              script_score: {
                script: {
                  source: diversityWeightedClaimsScript
                }
              }
            }
          ]
        }
      },
      inner_hits: innerHits
    }
  };
}

function processClaims(
  type: ClaimType,
  claimsCode: ClaimsCode,
  countOrTimeFrameFilterApplied: boolean,
  claimsFilters: ClaimFiltersInput,
  skipInnerHits: boolean,
  featureFlags: InstitutionSearchFeatureFlags,
  query?: string,
  indicationsParsedQuery?: string,
  indicationsIcdCodesQuery?: string,
  sortBy?: InstitutionSortOptions,
  raceFilterValues?: string[]
) {
  if (type === "diagnoses" && isEuropeanDiversitySort(sortBy)) {
    if (!_.isNil(claimsCode)) {
      const europeanDiversityQuery = getEuropeanDiversityRankingQuery(
        sortBy,
        raceFilterValues,
        indicationsIcdCodesQuery,
        claimsCode
      );

      return europeanDiversityQuery ? [europeanDiversityQuery] : [];
    } else {
      return [];
    }
  }
  let claimsRegionFilter;

  if (type !== "prescriptions") {
    claimsRegionFilter =
      buildClaimsRegionFilterForInstitutionsSearch(featureFlags);
  }

  if (countOrTimeFrameFilterApplied || !_.isEmpty(claimsCode)) {
    const claimsQuery = buildQueryForClaims(
      claimsCode,
      claimsFilters,
      skipInnerHits,
      featureFlags,
      type,
      query,
      indicationsParsedQuery,
      type === "diagnoses" ? indicationsIcdCodesQuery : undefined,
      sortBy,
      raceFilterValues
    );

    if (claimsRegionFilter) {
      return [
        {
          bool: {
            must_not: claimsRegionFilter,
            must: claimsQuery
          }
        }
      ];
    }

    return [claimsQuery];
  }

  return [];
}

function buildQueryForClaims(
  claimsCode: ClaimsCode,
  claimsFilters: ClaimFiltersInput,
  skipInnerHits: boolean,
  featureFlags: InstitutionSearchFeatureFlags,
  claimType: ClaimType,
  query?: string,
  indicationsParsedQuery?: string,
  indicationsIcdCodesQuery?: string,
  sortBy?: InstitutionSortOptions,
  raceFilterValues?: string[]
) {
  const skipInnerHitsIfQueryOrIndicationsPresent =
    (query || indicationsParsedQuery) && !claimsCode?.length ? true : false;

  if (
    query?.length ||
    indicationsParsedQuery?.length ||
    indicationsIcdCodesQuery?.length ||
    (claimsCode && claimsCode?.length) ||
    claimsFilters.timeFrame
  ) {
    if (featureFlags.enableClaimsFilterFunctionScore) {
      return buildNestedFunctionScoreQueryForClaims(
        claimsFilters,
        claimType,
        featureFlags,
        skipInnerHitsIfQueryOrIndicationsPresent,
        indicationsParsedQuery,
        claimType === "diagnoses" ? indicationsIcdCodesQuery : undefined,
        sortBy,
        raceFilterValues
      );
    } else {
      const rangeFilters =
        claimsCode && claimsCode?.length
          ? buildRangeFilterForClaims(
              claimsFilters,
              claimType,
              featureFlags,
              true
            )
          : undefined;

      return buildElasticsearchQueryForClaimFilters(
        claimsFilters,
        claimType,
        skipInnerHitsIfQueryOrIndicationsPresent,
        featureFlags,
        indicationsParsedQuery,
        claimType === "procedures" ? undefined : indicationsIcdCodesQuery,
        rangeFilters
      );
    }
  }

  return buildElasticsearchQueryForClaimFilters(
    claimsFilters,
    claimType,
    skipInnerHits,
    featureFlags,
    indicationsParsedQuery,
    claimType === "procedures" ? undefined : indicationsIcdCodesQuery
  );
}

function buildFiltersForClaims(
  query: QueryDslQueryContainer[],
  originalRequest: estypes.SearchRequest,
  filterType: "must" | "must_not" | "filter" | "should"
): estypes.SearchRequest {
  const request = _.cloneDeep(originalRequest);
  const boolQuery = request.query?.function_score?.query?.bool;
  const diversityQuery =
    request.query?.function_score?.query?.function_score?.query?.bool;
  let finalBoolQueryStructure: QueryDslBoolQuery;
  if (boolQuery?.[filterType]) {
    (
      request.query!.function_score!.query!.bool![
        filterType
      ] as QueryDslQueryContainer[]
    ).push(...query);
    finalBoolQueryStructure = request.query!.function_score!.query!.bool!;
  } else if (boolQuery) {
    request.query!.function_score!.query!.bool![filterType] = query;
    finalBoolQueryStructure = request.query!.function_score!.query!.bool!;
  } else if (request.query?.bool?.[filterType]) {
    (request.query!.bool![filterType] as QueryDslQueryContainer[]).push(
      ...query
    );
    finalBoolQueryStructure = request.query!.bool!;
  } else if (request.query?.bool) {
    request.query!.bool![filterType] = query;
    finalBoolQueryStructure = request.query!.bool!;
  } else if (diversityQuery?.[filterType]) {
    (
      request.query!.function_score!.query!.function_score!.query!.bool![
        filterType
      ] as QueryDslQueryContainer[]
    ).push(...query);
    finalBoolQueryStructure =
      request.query!.function_score!.query!.function_score!.query!.bool!;
  } else if (diversityQuery) {
    request.query!.function_score!.query!.function_score!.query!.bool![
      filterType
    ] = query;
    finalBoolQueryStructure =
      request.query!.function_score!.query!.function_score!.query!.bool!;
  }
  if (filterType == "should") {
    finalBoolQueryStructure!.minimum_should_match = 1;
  }

  return request;
}

function getNestedInnerHitsForClaims(
  claimType: ClaimType,
  featureFlags: InstitutionSearchFeatureFlags,
  claimsFilters?: ClaimFiltersInput
) {
  if (claimType === "prescriptions") {
    return claimsFilters?.timeFrame
      ? `num_prescriptions_${claimsFilters!.timeFrame}_year`
      : "num_prescriptions";
  }
  const {
    enableUniquePatientCountForClaims,
    disableUniquePatientCountForOnlyProcedures,
    enableClaimsFilteringMatchedCountsUpdate
  } = featureFlags;
  const uniquePatientToggleFromInput =
    claimsFilters?.showUniquePatients ?? false;
  const isProcedureWithUniqueCount =
    claimType === "procedures" && !disableUniquePatientCountForOnlyProcedures;
  const isDiagnosis = claimType === "diagnoses";
  const shouldUseUniquePatientCount =
    (isProcedureWithUniqueCount || isDiagnosis) &&
    enableUniquePatientCountForClaims;

  const claimsCountFieldForTimeFrame =
    shouldUseUniquePatientCount && uniquePatientToggleFromInput
      ? "internalUniqueCount"
      : "internalCount";
  const claimsCountFieldWithoutTimeFrame =
    shouldUseUniquePatientCount && uniquePatientToggleFromInput
      ? "internalUniqueCount"
      : "count";

  const innerHitsCountForClaims =
    enableClaimsFilteringMatchedCountsUpdate && claimsFilters?.timeFrame
      ? `${claimsCountFieldForTimeFrame}_${claimsFilters!.timeFrame}_year`
      : claimsCountFieldWithoutTimeFrame;

  return innerHitsCountForClaims;
}

function buildAffiliationFiltersForMatchingHcp(
  matchingField: string,
  institutionIds: number[]
): estypes.QueryDslQueryContainer[] {
  return [
    AFFILIATIONS_INSTITUTION_IS_IOL,
    AFFILIATIONS_IS_CURRENT,
    AFFILIATIONS_IS_WORK_TYPE,
    {
      terms: {
        [matchingField]: institutionIds
      }
    }
  ];
}

function buildAffiliationFiltersForMatchingIOLAffiliatedHcps(
  matchingField: string,
  institutionIds: number[]
): estypes.QueryDslQueryContainer[] {
  return [
    ...institutionIds.map((id) => ({
      nested: {
        path: "affiliations",
        query: {
          bool: {
            filter: [
              AFFILIATIONS_IS_CURRENT,
              AFFILIATIONS_IS_WORK_TYPE,
              {
                term: {
                  [matchingField]: id
                }
              }
            ]
          }
        }
      }
    }))
  ];
}

function hasCountFilters(
  filterType: ClaimType,
  claimsFilters?: ClaimFiltersInput
): boolean {
  if (!claimsFilters) return false;
  if (filterType === "diagnoses") {
    return (
      claimsFilters.diagnosesICDMinCount != null ||
      claimsFilters.diagnosesICDMaxCount != null
    );
  } else if (filterType === "procedures") {
    return (
      claimsFilters.proceduresCPTMinCount != null ||
      claimsFilters.proceduresCPTMaxCount != null
    );
  } else {
    return (
      claimsFilters.prescriptionsMinCount != null ||
      claimsFilters.prescriptionsMaxCount != null
    );
  }
}

function areCodesEmpty(
  filterType: ClaimType,
  claimsFilters?: ClaimFiltersInput
): boolean {
  if (!claimsFilters) return false;
  if (filterType === "diagnoses") {
    return _.isEmpty(claimsFilters.diagnosesICD);
  } else if (filterType === "procedures") {
    return _.isEmpty(claimsFilters.proceduresCPT);
  } else {
    return _.isEmpty(claimsFilters.genericNames);
  }
}

function supportedRaceCategories(bucket: InstitutionFilterAggregation) {
  const unsupportedRaceCategories = [
    ASIAN_RACE_CATEGORY,
    PACIFIC_ISLANDER_RACE_CATEGORY
  ];

  return !unsupportedRaceCategories.includes(bucket.id);
}

export function buildElasticsearchFiltersFromInputFilters(
  {
    filters: inputFilters,
    consistentLocationFilter,
    projectId,
    app,
    showAllTypesOfInstitutions
  }: InstitutionsSearchInput,
  featureFlags: InstitutionSearchFeatureFlags,
  extraFilters: ReadonlyArray<QueryDslQueryContainer>,
  resolvedIndications: string[],
  bothParserResponse?: BothQueryParseResult
): QueryDslQueryContainer[] {
  const esFilters: QueryDslQueryContainer[] = [buildProjectIdFilter(projectId)];
  const {
    enableLocationFilterRegionRollup,
    enableNonIOLsInTrialLandscapeInstitutionSearch,
    enableInstitutionOrgHierarchy
  } = featureFlags;

  if (
    !enableNonIOLsInTrialLandscapeInstitutionSearch ||
    app !== Apps.TRIAL_LANDSCAPE
  ) {
    esFilters.unshift(INSTITUTION_IS_IOL);
  }

  if (
    shouldFilterOutIrrelevantOrgTypes(app, showAllTypesOfInstitutions) &&
    _.isEmpty(inputFilters?.tags)
  ) {
    esFilters.push(IRRELEVANT_ORG_TYPE_FILTER_FOR_TRIAL_LANDSCAPE);
  }

  // Filter only parent IOL docs
  esFilters.push({
    term: {
      join_field: "iol"
    }
  });

  if (!inputFilters) {
    return [...esFilters, ...extraFilters];
  }

  // Add ultimate parent ID filter if specified
  if (inputFilters.ultimateParentId) {
    esFilters.push({
      bool: {
        should: [
          {
            term: {
              ultimate_parent_id: inputFilters.ultimateParentId
            }
          },
          {
            term: {
              id: inputFilters.ultimateParentId
            }
          }
        ],
        minimum_should_match: 1
      }
    });
  }

  if (!_.isEmpty(inputFilters.countries)) {
    if (consistentLocationFilter) {
      if (enableLocationFilterRegionRollup) {
        esFilters.push({
          bool: {
            should: [
              buildTermsQuery(
                "address.country.keyword",
                inputFilters.countries!
              ),
              buildTermsQuery(
                "filters.country_level_regions",
                inputFilters.countries!
              )
            ]
          }
        });
      } else {
        esFilters.push(
          buildTermsQuery("address.country.keyword", inputFilters.countries!)
        );
      }
    } else {
      if (enableLocationFilterRegionRollup) {
        esFilters.push({
          bool: {
            should: [
              buildTermsQuery("filters.country", inputFilters.countries!),
              buildTermsQuery(
                "filters.country_level_regions",
                inputFilters.countries!
              )
            ]
          }
        });
      } else {
        esFilters.push(
          buildTermsQuery("filters.country", inputFilters.countries!)
        );
      }
    }
  }

  if (!_.isEmpty(inputFilters.excludeCountries)) {
    if (consistentLocationFilter) {
      let mustNot;
      if (enableLocationFilterRegionRollup) {
        mustNot = [
          buildTermsQuery(
            "address.country.keyword",
            inputFilters.excludeCountries!
          ),
          buildTermsQuery(
            "filters.country_level_regions",
            inputFilters.excludeCountries!
          )
        ];
      } else {
        mustNot = buildTermsQuery(
          "address.country.keyword",
          inputFilters.excludeCountries!
        );
      }

      esFilters.push({
        bool: {
          must_not: mustNot
        }
      });
    } else {
      let mustNot;
      if (enableLocationFilterRegionRollup) {
        mustNot = [
          buildTermsQuery("filters.country", inputFilters.excludeCountries!),
          buildTermsQuery(
            "filters.country_level_regions",
            inputFilters.excludeCountries!
          )
        ];
      } else {
        mustNot = buildTermsQuery(
          "filters.country",
          inputFilters.excludeCountries!
        );
      }

      esFilters.push({
        bool: {
          must_not: mustNot
        }
      });
    }
  }

  if (!_.isEmpty(inputFilters.regions)) {
    if (consistentLocationFilter) {
      if (enableLocationFilterRegionRollup) {
        esFilters.push({
          bool: {
            should: [
              buildTermsQuery("address.region.keyword", inputFilters.regions!),
              buildTermsQuery(
                "filters.state_level_regions",
                inputFilters.regions!
              )
            ]
          }
        });
      } else {
        esFilters.push(
          buildTermsQuery("address.region.keyword", inputFilters.regions!)
        );
      }
    } else {
      if (enableLocationFilterRegionRollup) {
        esFilters.push({
          bool: {
            should: [
              buildTermsQuery("filters.region", inputFilters.regions!),
              buildTermsQuery(
                "filters.state_level_regions",
                inputFilters.regions!
              )
            ]
          }
        });
      } else {
        esFilters.push(
          buildTermsQuery("filters.region", inputFilters.regions!)
        );
      }
    }
  }

  if (
    !_.isEmpty(inputFilters.excludeRegions) &&
    enableLocationFilterRegionRollup
  ) {
    if (consistentLocationFilter) {
      const mustNot = [
        buildTermsQuery("address.region.keyword", inputFilters.excludeRegions!),
        buildTermsQuery(
          "filters.state_level_regions",
          inputFilters.excludeRegions!
        )
      ];

      esFilters.push({
        bool: {
          must_not: mustNot
        }
      });
    } else {
      const mustNot = [
        buildTermsQuery("filters.region", inputFilters.excludeRegions!),
        buildTermsQuery(
          "filters.state_level_regions",
          inputFilters.excludeRegions!
        )
      ];

      esFilters.push({
        bool: {
          must_not: mustNot
        }
      });
    }
  }

  if (!_.isEmpty(inputFilters.cities)) {
    if (consistentLocationFilter) {
      if (enableLocationFilterRegionRollup) {
        esFilters.push({
          bool: {
            should: [
              buildTermsQuery("address.token", inputFilters.cities!),
              buildTermsQuery(
                "filters.city_level_regions",
                inputFilters.cities!
              )
            ]
          }
        });
      } else {
        esFilters.push(buildTermsQuery("address.token", inputFilters.cities!));
      }
    } else {
      if (enableLocationFilterRegionRollup) {
        esFilters.push({
          bool: {
            should: [
              buildTermsQuery("filters.city", inputFilters.cities!),
              buildTermsQuery(
                "filters.city_level_regions",
                inputFilters.cities!
              )
            ]
          }
        });
      } else {
        esFilters.push(buildTermsQuery("filters.city", inputFilters.cities!));
      }
    }
  }

  if (
    !_.isEmpty(inputFilters.excludeCities) &&
    enableLocationFilterRegionRollup
  ) {
    if (consistentLocationFilter) {
      const mustNot = [
        buildTermsQuery("address.token", inputFilters.excludeCities!),
        buildTermsQuery(
          "filters.city_level_regions",
          inputFilters.excludeCities!
        )
      ];

      esFilters.push({
        bool: {
          must_not: mustNot
        }
      });
    } else {
      const mustNot = [
        buildTermsQuery("filters.city", inputFilters.excludeCities!),
        buildTermsQuery(
          "filters.city_level_regions",
          inputFilters.excludeCities!
        )
      ];

      esFilters.push({
        bool: {
          must_not: mustNot
        }
      });
    }
  }

  if (!_.isEmpty(inputFilters.postalCodes)) {
    if (consistentLocationFilter) {
      esFilters.push(
        buildTermsQuery(
          "address.postal_code.keyword",
          inputFilters.postalCodes!
        )
      );
    } else {
      esFilters.push(
        buildTermsQuery("filters.postal_code", inputFilters.postalCodes!)
      );
    }
  }

  if (
    !_.isEmpty(inputFilters.excludePostalCodes) &&
    enableLocationFilterRegionRollup
  ) {
    if (consistentLocationFilter) {
      esFilters.push({
        bool: {
          must_not: buildTermsQuery(
            "address.postal_code.keyword",
            inputFilters.excludePostalCodes!
          )
        }
      });
    } else {
      esFilters.push({
        bool: {
          must_not: buildTermsQuery(
            "filters.postal_code",
            inputFilters.excludePostalCodes!
          )
        }
      });
    }
  }

  // HeatMapZoomLevel filters for France and Spain diversity
  if (!_.isEmpty(inputFilters.heatMapZoomLevel1)) {
    esFilters.push(
      buildTermsQuery(
        "heatMapZoomLevel.level1Geography",
        inputFilters.heatMapZoomLevel1!
      )
    );
  }

  if (!_.isEmpty(inputFilters.heatMapZoomLevel2)) {
    esFilters.push(
      buildTermsQuery(
        "heatMapZoomLevel.level2Geography",
        inputFilters.heatMapZoomLevel2!
      )
    );
  }

  if (!_.isEmpty(inputFilters.heatMapZoomLevel3)) {
    esFilters.push(
      buildTermsQuery(
        "heatMapZoomLevel.level3Geography",
        inputFilters.heatMapZoomLevel3!
      )
    );
  }

  if (!_.isEmpty(inputFilters.iolIds)) {
    esFilters.push(buildInstitutionIdFilter(inputFilters.iolIds!));
  }

  if (!_.isEmpty(inputFilters.geoBoundingBox)) {
    const geoBoundingBoxFilter: QueryDslQueryContainer = {
      geo_bounding_box: {
        /*
         * TODO: Remove ts-ignore once we update the elasticsearch js client
         * Types for geo_bounding box are wrong in our version of the client (7.13)
         */
        // @ts-ignore
        location: {
          ...inputFilters.geoBoundingBox
        }
      }
    };

    // TODO: hasCTMSData is currently a misnomer
    // This now filters on institutions are in the CTMS network but may or may not have CTMS data in the project slice
    // We should a have separate filter for institutions with CTMS data
    if (
      featureFlags.enableCTMSV2 &&
      inputFilters.hasCTMSData &&
      _.isEqual(inputFilters.geoBoundingBox, DEFAULT_GEO_BOUNDING_BOX)
    ) {
      esFilters.push({
        bool: {
          should: [INSTITUTION_IN_CTMS_NETWORK, geoBoundingBoxFilter],
          minimum_should_match: 1
        }
      });
    } else {
      esFilters.push(geoBoundingBoxFilter);
    }
  }

  if (!_.isEmpty(inputFilters.geoHashGrids)) {
    esFilters.push({
      bool: {
        /*
         * TODO: Remove ts-ignore once we update the elasticsearch js client
         * Types for geo_bounding box are wrong in our version of the client (7.13)
         */
        // @ts-ignore
        should:
          inputFilters.geoHashGrids?.map((geoHash: estypes.GeoHash) => ({
            geo_bounding_box: {
              location: {
                // In order to specify a bounding box that would match entire area of a geohash
                // the geohash can be specified in both top_left and bottom_right parameters
                // https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-geo-bounding-box-query.html#_geohash_2
                top_left: geoHash,
                bottom_right: geoHash
              }
            }
          })) || []
      }
    });
  }

  if (!_.isEmpty(inputFilters.geoDistance)) {
    esFilters.push({
      geo_distance: {
        distance: `${inputFilters.geoDistance.distance}${inputFilters.geoDistance.distanceUnit}`,
        // @ts-ignore
        location: inputFilters.geoDistance.geoPosition
      }
    });
  }

  esFilters.push(...buildGeoShapeFilter(inputFilters));

  if (!_.isEmpty(inputFilters.orgTypes)) {
    esFilters.push({
      terms: {
        ["filters.orgTypes"]: inputFilters.orgTypes!
      }
    });
  }

  if (!_.isEmpty(inputFilters.trialEnrollmentRate)) {
    if (
      inputFilters.indications ||
      (resolvedIndications && resolvedIndications.length > 0)
    ) {
      const mergedIndicationsArray: string[] = [
        ...new Set([
          ...(resolvedIndications ?? []),
          ...(inputFilters.indications ?? [])
        ])
      ];

      esFilters.push({
        nested: {
          path: "enrollment_rates_by_indication",
          inner_hits: {},
          query: {
            bool: {
              must: [
                {
                  terms: {
                    ["enrollment_rates_by_indication.indication"]:
                      mergedIndicationsArray
                  }
                },
                {
                  range: {
                    "enrollment_rates_by_indication.enrollment_rate": {
                      gte: inputFilters?.trialEnrollmentRate?.min,
                      lte: inputFilters?.trialEnrollmentRate?.max
                    }
                  }
                }
              ]
            }
          }
        }
      });
    } else {
      esFilters.push({
        range: {
          trialEnrollmentRate: {
            gte: inputFilters?.trialEnrollmentRate?.min,
            lte: inputFilters?.trialEnrollmentRate?.max
          }
        }
      });
    }
  }

  // TODO: hasCTMSData is currently a misnomer
  // This now filters on institutions are in the CTMS network but may or may not have CTMS data in the project slice
  // We should a have separate filter for institutions with CTMS data
  if (featureFlags.enableCTMSV2 && inputFilters.hasCTMSData) {
    esFilters.push(INSTITUTION_IN_CTMS_NETWORK);
  }
  const hasTimeFrame = !!inputFilters.claimsFilters?.timeFrame;
  const claimTypes: ClaimType[] = ["diagnoses", "procedures", "prescriptions"];
  const claimsFiltersFromInput = inputFilters.claimsFilters ?? undefined;
  claimTypes.forEach((filterType) => {
    const isCountFilterPresent = hasCountFilters(
      filterType,
      claimsFiltersFromInput
    );
    const areCodesEmptyValue = areCodesEmpty(
      filterType,
      claimsFiltersFromInput
    );

    if (isCountFilterPresent && areCodesEmptyValue) {
      esFilters.push(
        buildRangeFilterForClaims(
          claimsFiltersFromInput!,
          filterType,
          featureFlags
        )
      );
    }
  });
  if (hasTimeFrame) {
    const isCountFilterPresentDiagnoses = hasCountFilters(
      "diagnoses",
      claimsFiltersFromInput
    );
    const isCountFilterPresentProcedures = hasCountFilters(
      "procedures",
      claimsFiltersFromInput
    );
    const isCountFilterPresentPrescriptions = hasCountFilters(
      "prescriptions",
      claimsFiltersFromInput
    );
    const shouldAddDiagnosesShouldClause =
      areCodesEmpty("diagnoses", claimsFiltersFromInput) &&
      !isCountFilterPresentDiagnoses;
    const shouldAddProceduresShouldClause =
      areCodesEmpty("procedures", claimsFiltersFromInput) &&
      !isCountFilterPresentProcedures;
    const shouldAddPrescriptionsShouldClause =
      areCodesEmpty("prescriptions", claimsFiltersFromInput) &&
      !isCountFilterPresentPrescriptions;

    if (
      shouldAddDiagnosesShouldClause &&
      shouldAddProceduresShouldClause &&
      shouldAddPrescriptionsShouldClause
    ) {
      esFilters.push({
        bool: {
          should: [
            buildRangeFilterForClaims(
              claimsFiltersFromInput!,
              "diagnoses",
              featureFlags
            ),
            buildRangeFilterForClaims(
              claimsFiltersFromInput!,
              "procedures",
              featureFlags
            ),
            buildRangeFilterForClaims(
              claimsFiltersFromInput!,
              "prescriptions",
              featureFlags
            )
          ],
          minimum_should_match: 1
        }
      });
    }
  }
  if (!_.isEmpty(inputFilters.diversityFilters?.raceMix)) {
    const raceMixRanges = buildRaceMixRanges(
      inputFilters.diversityFilters!.raceMix!
    );

    const raceMixQueryContainers = {
      bool: {
        should: raceMixRanges,
        minimum_should_match: 1
      }
    };

    esFilters.push(raceMixQueryContainers);
  }

  if (filtersPresent(inputFilters?.diversityFilters)) {
    const diversityFilters = buildElasticsearchQueryForDiversityFilters(
      inputFilters!.diversityFilters!
    );

    esFilters.push(...diversityFilters);
  }
  if (
    !_.isUndefined(inputFilters?.trialsFilters?.maxCount) ||
    !_.isUndefined(inputFilters?.trialsFilters?.minCount)
  ) {
    const defaultMin = inputFilters.trialsFilters?.maxCount === 0 ? 0 : 1;
    //Special case for trial max count 0 since institutions without trials_count field also have 0 trials
    if (
      inputFilters.trialsFilters?.maxCount === 0 &&
      (_.isUndefined(inputFilters.trialsFilters?.minCount) ||
        inputFilters.trialsFilters?.minCount === 0)
    ) {
      const minMaxTrialsFilterWithExistence: QueryDslQueryContainer = {
        bool: {
          should: [
            {
              range: {
                trials_count: {
                  gte: inputFilters?.trialsFilters?.minCount ?? defaultMin,
                  lte: inputFilters?.trialsFilters?.maxCount ?? undefined
                }
              }
            },
            {
              bool: {
                must_not: {
                  exists: {
                    field: "trials_count"
                  }
                }
              }
            }
          ]
        }
      };
      esFilters.push(minMaxTrialsFilterWithExistence);
    } else {
      const minMaxTrialsFilter: QueryDslQueryContainer = {
        range: {
          trials_count: {
            gte: inputFilters?.trialsFilters?.minCount ?? defaultMin,
            lte: inputFilters?.trialsFilters?.maxCount ?? undefined
          }
        }
      };
      esFilters.push(minMaxTrialsFilter);
    }
  }

  if (!_.isEmpty(inputFilters.excludeAcademicTypes)) {
    const must_not: QueryDslQueryContainer[] = [];
    if (
      inputFilters.excludeAcademicTypes?.includes(AcademicTypesEnum.hospital)
    ) {
      must_not.push({
        term: {
          "flags.hospital": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.privatePractice
      )
    ) {
      must_not.push({
        term: {
          "flags.private_practice.private_practice": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.privatePracticeSmall
      )
    ) {
      must_not.push({
        term: {
          "flags.private_practice.small": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.privatePracticeLarge
      )
    ) {
      must_not.push({
        term: {
          "flags.private_practice.large": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.nihCommunitySite
      )
    ) {
      must_not.push({
        term: {
          "flags.nih_community_site": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.minorityCommunitySite
      )
    ) {
      must_not.push({
        term: {
          "flags.minority_community_site": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(AcademicTypesEnum.government)
    ) {
      must_not.push({
        term: {
          "flags.government.government": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.militaryGovernment
      )
    ) {
      must_not.push({
        term: {
          "flags.government.military": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.veteransAffairGovernment
      )
    ) {
      must_not.push({
        term: {
          "flags.government.veterans_affairs": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.cancerCenter
      )
    ) {
      must_not.push({
        bool: {
          should: [
            { term: { "flags.nci_designated_cancer_center": true } },
            {
              term: {
                "flags.national_comprehensive_cancer_network_member": true
              }
            }
          ]
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.nciDesignatedCancerCenter
      )
    ) {
      must_not.push({
        term: {
          "flags.nci_designated_cancer_center": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.nationalComprehensiveCancerNetworkMember
      )
    ) {
      must_not.push({
        term: {
          "flags.national_comprehensive_cancer_network_member": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.teachingHospital
      )
    ) {
      must_not.push({
        term: {
          "flags.teaching_hospital": true
        }
      });
    }
    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.researchFacility
      )
    ) {
      must_not.push({
        term: {
          "flags.research_facility": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.contractResearchOrganization
      )
    ) {
      must_not.push({
        term: {
          "flags.cro": true
        }
      });
    }

    if (
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.communityHospital
      ) ||
      inputFilters.excludeAcademicTypes?.includes(
        AcademicTypesEnum.communitySite
      )
    ) {
      must_not.push({
        bool: {
          should: [
            { term: { "flags.nih_community_site": true } },
            { term: { "flags.minority_community_site": true } }
          ]
        }
      });
    }

    esFilters.push({
      bool: {
        must_not
      }
    });
  }

  if (!_.isEmpty(inputFilters.includeAcademicTypes)) {
    const academicFilters: estypes.QueryDslQueryContainer[] = [];
    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.communityHospital
      ) ||
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.communitySite
      )
    ) {
      academicFilters.push({
        bool: {
          should: [
            {
              term: {
                "flags.nih_community_site": true
              }
            },
            {
              term: {
                "flags.minority_community_site": true
              }
            }
          ],
          minimum_should_match: 1
        }
      });
    }

    if (
      inputFilters.includeAcademicTypes?.includes(AcademicTypesEnum.hospital)
    ) {
      academicFilters.push({
        term: {
          "flags.hospital": true
        }
      });
    }
    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.privatePractice
      )
    ) {
      academicFilters.push({
        term: {
          "flags.private_practice.private_practice": true
        }
      });
    }
    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.privatePracticeSmall
      )
    ) {
      academicFilters.push({
        term: {
          "flags.private_practice.small": true
        }
      });
    }
    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.privatePracticeLarge
      )
    ) {
      academicFilters.push({
        term: {
          "flags.private_practice.large": true
        }
      });
    }
    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.nihCommunitySite
      )
    ) {
      academicFilters.push({
        term: {
          "flags.nih_community_site": true
        }
      });
    }
    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.minorityCommunitySite
      )
    ) {
      academicFilters.push({
        term: {
          "flags.minority_community_site": true
        }
      });
    }
    if (
      inputFilters.includeAcademicTypes?.includes(AcademicTypesEnum.government)
    ) {
      academicFilters.push({
        term: {
          "flags.government.government": true
        }
      });
    }
    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.militaryGovernment
      )
    ) {
      academicFilters.push({
        term: {
          "flags.government.military": true
        }
      });
    }
    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.veteransAffairGovernment
      )
    ) {
      academicFilters.push({
        term: {
          "flags.government.veterans_affairs": true
        }
      });
    }

    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.teachingHospital
      )
    ) {
      academicFilters.push({
        term: {
          "flags.teaching_hospital": true
        }
      });
    }

    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.cancerCenter
      )
    ) {
      academicFilters.push({
        bool: {
          should: [
            {
              term: {
                "flags.national_comprehensive_cancer_network_member": true
              }
            },
            {
              term: {
                "flags.nci_designated_cancer_center": true
              }
            }
          ],
          minimum_should_match: 1
        }
      });
    }

    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.nationalComprehensiveCancerNetworkMember
      )
    ) {
      academicFilters.push({
        term: {
          "flags.national_comprehensive_cancer_network_member": true
        }
      });
    }

    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.nciDesignatedCancerCenter
      )
    ) {
      academicFilters.push({
        term: {
          "flags.nci_designated_cancer_center": true
        }
      });
    }

    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.researchFacility
      )
    ) {
      academicFilters.push({
        term: {
          "flags.research_facility": true
        }
      });
    }

    if (
      inputFilters.includeAcademicTypes?.includes(
        AcademicTypesEnum.contractResearchOrganization
      )
    ) {
      academicFilters.push({
        term: {
          "flags.cro": true
        }
      });
    }

    esFilters.push({
      bool: {
        should: academicFilters
      }
    });
  }

  if (featureFlags.enableBothOperatorForSearch && bothParserResponse) {
    esFilters.push(...buildBothOperatorFilters(bothParserResponse));
  }

  if (enableInstitutionOrgHierarchy && inputFilters.isPartOfSystem) {
    // Find all institutions that are part of a system.
    // We also need to check that the institution has a child affiliation count or ultimate parent id
    // to ensure the institution has affiliated sites in our system.
    esFilters.push({
      bool: {
        must: [
          {
            bool: {
              should: [
                {
                  term: {
                    "flags.part_of_system.health_system": { value: true }
                  }
                },
                {
                  term: {
                    "flags.part_of_system.part_of_system": { value: true }
                  }
                },
                {
                  term: {
                    "flags.part_of_system.university_system": { value: true }
                  }
                }
              ]
            }
          },
          {
            bool: {
              should: [
                { exists: { field: "ultimate_parent_id" } },
                { range: { child_affiliation_count: { gt: 0 } } }
              ]
            }
          }
        ]
      }
    });
  }

  return [...esFilters, ...extraFilters];
}

export function buildInstitutionIdFilter(
  institutionIds: (string | number)[]
): QueryDslQueryContainer {
  const filters: QueryDslQueryContainer[] = [];
  const numericInstitutionIds = institutionIds.filter(isNumeric);
  const h1dnInstitutionIds = _.difference(
    institutionIds,
    numericInstitutionIds
  ) as string[];

  if (numericInstitutionIds.length) {
    filters.push(
      buildTermsQuery(MASTER_ORGANIZATION_ID, numericInstitutionIds)
    );
  }

  if (h1dnInstitutionIds.length) {
    filters.push(buildInstitutionH1dnIdFilter(h1dnInstitutionIds));
  }

  if (filters.length === 1) {
    return filters[0];
  }

  return {
    bool: {
      should: filters,
      minimum_should_match: 1
    }
  };
}

function buildRangeFilterForClaims(
  claimsFilters: ClaimFiltersInput,
  type: ClaimType,
  featureFlags: InstitutionSearchFeatureFlags,
  nestedQuery = false
): QueryDslQueryContainer {
  const {
    enableUniquePatientCountForClaims,
    disableUniquePatientCountForOnlyProcedures
  } = featureFlags;
  const isProcedureWithUniqueCount =
    type === "procedures" && !disableUniquePatientCountForOnlyProcedures;
  const isDiagnosis = type === "diagnoses";

  const shouldUseUniquePatientCount =
    (isProcedureWithUniqueCount || isDiagnosis) &&
    enableUniquePatientCountForClaims;

  let claimsCountFieldForNestedQuery = shouldUseUniquePatientCount
    ? "internalUniqueCount"
    : "count";
  const uniquePatientToggleFromInput =
    claimsFilters?.showUniquePatients ?? false;
  let claimsCountField =
    shouldUseUniquePatientCount && uniquePatientToggleFromInput
      ? "internalUniqueCount"
      : "internalCount";
  if (type === "prescriptions") {
    claimsCountField = claimsCountFieldForNestedQuery = "num_prescriptions";
  }
  const timeFrame = claimsFilters?.timeFrame
    ? `${claimsCountField}_${claimsFilters.timeFrame}_year`
    : undefined;
  const rangeFieldNameForClaims = (claimType: string) => {
    if (timeFrame && nestedQuery) {
      return `${claimType}.${timeFrame}`;
    } else if (timeFrame) {
      if (claimType === "prescriptions") {
        return timeFrame;
      }
      return `${timeFrame}`.replace("internal", `${claimType}`);
    }

    if (nestedQuery) {
      return `${claimType}.${claimsCountFieldForNestedQuery}`;
    }

    if (claimType === "prescriptions") {
      return "num_prescriptions";
    }
    const claimsTotalCountFieldForDiagnoses =
      enableUniquePatientCountForClaims && uniquePatientToggleFromInput
        ? "diagnosesUniqueCount"
        : "diagnosis_count";
    const claimsTotalCountFieldForProcedures =
      shouldUseUniquePatientCount && uniquePatientToggleFromInput
        ? "proceduresUniqueCount"
        : "procedure_count";
    return claimType === "diagnoses"
      ? claimsTotalCountFieldForDiagnoses
      : claimsTotalCountFieldForProcedures;
  };

  const typeMapping: Record<ClaimType, ClaimsRangeTypeMapping> = {
    diagnoses: {
      countPropertyMin: "diagnosesICDMinCount",
      countPropertyMax: "diagnosesICDMaxCount",
      fieldName: () => rangeFieldNameForClaims("diagnoses")
    },
    procedures: {
      countPropertyMin: "proceduresCPTMinCount",
      countPropertyMax: "proceduresCPTMaxCount",
      fieldName: () => rangeFieldNameForClaims("procedures")
    },
    prescriptions: {
      countPropertyMin: "prescriptionsMinCount",
      countPropertyMax: "prescriptionsMaxCount",
      fieldName: () => rangeFieldNameForClaims("prescriptions")
    },
    ccsr: {
      countPropertyMin: "diagnosesICDMinCount",
      countPropertyMax: "diagnosesICDMaxCount",
      fieldName: () => rangeFieldNameForClaims("diagnoses")
    },
    ccsr_px: {
      countPropertyMin: "proceduresCPTMinCount",
      countPropertyMax: "proceduresCPTMaxCount",
      fieldName: () => rangeFieldNameForClaims("procedures")
    }
  };

  const { countPropertyMin, countPropertyMax, fieldName } = typeMapping[type];
  const minCount =
    (claimsFilters[countPropertyMin as keyof ClaimFiltersInput] as number) ??
    undefined;
  const maxCount =
    (claimsFilters[countPropertyMax as keyof ClaimFiltersInput] as number) ??
    undefined;

  return {
    range: {
      [fieldName()]: {
        gte: getMinClaimsCount(claimsFilters?.timeFrame, minCount),
        lte: maxCount
      }
    }
  };
}

function buildProjectIdFilter(projectId: string): QueryDslQueryContainer {
  return {
    bool: {
      should: [
        {
          bool: {
            must: [
              buildFieldExistsQuery(PROJECT_IDS),
              buildTermsQuery(PROJECT_IDS, [projectId])
            ]
          }
        },
        {
          bool: {
            must_not: [buildFieldExistsQuery(PROJECT_IDS)]
          }
        }
      ]
    }
  };
}

function buildInstitutionH1dnIdFilter(
  h1dnInstitutionIds: string[]
): QueryDslQueryContainer {
  return {
    bool: {
      should: [
        buildTermsQuery("id", h1dnInstitutionIds),
        buildTermsQuery("groupH1dnOrganizationId", h1dnInstitutionIds)
      ],
      minimum_should_match: 1
    }
  };
}

function buildPatientsDiversityRatioRangeFilter(race: MinMaxFilter) {
  return {
    range: {
      [`patientsDiversityRatio.${race.key}`]: {
        gt: 0, // This condition makes sure that ratio is non-zero
        gte: _.isNumber(race.min) ? race.min / 100 : MINIMUM_RACE_THRESHOLD,
        lte: _.isNumber(race.max) ? race.max / 100 : MAXIMUM_RACE_THRESHOLD
      }
    }
  };
}

function buildRaceMixRanges(raceMix: MinMaxFilter[]): QueryDslQueryContainer[] {
  return raceMix.map((race) => buildPatientsDiversityRatioRangeFilter(race));
}

export function filtersPresent(
  filters:
    | InstitutionTrialsFiltersInput
    | ClaimFiltersInput
    | DiversityFiltersInput
    | null
    | undefined
) {
  if (_.isNil(filters)) {
    return false;
  }

  const nonEmptyValues = _.pickBy(filters, (value) => {
    return !(_.isNull(value) || (_.isArray(value) && _.isEmpty(value)));
  });

  return !_.isEmpty(nonEmptyValues);
}

function buildElasticsearchQueryForDiversityFilters(
  diversityFilters: DiversityFiltersInput
): NestedClaimsQuery[] {
  const queries: NestedClaimsQuery[] = [];
  const PATIENT_DIVERSITY_PATH = "patientDiversity";

  const buildQuery = (field: string, filters: QueryDslQueryContainer[]) => {
    const query = {
      path: `${PATIENT_DIVERSITY_PATH}.${field}`,
      query: {
        bool: {
          filter: filters
        }
      }
    };

    return { nested: query };
  };

  if (!_.isEmpty(diversityFilters.sex)) {
    const filters = [
      buildTermsQuery(
        `${PATIENT_DIVERSITY_PATH}.sex.sex`,
        diversityFilters.sex!
      )
    ];

    queries.push(buildQuery("sex", filters));
  }

  if (!_.isEmpty(diversityFilters.race)) {
    const filters = [
      buildTermsQuery(
        `${PATIENT_DIVERSITY_PATH}.race.race`,
        diversityFilters.race!
      )
    ];

    queries.push(buildQuery("race", filters));
  }

  return queries;
}

function getMinClaimsCount(
  timeFrame: string | number | undefined | null,
  minCount: number | undefined
): number | undefined {
  if (timeFrame) {
    return minCount || AT_LEAST_ONE;
  }

  return minCount;
}

function getMinClaimsCountForNestedFunctionScore(
  timeFrame: number | null,
  minCount: number | null
): number | null {
  if (timeFrame) {
    return minCount || AT_LEAST_ONE;
  }

  return minCount;
}

function isNumeric(value: string | number): boolean {
  return /^\d+$/.test(value.toString());
}

function buildGeoShapeFilter(
  inputFilters: InstitutionSearchFilter
): QueryDslQueryContainer[] {
  if (
    !inputFilters ||
    _.isEmpty(inputFilters.geoShape) ||
    _.isEmpty(inputFilters.geoShape?.coordinates)
  ) {
    return [];
  }

  return [
    {
      geo_shape: {
        location: {
          shape: {
            type: inputFilters.geoShape.type,
            //@ts-ignore
            coordinates: [
              inputFilters.geoShape.coordinates!.map(
                (point: estypes.LatLonGeoLocation) => [point.lon, point.lat],
                (point: { lat: any; lon: any }) => [point.lon, point.lat]
              )
            ]
          }
        }
      }
    }
  ];
}

function getAgeSortValues(
  input: InstitutionsSearchInput
): [boolean, string[], number] {
  const ageRangesSelected = input.filters?.diversityFilters?.ageRange ?? [];
  const isAgeSort =
    input.sortBy === InstitutionSortOptions.AGE && ageRangesSelected.length > 0;
  const ageSortWeight = isAgeSort ? 1 : 0;
  return [isAgeSort, ageRangesSelected, ageSortWeight];
}

function buildAgeSortQuery(
  ageRanges: string[],
  weight: number
): QueryDslQueryContainer {
  const ageRangeFunction: QueryDslNestedQuery = {
    path: "patientDiversity.age",
    score_mode: "sum",
    query: {
      function_score: {
        query: {
          bool: {
            filter: [
              {
                terms: {
                  "patientDiversity.age.range": ageRanges
                }
              }
            ]
          }
        },
        boost_mode: "replace",
        functions: [
          {
            field_value_factor: {
              field: "patientDiversity.age.count",
              modifier: "none",
              missing: 0
            },
            weight
          }
        ]
      }
    }
  };
  return { nested: ageRangeFunction };
}

function buildUkDiversityHeatmapAggregation(
  searchQuery?: string | undefined,
  analyzer?: string,
  claimsCode?: ClaimsCode
): Record<string, AggregationsAggregationContainer> {
  const filterClause = getUkDiversityFilterClause(
    searchQuery,
    analyzer,
    claimsCode
  );

  const aggs: Record<string, AggregationsAggregationContainer> = {};
  for (const patientCountField of Object.values(UkPatientCountField)) {
    aggs[patientCountField] = {
      sum: {
        field: `patientClaimsUk.${patientCountField}`
      }
    };
  }

  return {
    heatmap: {
      terms: {
        field: "subIcbLocationCode",
        size: 100
      },
      aggs: {
        nested_agg: {
          nested: {
            path: "patientClaimsUk"
          },
          aggs: {
            filtered_matching: {
              filter: {
                bool: {
                  filter: filterClause
                }
              },
              aggs
            }
          }
        }
      }
    }
  };
}

function buildFranceDiversityHeatmapAggregation(
  zoomLevel: HeatMapZoomLevel = HeatMapZoomLevel.Level1Geography,
  searchQuery?: string | undefined,
  analyzer?: string,
  claimsCode?: ClaimsCode
): Record<string, AggregationsAggregationContainer> {
  const filterClause = getFranceDiversityFilterClause(
    searchQuery,
    analyzer,
    claimsCode
  );

  const aggs: Record<string, AggregationsAggregationContainer> = {};
  for (const patientCountField of Object.values(FrancePatientCountField)) {
    aggs[patientCountField] = {
      sum: {
        field: `patientClaimsFrance.${patientCountField}`
      }
    };
  }

  return {
    france_heatmap: {
      terms: {
        field: `heatMapZoomLevel.${zoomLevel}`,
        size: 100
      },
      aggs: {
        nested_agg: {
          nested: {
            path: "patientClaimsFrance"
          },
          aggs: {
            filtered_matching: {
              filter: {
                bool: {
                  filter: filterClause
                }
              },
              aggs
            }
          }
        }
      }
    }
  };
}

function buildFranceDiversityDashboardAggregation(
  inputFilters?: InstitutionSearchFilter
): Record<string, AggregationsAggregationContainer> {
  if (
    !inputFilters ||
    (!inputFilters.geoBoundingBox &&
      !inputFilters.regions &&
      !inputFilters.cities &&
      !inputFilters.heatMapZoomLevel1 &&
      !inputFilters.heatMapZoomLevel2)
  )
    return {};

  const esFilters: QueryDslQueryContainer[] = [
    {
      term: {
        ["filters.country"]: "fr"
      }
    }
  ];

  if (inputFilters.geoBoundingBox) {
    esFilters.push({
      geo_bounding_box: {
        // @ts-ignore
        location: { ...inputFilters.geoBoundingBox }
      }
    });
  }

  if (!_.isEmpty(inputFilters.regions)) {
    esFilters.push(
      buildTermsQuery("address.region.keyword", inputFilters.regions!)
    );
  }

  if (!_.isEmpty(inputFilters.cities)) {
    esFilters.push(buildTermsQuery("address.token", inputFilters.cities!));
  }

  if (!_.isEmpty(inputFilters.heatMapZoomLevel1)) {
    esFilters.push(
      buildTermsQuery(
        "heatMapZoomLevel.level1Geography",
        inputFilters.heatMapZoomLevel1!
      )
    );
  }

  if (!_.isEmpty(inputFilters.heatMapZoomLevel2)) {
    esFilters.push(
      buildTermsQuery(
        "heatMapZoomLevel.level2Geography",
        inputFilters.heatMapZoomLevel2!
      )
    );
  }

  return {
    france_diversity_dashboard: {
      global: {},
      aggs: {
        location_filtered: {
          filter: {
            bool: {
              filter: esFilters
            }
          },
          aggs: {
            nationality_counts: {
              nested: {
                path: "patientDiversity.nationality"
              },
              aggs: {
                total_nationality_count: {
                  sum: {
                    field: "patientDiversity.nationality.count"
                  }
                },
                by_nationality: {
                  terms: {
                    field: "patientDiversity.nationality.nationality",
                    size: Object.values(PatientNationalityFrance).length
                  },
                  aggs: {
                    count: {
                      sum: {
                        field: "patientDiversity.nationality.count"
                      }
                    }
                  }
                }
              }
            },
            age_counts: {
              nested: {
                path: "patientDiversity.age"
              },
              aggs: {
                total_age_count: {
                  sum: {
                    field: "patientDiversity.age.count"
                  }
                },
                by_age: {
                  terms: {
                    field: "patientDiversity.age.range"
                  },
                  aggs: {
                    count: {
                      sum: {
                        field: "patientDiversity.age.count"
                      }
                    }
                  }
                }
              }
            },
            gender_counts: {
              nested: {
                path: "patientDiversity.sex"
              },
              aggs: {
                total_gender_count: {
                  sum: {
                    field: "patientDiversity.sex.count"
                  }
                },
                by_gender: {
                  terms: {
                    field: "patientDiversity.sex.sex"
                  },
                  aggs: {
                    count: {
                      sum: {
                        field: "patientDiversity.sex.count"
                      }
                    }
                  }
                }
              }
            },
            education_counts: {
              nested: {
                path: "patientDiversity.education"
              },
              aggs: {
                total_education_count: {
                  sum: {
                    field: "patientDiversity.education.count"
                  }
                },
                by_education: {
                  terms: {
                    field: "patientDiversity.education.education"
                  },
                  aggs: {
                    count: {
                      sum: {
                        field: "patientDiversity.education.count"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  };
}

function buildSpainDiversityHeatmapAggregation(
  zoomLevel: HeatMapZoomLevel = HeatMapZoomLevel.Level1Geography,
  searchQuery?: string | undefined,
  analyzer?: string,
  claimsCode?: ClaimsCode
): Record<string, AggregationsAggregationContainer> {
  const filterClause = getSpainDiversityFilterClause(
    searchQuery,
    analyzer,
    claimsCode
  );

  const aggs: Record<string, AggregationsAggregationContainer> = {};
  for (const patientCountField of Object.values(SpainPatientCountField)) {
    aggs[patientCountField] = {
      sum: {
        field: `patientClaimsSpain.${patientCountField}`
      }
    };
  }

  return {
    spain_heatmap: {
      terms: {
        field: `heatMapZoomLevel.${zoomLevel}`,
        size: 100
      },
      aggs: {
        nested_agg: {
          nested: {
            path: "patientClaimsSpain"
          },
          aggs: {
            filtered_matching: {
              filter: {
                bool: {
                  filter: filterClause
                }
              },
              aggs
            }
          }
        }
      }
    }
  };
}

function buildSpainDiversityDashboardAggregation(
  inputFilters?: InstitutionSearchFilter
): Record<string, AggregationsAggregationContainer> {
  if (
    !inputFilters ||
    (!inputFilters.geoBoundingBox &&
      !inputFilters.regions &&
      !inputFilters.cities &&
      !inputFilters.heatMapZoomLevel1 &&
      !inputFilters.heatMapZoomLevel2)
  )
    return {};

  const esFilters: QueryDslQueryContainer[] = [
    {
      term: {
        ["filters.country"]: "es"
      }
    }
  ];

  if (inputFilters.geoBoundingBox) {
    esFilters.push({
      geo_bounding_box: {
        // @ts-ignore
        location: { ...inputFilters.geoBoundingBox }
      }
    });
  }

  if (!_.isEmpty(inputFilters.regions)) {
    esFilters.push(
      buildTermsQuery("address.region.keyword", inputFilters.regions!)
    );
  }

  if (!_.isEmpty(inputFilters.cities)) {
    esFilters.push(buildTermsQuery("address.token", inputFilters.cities!));
  }

  if (!_.isEmpty(inputFilters.heatMapZoomLevel1)) {
    esFilters.push(
      buildTermsQuery(
        "heatMapZoomLevel.level1Geography",
        inputFilters.heatMapZoomLevel1!
      )
    );
  }

  if (!_.isEmpty(inputFilters.heatMapZoomLevel2)) {
    esFilters.push(
      buildTermsQuery(
        "heatMapZoomLevel.level2Geography",
        inputFilters.heatMapZoomLevel2!
      )
    );
  }

  return {
    spain_diversity_dashboard: {
      global: {},
      aggs: {
        location_filtered: {
          filter: {
            bool: {
              filter: esFilters
            }
          },
          aggs: {
            nationality_counts: {
              nested: {
                path: "patientDiversity.nationality"
              },
              aggs: {
                total_nationality_count: {
                  sum: {
                    field: "patientDiversity.nationality.count"
                  }
                },
                by_nationality: {
                  terms: {
                    field: "patientDiversity.nationality.nationality",
                    size: Object.values(PatientNationalitySpain).length
                  },
                  aggs: {
                    count: {
                      sum: {
                        field: "patientDiversity.nationality.count"
                      }
                    }
                  }
                }
              }
            },
            age_counts: {
              nested: {
                path: "patientDiversity.age"
              },
              aggs: {
                total_age_count: {
                  sum: {
                    field: "patientDiversity.age.count"
                  }
                },
                by_age: {
                  terms: {
                    field: "patientDiversity.age.range",
                    size: SPAIN_DIVERSITY_AGE_RANGE_COUNT
                  },
                  aggs: {
                    count: {
                      sum: {
                        field: "patientDiversity.age.count"
                      }
                    }
                  }
                }
              }
            },
            gender_counts: {
              nested: {
                path: "patientDiversity.sex"
              },
              aggs: {
                total_gender_count: {
                  sum: {
                    field: "patientDiversity.sex.count"
                  }
                },
                by_gender: {
                  terms: {
                    field: "patientDiversity.sex.sex"
                  },
                  aggs: {
                    count: {
                      sum: {
                        field: "patientDiversity.sex.count"
                      }
                    }
                  }
                }
              }
            },
            education_counts: {
              nested: {
                path: "patientDiversity.education"
              },
              aggs: {
                total_education_count: {
                  sum: {
                    field: "patientDiversity.education.count"
                  }
                },
                by_education: {
                  terms: {
                    field: "patientDiversity.education.education"
                  },
                  aggs: {
                    count: {
                      sum: {
                        field: "patientDiversity.education.count"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  };
}

function buildChildrenMatchedCountAggregation(
  idToIgnore: string
): Record<string, AggregationsAggregationContainer> {
  return {
    children_matched_count: {
      filter: {
        bool: {
          must_not: [
            {
              term: {
                id: idToIgnore
              }
            }
          ]
        }
      },
      aggs: {
        matching_children_count: {
          value_count: {
            field: "id"
          }
        }
      }
    }
  };
}

export function buildAggregationsForSearchRequest(
  input: Readonly<InstitutionsSearchInput>,
  featureFlags: InstitutionSearchFeatureFlags,
  queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
  searchQuery?: string | undefined,
  analyzer?: string,
  claimsCode?: ClaimsCode
) {
  let aggs = {};

  if (
    ((input.filters?.precision || input.filters?.zoom) &&
      input.filters?.geoBoundingBox) ||
    input.filters?.geoDistance
  ) {
    aggs = {
      ...aggs,
      ...buildGeoLocationAggregations(input, queryUnderstandingServiceResponse)
    };
  }

  if (input.filters?.zoom && input.filters?.computeResultGeoClusters === true) {
    aggs = {
      ...aggs,
      ...buildGeoClusteringAggregations(input.filters.zoom)
    };
  }

  aggs = getEuropeanDiversityAggregations(
    input,
    aggs,
    searchQuery,
    analyzer,
    claimsCode
  );

  if (input.filters?.ultimateParentId) {
    aggs = {
      ...aggs,
      ...buildChildrenMatchedCountAggregation(input.filters.ultimateParentId)
    };
  }

  return aggs;
}

export function buildFilterAggregatesForTopOptions(
  consistentLocationFilter: boolean | undefined,
  featureFlags: InstitutionSearchFeatureFlags,
  prefix?: string
): Record<string, AggregationsAggregationContainer> {
  const locationAggregations = buildLocationFilterAggregates(
    consistentLocationFilter,
    featureFlags,
    prefix
  );

  return {
    ...locationAggregations,
    orgTypes: {
      terms: {
        field: "orgTypes.keyword",
        size: AGGREGATE_HITS
      }
    },
    orgTypesLevel2: {
      terms: {
        field: "orgTypesLevel2.keyword",
        size: AGGREGATE_HITS
      }
    },
    orgTypesLevel3: {
      terms: {
        field: "orgTypesLevel3.keyword",
        size: AGGREGATE_HITS
      }
    }
  };
}

function buildOrgTypeAggregates(
  field: string
): Record<string, AggregationsAggregationContainer> {
  return {
    [field]: {
      terms: {
        field: `${field}.keyword`,
        size: AGGREGATE_HITS
      }
    }
  };
}

function buildLocationFilterAggregates(
  consistentLocationFilter: boolean | undefined,
  featureFlags: InstitutionSearchFeatureFlags,
  prefix?: string
): Record<string, AggregationsAggregationContainer> {
  if (featureFlags.enableLocationFilterRegionRollup) {
    return {
      country: {
        filter: {
          bool: {
            filter: buildFilterForLocationAggregation(prefix, "country")
          }
        },
        aggs: {
          filtered_matching: {
            terms: {
              field: consistentLocationFilter
                ? "address.country.keyword"
                : "filters.country",
              size: AGGREGATE_HITS
            },
            aggs: {
              regions_in: {
                terms: {
                  field: "filters.country_level_regions",
                  exclude: ZERO_LENGTH_FILTER_VALUE
                }
              }
            }
          }
        }
      },
      region: {
        filter: {
          bool: {
            filter: buildFilterForLocationAggregation(prefix, "region")
          }
        },
        aggs: {
          filtered_matching: {
            terms: {
              field: consistentLocationFilter
                ? "address.region.keyword"
                : "filters.region",
              size: AGGREGATE_HITS
            },
            aggs: {
              regions_in: {
                terms: {
                  field: "filters.state_level_regions",
                  exclude: ZERO_LENGTH_FILTER_VALUE
                }
              }
            }
          }
        }
      },
      city: {
        filter: {
          bool: {
            filter: buildFilterForLocationAggregation(prefix, "city")
          }
        },
        aggs: {
          filtered_matching: {
            terms: {
              field: consistentLocationFilter
                ? "address.token"
                : "filters.city",
              size: AGGREGATE_HITS
            },
            aggs: {
              regions_in: {
                terms: {
                  field: "filters.city_level_regions",
                  exclude: ZERO_LENGTH_FILTER_VALUE
                }
              }
            }
          }
        }
      },
      postal_code: {
        terms: {
          field: consistentLocationFilter
            ? "address.postal_code.keyword"
            : "filters.postal_code",
          size: AGGREGATE_HITS
        },
        aggs: {
          regions_in: {
            terms: {
              field: "filters.city_level_regions",
              exclude: ZERO_LENGTH_FILTER_VALUE
            }
          }
        }
      },
      country_level_regions: {
        terms: {
          field: "filters.country_level_regions",
          exclude: ZERO_LENGTH_FILTER_VALUE,
          include: prefix
            ? generateRegularExpressionForAutocompleteMatching(prefix)
            : undefined
        },
        aggs: {
          locations_in_region: {
            terms: {
              field: consistentLocationFilter
                ? "address.country.keyword"
                : "filters.country",
              size: LOCATIONS_IN_REGION_AGGREGATION_SIZE
            }
          }
        }
      },
      state_level_regions: {
        terms: {
          field: "filters.state_level_regions",
          exclude: ZERO_LENGTH_FILTER_VALUE,
          include: prefix
            ? generateRegularExpressionForAutocompleteMatching(prefix)
            : undefined
        },
        aggs: {
          locations_in_region: {
            terms: {
              field: consistentLocationFilter
                ? "address.region.keyword"
                : "filters.region",
              size: LOCATIONS_IN_REGION_AGGREGATION_SIZE
            }
          }
        }
      },
      city_level_regions: {
        terms: {
          field: "filters.city_level_regions",
          exclude: ZERO_LENGTH_FILTER_VALUE,
          include: prefix
            ? generateRegularExpressionForAutocompleteMatching(prefix)
            : undefined
        },
        aggs: {
          locations_in_region: {
            terms: {
              field: consistentLocationFilter
                ? "address.token"
                : "filters.city",
              size: LOCATIONS_IN_REGION_AGGREGATION_SIZE
            }
          }
        }
      }
    };
  }

  return {
    country: {
      terms: {
        field: consistentLocationFilter
          ? "address.country.keyword"
          : "filters.country",
        size: AGGREGATE_HITS
      }
    },
    region: {
      terms: {
        field: consistentLocationFilter
          ? "address.region.keyword"
          : "filters.region",
        size: AGGREGATE_HITS
      }
    },
    city: {
      terms: {
        field: consistentLocationFilter ? "address.token" : "filters.city",
        size: AGGREGATE_HITS
      }
    },
    postal_code: {
      terms: {
        field: consistentLocationFilter
          ? "address.postal_code.keyword"
          : "filters.postal_code",
        size: AGGREGATE_HITS
      }
    }
  };
}

function buildFilterForLocationAggregation(
  prefix: string | undefined,
  field: keyof typeof queryableAddressFields
): estypes.QueryDslQueryContainer[] {
  return prefix
    ? [
        {
          multi_match: {
            type: "phrase_prefix",
            query: prefix,
            fields: queryableAddressFields[field]
          }
        }
      ]
    : [];
}

function buildGeoLocationAggregations(
  input: Readonly<InstitutionsSearchInput>,
  queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
): Record<string, AggregationsAggregationContainer> {
  let aggs = {};

  if (input.filters?.precision && input.filters?.geoBoundingBox) {
    aggs = {
      ...aggs,
      geo_hash: {
        geohash_grid: {
          field: "location",
          precision: input.filters!.precision!,
          bounds: input.filters!.geoBoundingBox!
        }
      }
    };
  }

  if (input.filters?.geoDistance) {
    aggs = {
      ...aggs,
      geoDistance: {
        geo_distance: {
          field: "location",
          origin: input.filters!.geoDistance.geoPosition,
          unit: input.filters!.geoDistance.distanceUnit,
          ranges: [
            {
              from: 0,
              to: input.filters!.geoDistance.distance
            }
          ]
        }
      }
    };
  }

  const claimsChoropleth = input.filters?.geoStatsRegionLevel?.claims;
  if (claimsChoropleth) {
    const level = `claims_${claimsChoropleth.zoomLevel}`;
    aggs = {
      ...aggs,
      [level]: {
        terms: {
          field: `filters.${claimsChoropleth.zoomLevel}`,
          size:
            claimsChoropleth.zoomLevel === GeoChoroplethLevelTypes.Country
              ? 200
              : 3500
        },
        aggs: {
          diagnoses: buildGeoClaimsAggregation({
            path: "diagnoses",
            fields: ["description_eng", "code_eng"],
            searchQuery: input.query
              ? constructDiagnosesQueryString(
                  input.query,
                  queryUnderstandingServiceResponse
                )
              : undefined,
            analyzer: CLAIMS_DESCRIPTION_ANALYZER
          }),
          procedures: buildGeoClaimsAggregation({
            path: "procedures",
            fields: ["description_eng", "code_eng"],
            searchQuery: getSynonymizedQuery(
              input.query,
              queryUnderstandingServiceResponse
            ),
            analyzer: CLAIMS_DESCRIPTION_ANALYZER
          })
        }
      }
    };
  }

  return aggs;
}

function buildGeoClusteringAggregations(
  zoom: number
): Record<string, AggregationsAggregationContainer> {
  return {
    geo_clustering: {
      // @ts-ignore-line
      geo_point_clustering: {
        field: "location",
        zoom
      }
    }
  };
}

function buildGeoClaimsAggregation({
  path,
  fields,
  searchQuery,
  analyzer
}: QueryBuilderArguments): AggregationsAggregationContainer {
  return {
    nested: { path },
    aggs: {
      filteredClaims: {
        filter: !searchQuery
          ? { match_all: {} }
          : {
              simple_query_string: toSimpleQueryString({
                path,
                fields,
                searchQuery,
                analyzer
              })
            },
        aggs: {
          counts: {
            sum: {
              field: `${path}.count`
            }
          }
        }
      }
    }
  };
}

function toSimpleQueryString({
  path,
  name,
  fields,
  searchQuery,
  analyzer
}: QueryBuilderArguments): estypes.QueryDslSimpleQueryStringQuery {
  const fieldsWithPath = fields.map((field) => {
    return `${path}.${field}`;
  });
  return {
    _name: name ?? path,
    query: searchQuery!,
    fields: fieldsWithPath,
    default_operator: "and",
    analyzer
  };
}

function constructDiagnosesQueryString(
  parsedQueryTree: string,
  queryUnderstandingServiceResponse:
    | QueryUnderstandingServiceResponse
    | undefined
): string {
  if (queryUnderstandingServiceResponse?.getDiagnosisCodesList().length) {
    return queryUnderstandingServiceResponse!
      .getDiagnosisCodesList()
      .map((diagnosisCode) => `(${diagnosisCode}*)`)
      .join(OR);
  } else if (queryUnderstandingServiceResponse?.getAugmentedQuery()) {
    return convertQueryToSimpleQueryStringSyntax(
      queryUnderstandingServiceResponse.getAugmentedQuery()
    );
  }
  return convertQueryToSimpleQueryStringSyntax(parsedQueryTree);
}

/**
 * Converts the user passed query to a query with synonyms that can we used with simple query string clause of elasticsearch
 * @param query query string passed by the user
 * @param queryUnderstandingServiceResponse query understanding response that may contain synonyms
 * @returns query in elastic search simple query string format or undefined if the query passed itself is undefined
 */
function getSynonymizedQuery(
  query?: string,
  queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
): string | undefined {
  let searchQuery = query;
  // If the query was augmented with synonyms by the query understanding service then use the augmented query
  if (
    queryUnderstandingServiceResponse &&
    queryUnderstandingServiceResponse.getAugmentedQuery()
  ) {
    searchQuery = convertQueryToSimpleQueryStringSyntax(
      queryUnderstandingServiceResponse.getAugmentedQuery()
    );
  }
  return searchQuery;
}

/**
 * Converts a query with advanced operator to elastic search simple query string format. E.g.
 * query: "( lyme disease ) OR ( a6923 ) OR ( ( lyme ) AND ( disease OR disorder) )" gets converted to
 * simple query string: "( lyme disease ) | ( a6923 ) | ( ( lyme ) + ( disease | disorder) )"
 * @param query augmented query from query understanding service
 * @returns query in elastic search simple query string format
 */
export function convertQueryToSimpleQueryStringSyntax(query: string): string {
  const orRegex = /\sOR\s/g;
  const andRegex = /\sAND\s/g;
  const ALL_UNICODE_DOUBLE_QUOTES = /[“”]/g;
  const ASCII_DOUBLE_QUOTES = '"';

  const simpleQueryString = query
    .replace(orRegex, " | ")
    .replace(andRegex, " + ")
    .replace(ALL_UNICODE_DOUBLE_QUOTES, ASCII_DOUBLE_QUOTES);
  return simpleQueryString;
}

export function buildAgeSortQueryForNameSearchOrNoQueryRequest(
  ageRangesSelected: string[],
  ageSortWeight: number,
  filters: QueryDslQueryContainer[]
): estypes.QueryDslQueryContainer {
  return {
    function_score: {
      boost_mode: "replace",
      score_mode: "sum",
      query: {
        bool: {
          must: [buildAgeSortQuery(ageRangesSelected, ageSortWeight)],
          filter: filters
        }
      },
      functions: []
    }
  };
}

export function internalCountFieldForNestedClaimsQuery(
  claimsFilters: ClaimFiltersInput,
  path: ClaimType,
  featureFlags?: InstitutionSearchFeatureFlags
) {
  let enableUniquePatientCountForClaims = false;
  let disableUniquePatientCountForOnlyProcedures = false;
  let enableClaimsFilteringMatchedCountsUpdate = false;
  const uniquePatientToggleFromInput: boolean =
    claimsFilters.showUniquePatients ?? false;

  if (path === "prescriptions") {
    return claimsFilters?.timeFrame
      ? `prescriptions.num_prescriptions_${claimsFilters.timeFrame}_year`
      : "prescriptions.num_prescriptions";
  }

  if (featureFlags) {
    ({
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures,
      enableClaimsFilteringMatchedCountsUpdate
    } = featureFlags);
  }
  const isProcedureWithUniqueCount =
    (path === "procedures" || path === "ccsr_px") &&
    !disableUniquePatientCountForOnlyProcedures;
  const isDiagnosis = path === "diagnoses" || path === "ccsr";

  const shouldUseUniquePatientCount =
    (isProcedureWithUniqueCount || isDiagnosis) &&
    enableUniquePatientCountForClaims;
  const claimsCountField =
    shouldUseUniquePatientCount && uniquePatientToggleFromInput
      ? "internalUniqueCount"
      : "internalCount";
  const claimsCountFieldWithoutTimeFrame =
    shouldUseUniquePatientCount && uniquePatientToggleFromInput
      ? "internalUniqueCount"
      : "count";
  const innerHitsCount =
    enableClaimsFilteringMatchedCountsUpdate && claimsFilters?.timeFrame
      ? `${claimsCountField}_${claimsFilters.timeFrame}_year`
      : claimsCountFieldWithoutTimeFrame;

  return `${path}.${innerHitsCount}`;
}

function toNestedFunctionScoreQueryForClaims(
  path: ClaimType,
  filters: QueryDslQueryContainer[],
  internalCountField: string,
  skipInnerHits: boolean,
  featureFlags: InstitutionSearchFeatureFlags,
  minCount: number | null,
  maxCount?: number | null,
  sortBy?: InstitutionSortOptions,
  raceFilterValues?: string[]
): QueryDslQueryContainer {
  let scoring_function: QueryDslFunctionScoreContainer;
  if (
    sortBy === InstitutionSortOptions.PATIENT_COUNT &&
    path === "procedures"
  ) {
    // if patient count sort is applied then we don't want to score via procedure or global diagnoses only by patient level data
    scoring_function = {
      weight: 0
    };
  } else {
    scoring_function = {
      field_value_factor: {
        field: internalCountField,
        missing: 0
      }
    };
  }

  const nestedQuery: QueryDslNestedQuery = {
    path,
    query: {
      function_score: {
        score_mode: "sum",
        boost_mode: "replace",
        query: {
          bool: {
            must: filters
          }
        },

        functions: [scoring_function]
      }
    },
    ...(!skipInnerHits
      ? {
          inner_hits: {
            _source: false,
            docvalue_fields: [internalCountField],
            size: 1000
          }
        }
      : {}),
    score_mode: "sum"
  };

  let saturationParam;
  if (path === "diagnoses" || path === "ccsr") {
    saturationParam = DIAGNOSES_SATURATION;
  } else if (path === "prescriptions" || path === "ccsr_px") {
    saturationParam = PRESCRIPTIONS_SATURATION;
  } else {
    saturationParam = PROCEDURES_SATURATION;
  }

  const isDiversitySort = sortBy === InstitutionSortOptions.DIVERSITY;
  const baseQuery: QueryDslQueryContainer = {
    function_score: {
      query: {
        nested: nestedQuery
      },
      boost_mode: isDiversitySort ? "replace" : "multiply",
      functions: isDiversitySort
        ? [h1DiversityScoring(saturationParam, raceFilterValues)]
        : []
    }
  };

  if (_.isNil(minCount) && _.isNil(maxCount)) {
    return baseQuery;
  } else {
    const baseQueryWithMinMaxCount: QueryDslQueryContainer = {
      ...baseQuery,
      function_score: {
        ...baseQuery.function_score,
        score_mode: "multiply",
        min_score: isDiversitySort ? MIN_DIVERSITY_SCORE : 1,
        functions: [
          {
            script_score: buildMinMaxFilterScriptForAsset(minCount, maxCount)
          }
        ]
      }
    };
    if (isDiversitySort) {
      baseQueryWithMinMaxCount.function_score!.functions!.push(
        h1DiversityScoring(
          saturationParam,
          raceFilterValues,
          MIN_DIVERSITY_SCORE
        )
      );
    }
    return baseQueryWithMinMaxCount;
  }
}

export function buildRequestForExcludedClaims(
  excludedClaimsFilters: ExclusionClaimFiltersInput,
  request: estypes.SearchRequest,
  featureFlags: InstitutionSearchFeatureFlags,
  shouldExcludeHcps?: boolean
): estypes.SearchRequest {
  const diagnosisClaimsQuery = buildElasticsearchQueryForClaimFilters(
    excludedClaimsFilters,
    "diagnoses",
    false,
    featureFlags,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    shouldExcludeHcps,
    true
  );
  const proceduresClaimsQuery = buildElasticsearchQueryForClaimFilters(
    excludedClaimsFilters,
    "procedures",
    false,
    featureFlags,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    true,
    true
  );
  const prescriptionsClaimsQuery = buildElasticsearchQueryForClaimFilters(
    excludedClaimsFilters,
    "prescriptions",
    false,
    featureFlags,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    true,
    true
  );
  const ccsrClaimsQuery = buildElasticsearchQueryForClaimFilters(
    excludedClaimsFilters,
    "ccsr",
    false,
    featureFlags,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    shouldExcludeHcps,
    true
  );

  const ccsrPxClaimsQuery = buildElasticsearchQueryForClaimFilters(
    excludedClaimsFilters,
    "ccsr_px",
    false,
    featureFlags,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    shouldExcludeHcps,
    true
  );

  const nestedClaimQueries = [
    diagnosisClaimsQuery,
    proceduresClaimsQuery,
    prescriptionsClaimsQuery,
    ccsrClaimsQuery,
    ccsrPxClaimsQuery
  ].filter(
    (query) =>
      !_.isEmpty(query?.nested?.query?.bool?.filter) ||
      !_.isEmpty(query?.function_score?.query?.nested?.query?.bool?.filter)
  );

  if (nestedClaimQueries.length === 0) return request;

  return buildFiltersForClaims(nestedClaimQueries, request, "must_not");
}

export function toNestedClaimsConstantScoreQuery(
  args: QueryBuilderArguments,
  featureFlags: InstitutionSearchFeatureFlags
): QueryDslQueryContainer {
  const claimsRegionFilter =
    buildClaimsRegionFilterForInstitutionsSearch(featureFlags);
  const nestedConstantScoreQuery = toNestedConstantScoreQuery(args);

  if (claimsRegionFilter) {
    return {
      bool: {
        must_not: claimsRegionFilter,
        must: nestedConstantScoreQuery
      }
    };
  }

  return nestedConstantScoreQuery;
}

function toNestedConstantScoreQuery({
  path,
  name,
  fields,
  searchQuery,
  innerHits,
  analyzer
}: QueryBuilderArguments): QueryDslQueryContainer {
  return {
    nested: {
      path: path,
      query: {
        constant_score: {
          filter: {
            simple_query_string: toSimpleQueryString({
              path,
              name,
              fields,
              searchQuery,
              analyzer
            })
          },
          boost: 0
        }
      },
      inner_hits: innerHits
    }
  };
}

export function toNestedMatchAllClaimsQuery(
  featureFlags: InstitutionSearchFeatureFlags,
  sortBy?: InstitutionSortOptions,
  showUniquePatients?: boolean | null
): QueryDslQueryContainer[] {
  const numberOfClaims = 5;
  const shoulds: QueryDslQueryContainer[] = [];
  const claimsRegionFilter =
    buildClaimsRegionFilterForInstitutionsSearch(featureFlags);

  let diagnosesPath: nestedPath = "diagnoses";
  let diagnosesCountField =
    showUniquePatients && featureFlags.enableUniquePatientCountForClaims
      ? "diagnoses.internalUniqueCount"
      : "diagnoses.count";

  let diagnosesFields = ["description_eng", "code_eng"];

  if (isEuropeanDiversitySort(sortBy)) {
    const europeanDiagnosesFields = getEuropeanDiagnosesFields(sortBy);
    if (europeanDiagnosesFields) {
      diagnosesPath = europeanDiagnosesFields.diagnosesPath;
      diagnosesCountField = europeanDiagnosesFields.diagnosesCountField;
      diagnosesFields = ["code", "description"];
    }
  }

  const diagnosesDetailsQueryArguments: QueryBuilderArguments = {
    path: diagnosesPath,
    fields: diagnosesFields,
    searchQuery: undefined,
    innerHits: {
      name: "diagnoses_collection",
      _source: true,
      docvalue_fields: [diagnosesCountField],
      sort: [
        {
          [diagnosesCountField]: {
            order: "desc"
          }
        }
      ],
      size: numberOfClaims
    }
  };

  const procedureDetailsQueryArguments: QueryBuilderArguments = {
    path: "procedures",
    fields: ["description_eng", "code_eng"],
    searchQuery: undefined,
    innerHits: {
      name: "procedures_collection",
      _source: true,
      docvalue_fields: ["procedures.count"],
      sort: [
        {
          "procedures.count": {
            order: "desc"
          }
        }
      ],
      size: numberOfClaims
    }
  };

  shoulds.push(
    toNestedMatchAllQuery(diagnosesDetailsQueryArguments),
    toNestedMatchAllQuery(procedureDetailsQueryArguments)
  );

  if (claimsRegionFilter) {
    return shoulds.map((query) => {
      return {
        bool: {
          must_not: claimsRegionFilter,
          must: query
        }
      };
    });
  }

  return shoulds;
}

function toNestedMatchAllQuery({
  path,
  innerHits
}: QueryBuilderArguments): QueryDslQueryContainer {
  return {
    nested: {
      path,
      query: MATCH_ALL,
      inner_hits: innerHits
    }
  };
}

function shouldFilterOutIrrelevantOrgTypes(
  app?: Apps,
  showAllTypesOfInstitutions?: boolean
) {
  return (
    showAllTypesOfInstitutions === false ||
    (_.isNil(showAllTypesOfInstitutions) && app === Apps.TRIAL_LANDSCAPE)
  );
}

/**
 * Returns a regular expression to match the start of a word in a phrase case-insensitively.
 * Handles the case if the provider prefix contains characters that are reserved for compiling elasticsearch regular expression.
 * Examples: "ma" -> "(.* )?[mM][aA].*" , "san-fr" -> "(.* )?[sS][aA][nN]-[fF][rR]", "Inc." -> "(.* )?[iI][nN][cC]\\."
 * @param prefix prefix to match a word
 * @returns regular expression
 */
export function generateRegularExpressionForAutocompleteMatching(
  prefix: string
): string {
  // Input validation for this function. Although its ensured that this function is not called if the user provided prefix is empty.
  if (prefix.length == 0) {
    return ".*";
  }

  const prefixUpperCase: string = prefix.toUpperCase();
  const prefixLowerCase: string = prefix.toLowerCase();
  const regexpReservedChars = '.?+*|{}[]()"\\';

  let regularExpressionToMatchStartOfWord = "(.* )?";
  for (let i = 0; i < prefix.length; i++) {
    if (regexpReservedChars.indexOf(prefix[i]) > -1) {
      regularExpressionToMatchStartOfWord += "\\" + prefix[i];
    } else if (prefixUpperCase[i] != prefixLowerCase[i]) {
      regularExpressionToMatchStartOfWord +=
        "[" + prefixLowerCase[i] + prefixUpperCase[i] + "]";
    } else {
      regularExpressionToMatchStartOfWord += prefix[i];
    }
  }
  regularExpressionToMatchStartOfWord += ".*";
  return regularExpressionToMatchStartOfWord;
}

function shouldExcludeHcpBasedOnInput(
  input: InstitutionsSearchInput,
  featureFlags: InstitutionSearchFeatureFlags
) {
  return (
    !featureFlags.enableCcsrExclusionForMatchedCounts ||
    !input.filters?.claimsFilters?.showUniquePatients
  );
}

function getSaturationScriptFunction(
  saturationParam: number
): QueryDslFunctionScoreContainer {
  return {
    script_score: {
      script: {
        source: `saturation(_score, params['saturation_param'])`,
        params: {
          saturation_param: saturationParam
        }
      }
    }
  };
}

function franceMapZoomLevelToHeatMapZoomLevel(
  franceMapZoomLevel?: FranceGeoChoroplethLevelTypes | null
) {
  switch (franceMapZoomLevel) {
    case FranceGeoChoroplethLevelTypes.Region:
      return HeatMapZoomLevel.Level1Geography;
    case FranceGeoChoroplethLevelTypes.Department:
      return HeatMapZoomLevel.Level2Geography;
    case FranceGeoChoroplethLevelTypes.Commune:
      return HeatMapZoomLevel.Level3Geography;
    default:
      return null;
  }
}

function isEuropeanDiversitySort(sortBy?: InstitutionSortOptions) {
  return (
    sortBy === InstitutionSortOptions.UK_DIVERSITY ||
    sortBy === InstitutionSortOptions.FRANCE_DIVERSITY ||
    sortBy === InstitutionSortOptions.SPAIN_DIVERSITY
  );
}

function getEuropeanDiversityRankingQuery(
  sortBy?: InstitutionSortOptions,
  raceFilterValues?: string[] | null,
  searchQueryForDiagnoses?: string,
  claimsCode?: ClaimsCode
) {
  let rankingQueryFunction;
  let patientClaimsField;
  if (sortBy === InstitutionSortOptions.UK_DIVERSITY) {
    rankingQueryFunction = getUkDiversityRankingQuery;
    patientClaimsField = "patientClaimsUk.count";
  } else if (sortBy === InstitutionSortOptions.FRANCE_DIVERSITY) {
    rankingQueryFunction = getFranceDiversityRankingQuery;
    patientClaimsField = "patientClaimsFrance.count";
  } else if (sortBy === InstitutionSortOptions.SPAIN_DIVERSITY) {
    rankingQueryFunction = getSpainDiversityRankingQuery;
    patientClaimsField = "patientClaimsSpain.count";
  } else {
    return null;
  }

  const innerHits =
    searchQueryForDiagnoses || claimsCode?.length
      ? {
          _source: false,
          docvalue_fields: [patientClaimsField],
          size: 1000
        }
      : undefined;

  return rankingQueryFunction(
    raceFilterValues,
    searchQueryForDiagnoses,
    innerHits,
    CLAIMS_DESCRIPTION_ANALYZER,
    claimsCode
  );
}

function getEuropeanDiagnosesFields(sortBy?: InstitutionSortOptions) {
  let diagnosesPath: nestedPath;
  let diagnosesCountField;
  if (sortBy === InstitutionSortOptions.UK_DIVERSITY) {
    diagnosesPath = "patientClaimsUk";
    diagnosesCountField = "patientClaimsUk.count";
  } else if (sortBy === InstitutionSortOptions.FRANCE_DIVERSITY) {
    diagnosesPath = "patientClaimsFrance";
    diagnosesCountField = "patientClaimsFrance.count";
  } else if (sortBy === InstitutionSortOptions.SPAIN_DIVERSITY) {
    diagnosesPath = "patientClaimsSpain";
    diagnosesCountField = "patientClaimsSpain.count";
  } else {
    return null;
  }

  return {
    diagnosesPath,
    diagnosesCountField
  };
}

function getEuropeanDiversityAggregations(
  input: InstitutionsSearchInput,
  aggs: Record<string, AggregationsAggregationContainer>,
  searchQuery?: string | undefined,
  analyzer?: string,
  claimsCode?: ClaimsCode
) {
  const franceMapZoomLevel =
    input.filters?.geoStatsRegionLevel?.franceClaims?.zoomLevel;
  const heatMapZoomLevel =
    franceMapZoomLevelToHeatMapZoomLevel(franceMapZoomLevel) ??
    input.filters?.heatMapZoomLevel ??
    HeatMapZoomLevel.Level1Geography;

  if (input.sortBy === InstitutionSortOptions.UK_DIVERSITY) {
    return {
      ...aggs,
      ...buildUkDiversityHeatmapAggregation(searchQuery, analyzer, claimsCode)
    };
  } else if (input.sortBy === InstitutionSortOptions.FRANCE_DIVERSITY) {
    return {
      ...aggs,
      ...buildFranceDiversityHeatmapAggregation(
        heatMapZoomLevel,
        searchQuery,
        analyzer,
        claimsCode
      ),
      ...buildFranceDiversityDashboardAggregation(input.filters)
    };
  } else if (input.sortBy === InstitutionSortOptions.SPAIN_DIVERSITY) {
    return {
      ...aggs,
      ...buildSpainDiversityHeatmapAggregation(
        heatMapZoomLevel,
        searchQuery,
        analyzer,
        claimsCode
      ),
      ...buildSpainDiversityDashboardAggregation(input.filters)
    };
  }

  return aggs;
}
