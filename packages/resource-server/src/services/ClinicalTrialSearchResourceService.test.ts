import { createMockInstance } from "../util/TestUtils";
import {
  ClinicalTrialSearchResourceService,
  TrialSearchFlagVariation,
  expandFilterValueToRegex
} from "./ClinicalTrialSearchResourceService";
import { ConfigService } from "./ConfigService";
import { ElasticTrialSearchService } from "./ElasticTrialSearchService";
import { ElasticTrialSearchServiceV2 } from "./ElasticTrialSearchServiceV2";
import { ElasticTrialSearchServiceV3 } from "./ElasticTrialSearchServiceV3";
import { ElasticTrialSearchServiceV4 } from "./ElasticTrialSearchServiceV4";
import {
  ClinicalTrialFilterAutocompleteFilterField,
  ClinicalTrialFilterAutocompleteInput,
  ClinicalTrialSource,
  IndicationsType
} from "@h1nyc/search-sdk";

import fixture1 from "./__fixtures__/GoldToProduct/TrialsV2.json";
import fixture2 from "./__fixtures__/GoldToProduct/TrialsV2_withPersonAndFacility.json";
import fixture3 from "./__fixtures__/GoldToProduct/TrialsV2_withFacilityInvestigator.json";
import fixtureV3_deprecated from "./__fixtures__/GoldToProduct/TrialsV3_deprecated.json";
import fixtureV3_replacement_survivors from "./__fixtures__/GoldToProduct/TrialsV3_replacement_survivors.json";
import fixtureV4 from "./__fixtures__/GoldToProduct/TrialsV4.json";
import fixtureV4EnrollmentEudra from "./__fixtures__/GoldToProduct/TrialsV4_enrollment_member_states.json";
import fixtureV1ForDiff from "./__fixtures__/GoldToProduct/TrialsV1_forDiff.json";
import fixtureV2ForDiff from "./__fixtures__/GoldToProduct/TrialsV2_forDiff.json";
import fixtureWithIdsOnly from "./__fixtures__/GoldToProduct/TrialsV2_idsOnly.json";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { ClinicalTrialInput } from "@h1nyc/search-sdk";
import { EntityTagResourceClient } from "@h1nyc/account-sdk";
import { EntityType } from "@h1nyc/account-user-entities";
import {
  SearchResponse,
  SearchHit
} from "@elastic/elasticsearch/lib/api/types";
import { faker } from "@faker-js/faker";
import { ClinicalTrialDocumentWithCTMS } from "@h1nyc/search-sdk";
import { QueryParserService } from "./QueryParserService";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";

const fixtures = [fixture1, fixture2, fixture3];

function generateInput(
  overrides: Partial<ClinicalTrialInput> = {}
): ClinicalTrialInput {
  const input = {
    idsOnly: false,
    filters: [],
    limit: faker.datatype.number(),
    offset: faker.datatype.number(),
    sort: {
      sortBy: "NctId",
      direction: faker.datatype.string()
    },
    userId: faker.datatype.uuid(),
    projectId: faker.datatype.uuid()
  };

  return { ...input, ...overrides };
}

function generateAutocompleteInput(
  overrides: Partial<ClinicalTrialFilterAutocompleteInput> = {},
  filterField: ClinicalTrialFilterAutocompleteFilterField,
  filterValue: string
): ClinicalTrialFilterAutocompleteInput {
  const input = {
    idsOnly: false,
    filters: [],
    limit: faker.datatype.number(),
    offset: faker.datatype.number(),
    sort: {
      sortBy: "NctId",
      direction: faker.datatype.string()
    },
    userId: faker.datatype.uuid(),
    projectId: faker.datatype.uuid(),
    filterField,
    filterValue
  };

  return { ...input, ...overrides };
}

function generateAutocompleteAggregationResponse(isNested: boolean) {
  if (isNested) {
    return {
      nested: {
        filtered_matching: {
          matching: {
            buckets: [
              {
                key: faker.datatype.string(),
                count: faker.datatype.number()
              }
            ]
          }
        }
      }
    };
  }
  return {
    filtered_matching: {
      matching: {
        buckets: [
          {
            key: faker.datatype.string(),
            count: faker.datatype.number()
          }
        ]
      }
    }
  };
}

function generateMockElasticsearchResponse<T>(
  hits: Array<SearchHit<T>> = [],
  aggregations?: any
): SearchResponse<T> {
  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: {
        value: faker.datatype.number(),
        relation: "eq"
      },
      max_score: faker.datatype.number(),
      hits
    },
    aggregations
  };
}

function generateMockHit(
  overrides: Partial<ClinicalTrialDocumentWithCTMS> = {}
): SearchHit<ClinicalTrialDocumentWithCTMS> {
  const hit = {
    h1_clinical_trial_id: faker.datatype.uuid(),
    is_deprecated: faker.datatype.boolean(),
    survivor_h1_clinical_trial_id: faker.datatype.uuid(),
    version: faker.datatype.string(),
    effective_date: faker.date.past().toString(),
    facility: [],
    identifiers: [],
    person: [],
    study: {},
    ctms: []
  };

  return {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: { ...hit, ...overrides }
  } as SearchHit<ClinicalTrialDocumentWithCTMS>;
}

beforeAll(() => {
  jest.useFakeTimers().setSystemTime(new Date("2022-04-20").getTime());
});

describe("Timezones", () => {
  it("should always be UTC", () => {
    expect(new Date().getTimezoneOffset()).toBe(0);
  });
});

describe("searchTrials()", () => {
  for (let i = 0; i < fixtures.length; i += 1) {
    it(`returns expected response for fixture index ${i}`, async () => {
      const configService = createMockInstance(ConfigService);
      const elasticTrialSearchService = createMockInstance(
        ElasticTrialSearchService
      );
      const elasticTrialSearchServiceV2 = createMockInstance(
        ElasticTrialSearchServiceV2
      );
      elasticTrialSearchServiceV2.getSignedElasticRequest.mockResolvedValue(
        fixtures[i]
      );

      const elasticTrialSearchServiceV3 = createMockInstance(
        ElasticTrialSearchServiceV3
      );
      const elasticTrialSearchServiceV4 = createMockInstance(
        ElasticTrialSearchServiceV4
      );

      const entityTagResourceClient = createMockInstance(
        EntityTagResourceClient
      );
      const featureFlagsService = createMockInstance(
        FeatureFlagsService as any
      ) as jest.Mocked<FeatureFlagsService>;
      featureFlagsService.getFlag.mockResolvedValue(
        TrialSearchFlagVariation.V2
      );

      const expectedResult = expectedResults[i];

      const queryParserService = createMockInstance(QueryParserService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );
      const clinicalTrialResourceService =
        new ClinicalTrialSearchResourceService(
          configService,
          elasticTrialSearchService,
          elasticTrialSearchServiceV2,
          elasticTrialSearchServiceV3,
          elasticTrialSearchServiceV4,
          featureFlagsService,
          entityTagResourceClient,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService,
          queryUnderstandingServiceClient
        );

      const resp = await clinicalTrialResourceService.searchTrials({
        limit: 1,
        offset: 0,
        sort: { sortBy: "NctId", direction: "ascending" }
      });

      expect(
        elasticTrialSearchService.getSignedElasticRequest
      ).not.toHaveBeenCalled();
      expect(
        elasticTrialSearchServiceV2.getSignedElasticRequest
      ).toHaveBeenCalled();
      resp.results[0].id = 0;
      expect(resp).toEqual(expectedResult);
    });
  }
});

describe("searchTrials() v3", () => {
  it("returns expected response with deprecated trial", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    elasticTrialSearchServiceV3.getSignedElasticRequest.mockResolvedValueOnce(
      fixtureV3_deprecated
    );

    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );

    elasticTrialSearchServiceV4.getSignedElasticRequest.mockResolvedValueOnce(
      fixtureV3_replacement_survivors
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(TrialSearchFlagVariation.V3);

    const expectedResult = {
      from: 0,
      pageSize: 1,
      resultIds: undefined,
      results: [
        {
          acronym: undefined,
          allocation: ["Randomized"],
          avgPatientsPerSites: Infinity,
          briefTitle:
            "Adjunctive Pimavanserin in Subjects With Major Depressive Disorder and Inadequate Response to Antidepressant Treatment",
          conditions: ["Adjunctive Treatment of Major Depressive Disorder"],
          contactNames: ["Cheryl Knipe", "Becky Howell", "Jim Harlick"],
          eligibilityMinAge: ["18 Years"],
          enrollment: 298,
          enrollmentType: "Actual",
          estimatedEndDate: 1588204800000,
          facilities: undefined,
          facilityCity: [],
          facilityCountry: [],
          facilityName: [],
          facilityState: [],
          h1Id: "CT-29-ff774782-70b9-4811-bf1c-7ef5e4506020",
          id: 0,
          interventionModel: ["Parallel Assignment"],
          interventionOtherNames: [],
          interventionTypes: ["Drug"],
          interventions: ["Pimavanserin", "Placebo"],
          investigators: [],
          lastUpdatedAt: 1637107200000,
          masking: ["Quadruple"],
          nctId: "NCT03968159",
          patientsPerMonths: 22.92,
          patientsPerSitesPerMonth: Infinity,
          people: [
            {
              external_uuid:
                "7260a4fd1e5d49ac0d2e277cf73e36b6aae1589441564322c4b593bf5bbfd9d0",
              name: "Sr. Dir. Medical Information and Medical Communications",
              roleHistory: [
                {
                  affiliation: "Acadia Pharmaceuticals Inc.",
                  email: "<EMAIL>",
                  end_date: "2019-08-13",
                  hash_or_id:
                    "f492430ca17d538a8fd10a789c5767bcb532ccc4cd8ac3256e0143b9b25c2d69",
                  phone: "858-261",
                  role: "Result Contact",
                  start_date: "2019-07-31",
                  study_person_role_hash:
                    "f492430ca17d538a8fd10a789c5767bcb532ccc4cd8ac3256e0143b9b25c2d69"
                },
                {
                  affiliation: "Acadia Pharmaceuticals Inc.",
                  email: "<EMAIL>",
                  hash_or_id:
                    "f492430ca17d538a8fd10a789c5767bcb532ccc4cd8ac3256e0143b9b25c2d69",
                  phone: "858-261",
                  role: "Result Contact",
                  start_date: "2020-01-23",
                  study_person_role_hash:
                    "f492430ca17d538a8fd10a789c5767bcb532ccc4cd8ac3256e0143b9b25c2d69"
                },
                {
                  affiliation: "Acadia Pharmaceuticals Inc.",
                  email: "<EMAIL>",
                  hash_or_id:
                    "f492430ca17d538a8fd10a789c5767bcb532ccc4cd8ac3256e0143b9b25c2d69",
                  phone: "858-261",
                  role: "Result Contact",
                  start_date: "2019-05-30",
                  study_person_role_hash:
                    "f492430ca17d538a8fd10a789c5767bcb532ccc4cd8ac3256e0143b9b25c2d69"
                },
                {
                  affiliation: "Acadia Pharmaceuticals Inc.",
                  email: "<EMAIL>",
                  end_date: "2019-12-26",
                  hash_or_id:
                    "f492430ca17d538a8fd10a789c5767bcb532ccc4cd8ac3256e0143b9b25c2d69",
                  phone: "858-261",
                  role: "Result Contact",
                  start_date: "2019-08-30",
                  study_person_role_hash:
                    "f492430ca17d538a8fd10a789c5767bcb532ccc4cd8ac3256e0143b9b25c2d69"
                }
              ],
              study_person_hash:
                "a1260f2181571fc0ea18ae219b18881e075a03d9e6bb51913b5fb2fe0fe1e314"
            },
            {
              external_uuid:
                "c2b7a796df1aac499168347c8028d97f95a45febf50065d88b001909aa64df79",
              name: "Cheryl Knipe",
              roleHistory: [
                {
                  email: "<EMAIL>",
                  hash_or_id:
                    "c6d00ba43a0db475b11f37cf325a19def6105a4d063967ff716284a30f0a1b5c",
                  phone: "************",
                  role: "Central Contact",
                  start_date: "2019-05-30",
                  study_person_role_hash:
                    "c6d00ba43a0db475b11f37cf325a19def6105a4d063967ff716284a30f0a1b5c"
                }
              ],
              study_person_hash:
                "3dc07a636da49de1c256f7457dac339dd35fcbf021cb01832fb1d5e4f11e6d98"
            },
            {
              external_uuid:
                "94900c0b1d278ba4add2844c5b5e12c2d609bd19c905b38eaab1373d00ccf76d",
              name: "Becky Howell",
              roleHistory: [
                {
                  email: "<EMAIL>",
                  hash_or_id:
                    "88dd79385700a2de43a0897013cf3ad77507a6402c239cf7c615c1172d84f23d",
                  phone: "************",
                  role: "Central Contact",
                  start_date: "2019-05-30",
                  study_person_role_hash:
                    "88dd79385700a2de43a0897013cf3ad77507a6402c239cf7c615c1172d84f23d"
                }
              ],
              study_person_hash:
                "a03c738fd8e9ba2d49658a080a5d891ec26309351bece7a6674a448cc0dee42c"
            },
            {
              external_uuid:
                "b1119ab87041fc43bece95680c99dac67a9219f41afc86115c95cdd3a16fc270",
              name: "Jim Harlick",
              roleHistory: [
                {
                  email: "<EMAIL>",
                  hash_or_id:
                    "18b14f069dc9dd13b226d2f17f965acda9f4b2ccbdd088e0b228c51bd5597f74",
                  phone: "************",
                  role: "Central Contact",
                  start_date: "2019-08-13",
                  study_person_role_hash:
                    "18b14f069dc9dd13b226d2f17f965acda9f4b2ccbdd088e0b228c51bd5597f74"
                }
              ],
              study_person_hash:
                "550f7d66c1b502a2813939c5d56388cca7debb74c54b1326ccdf4766b697cec6"
            }
          ],
          personIds: [],
          primaryCompletionDate: 1588204800000,
          primaryPurpose: ["Treatment"],
          sitesCount: 0,
          sponsors: [],
          startDate: 1556150400000,
          status: "Completed",
          studyCompletionDate: 1590710400000,
          studyPhase: "Phase 3",
          studyType: "Interventional",
          trialDuration: "1 yr 1 mo"
        }
      ],
      total: 1,
      aggregations: {
        avgFacilityCount: 0,
        medianFacilityCount: null,
        avgInvestigatorCount: 0,
        medianInvestigatorCount: null,
        avgPatientCount: null,
        avgPatientsPerSite: 0,
        avgPatientsPerSitePerMonth: 0,
        avgPatientsPerSitePerMonthV2: undefined,
        medianPatientsPerSitePerMonthV2: undefined,
        medianEnrollmentDurationV2: undefined,
        avgTrialsDuration: null,
        facilityCount: 0,
        investigatorCount: 0,
        groupByCountry: [],
        groupByEnrollmentDuration: undefined,
        groupByPatientsPerSitePerEnrollmentDuration: undefined,
        groupByPersonRole: [],
        groupByPhaseAndStatus: [],
        groupByTopCountrySites: undefined,
        groupByTopCountryTrials: [],
        groupByTopCountryAvgEnrollmentRate: [],
        totalEnrollment: 0
      }
    };

    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const resp = await clinicalTrialResourceService.searchTrials({
      limit: 1,
      offset: 0,
      sort: { sortBy: "NctId", direction: "ascending" }
    });

    expect(
      elasticTrialSearchService.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV2.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    resp.results[0].id = 0;
    expect(resp).toEqual(expectedResult);
  });

  it.each(Array.from(fixtures.entries()))(
    `returns v2 expected response for fixture index %p`,
    async (index, fixture) => {
      const configService = createMockInstance(ConfigService);
      const elasticTrialSearchService = createMockInstance(
        ElasticTrialSearchService
      );
      const elasticTrialSearchServiceV2 = createMockInstance(
        ElasticTrialSearchServiceV2
      );
      const elasticTrialSearchServiceV3 = createMockInstance(
        ElasticTrialSearchServiceV3
      );
      elasticTrialSearchServiceV3.getSignedElasticRequest.mockResolvedValue(
        fixture
      );

      const elasticTrialSearchServiceV4 = createMockInstance(
        ElasticTrialSearchServiceV4
      );
      const featureFlagsService = createMockInstance(
        FeatureFlagsService as any
      ) as jest.Mocked<FeatureFlagsService>;
      featureFlagsService.getFlag.mockResolvedValue(
        TrialSearchFlagVariation.V3
      );
      const entityTagResourceClient = createMockInstance(
        EntityTagResourceClient
      );
      const expectedResult = expectedResults[index];

      const queryParserService = createMockInstance(QueryParserService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );
      const clinicalTrialResourceService =
        new ClinicalTrialSearchResourceService(
          configService,
          elasticTrialSearchService,
          elasticTrialSearchServiceV2,
          elasticTrialSearchServiceV3,
          elasticTrialSearchServiceV4,
          featureFlagsService,
          entityTagResourceClient,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService,
          queryUnderstandingServiceClient
        );

      const resp = await clinicalTrialResourceService.searchTrials({
        limit: 1,
        offset: 0,
        sort: { sortBy: "NctId", direction: "ascending" }
      });

      expect(
        elasticTrialSearchService.getSignedElasticRequest
      ).not.toHaveBeenCalled();
      expect(
        elasticTrialSearchServiceV2.getSignedElasticRequest
      ).not.toHaveBeenCalled();
      expect(
        elasticTrialSearchServiceV3.getSignedElasticRequest
      ).toHaveBeenCalled();
      expect(
        elasticTrialSearchServiceV3.getSignedElasticRequest
      ).toHaveBeenCalledTimes(1);
      resp.results[0].id = 0;
      expect(resp).toEqual(expectedResult);
    }
  );
});

describe("searchTrials() v4", () => {
  it("returns expected response with updated fields", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    elasticTrialSearchServiceV4.getSignedElasticRequest.mockResolvedValue(
      fixtureV4
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(TrialSearchFlagVariation.V4);

    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const resp = await clinicalTrialResourceService.searchTrials({
      limit: 1,
      offset: 0,
      sort: { sortBy: "NctId", direction: "ascending" }
    });

    expect(
      elasticTrialSearchService.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV2.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV3.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV4.getSignedElasticRequest
    ).toHaveBeenCalled();
    resp.results[0].id = 0;
    expect(resp).toEqual(expectedResultForFixtureV4);
  });

  it("returns expected response with correct enrollment count for EUDRA", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    elasticTrialSearchServiceV4.getSignedElasticRequest.mockResolvedValue(
      fixtureV4EnrollmentEudra
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(TrialSearchFlagVariation.V4);

    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const resp = await clinicalTrialResourceService.searchTrials({
      limit: 1,
      offset: 0,
      sort: { sortBy: "NctId", direction: "ascending" }
    });

    expect(
      elasticTrialSearchService.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV2.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV3.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV4.getSignedElasticRequest
    ).toHaveBeenCalled();
    resp.results[0].id = 0;
    expect(resp).toEqual(expectedResultForFixtureV4Enrollment);
  });
});

describe("searchTrials() with idsOnly", () => {
  it("returns expected response", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    elasticTrialSearchServiceV2.getSignedElasticRequest.mockResolvedValue(
      fixtureWithIdsOnly
    );

    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(TrialSearchFlagVariation.V2);
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const resp = await clinicalTrialResourceService.searchTrials({
      limit: 1,
      offset: 0,
      sort: { sortBy: "NctId", direction: "ascending" },
      idsOnly: true
    });

    const expectedResultForIdsOnly = {
      from: 0,
      pageSize: 1,
      resultIds: ["NCT03591926", "NCT03591926", "NCT00257140"],
      results: [],
      total: 708572
    };

    expect(
      elasticTrialSearchService.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV2.getSignedElasticRequest
    ).toHaveBeenCalled();
    expect(resp).toEqual(expectedResultForIdsOnly);
  });
});

describe("trialCountForIndications()", () => {
  const configService = createMockInstance(ConfigService);
  const trialsIndex = faker.datatype.string();
  configService.elasticTrialsIndexWithCTMS = trialsIndex;

  const elasticTrialSearchService = createMockInstance(
    ElasticTrialSearchService
  );
  const elasticTrialSearchServiceV2 = createMockInstance(
    ElasticTrialSearchServiceV2
  );
  const elasticTrialSearchServiceV3 = createMockInstance(
    ElasticTrialSearchServiceV3
  );
  const elasticTrialSearchServiceV4 = createMockInstance(
    ElasticTrialSearchServiceV4
  );
  elasticTrialSearchServiceV4.query.mockResolvedValue(
    generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([])
  );

  const featureFlagsService = createMockInstance(
    FeatureFlagsService as any
  ) as jest.Mocked<FeatureFlagsService>;
  featureFlagsService.getFlag.mockResolvedValue(TrialSearchFlagVariation.CTMS);
  const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
  const queryParserService = createMockInstance(QueryParserService);
  const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
    ParsedQueryTreeToElasticsearchQueriesService
  );

  const queryUnderstandingServiceClient = createMockInstance(
    QueryUnderstandingServiceClient
  );
  const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
    configService,
    elasticTrialSearchService,
    elasticTrialSearchServiceV2,
    elasticTrialSearchServiceV3,
    elasticTrialSearchServiceV4,
    featureFlagsService,
    entityTagResourceClient,
    queryParserService,
    parsedQueryTreeToElasticsearchQueriesService,
    queryUnderstandingServiceClient
  );

  jest.spyOn(
    clinicalTrialResourceService,
    "buildCountForIndicationsAggregation"
  );

  const input = generateInput();
  const mockIndications = [faker.datatype.string(), faker.datatype.string()];

  beforeAll(async () => {
    // Fetch data or perform asynchronous setup
    await clinicalTrialResourceService.trialCountForIndications(
      input,
      mockIndications,
      IndicationsType.ALL
    );
  });

  it("queries using elasticTrialsSearchService.query() and the elasticTrialsIndexWithCTMS", () => {
    expect(
      elasticTrialSearchService.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV2.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV3.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV4.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        index: trialsIndex
      })
    );
  });
  it("calls buildCountForIndicationsAggregation to reconstruct the search query", () => {
    expect(
      clinicalTrialResourceService.buildCountForIndicationsAggregation
    ).toHaveBeenCalled();
  });
});

describe("buildCountForIndicationsAggregation()", () => {
  const configService = createMockInstance(ConfigService);
  const trialsIndex = faker.datatype.string();
  configService.elasticTrialsIndexWithCTMS = trialsIndex;

  const elasticTrialSearchService = createMockInstance(
    ElasticTrialSearchService
  );
  const elasticTrialSearchServiceV2 = createMockInstance(
    ElasticTrialSearchServiceV2
  );
  const elasticTrialSearchServiceV3 = createMockInstance(
    ElasticTrialSearchServiceV3
  );
  const elasticTrialSearchServiceV4 = createMockInstance(
    ElasticTrialSearchServiceV4
  );
  elasticTrialSearchServiceV4.query.mockResolvedValue(
    generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([])
  );

  const featureFlagsService = createMockInstance(
    FeatureFlagsService as any
  ) as jest.Mocked<FeatureFlagsService>;
  featureFlagsService.getFlag.mockResolvedValue(TrialSearchFlagVariation.CTMS);
  const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
  const queryParserService = createMockInstance(QueryParserService);
  const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
    ParsedQueryTreeToElasticsearchQueriesService
  );

  const queryUnderstandingServiceClient = createMockInstance(
    QueryUnderstandingServiceClient
  );
  const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
    configService,
    elasticTrialSearchService,
    elasticTrialSearchServiceV2,
    elasticTrialSearchServiceV3,
    elasticTrialSearchServiceV4,
    featureFlagsService,
    entityTagResourceClient,
    queryParserService,
    parsedQueryTreeToElasticsearchQueriesService,
    queryUnderstandingServiceClient
  );

  const mockIndications = [faker.datatype.string(), faker.datatype.string()];

  it("should construct query from scratch if parts of query and aggs do not exist", () => {
    const searchQuery = {};
    const expectedResp = {
      aggs: {
        indication_counts: {
          terms: {
            size: 100,
            field: "indications",
            include: mockIndications
          }
        }
      }
    };
    clinicalTrialResourceService.buildCountForIndicationsAggregation(
      searchQuery,
      mockIndications,
      IndicationsType.ALL
    );

    expect(searchQuery).toEqual(expectedResp);
  });
  it("should rebuild query with filters and aggregations", () => {
    const searchQuery = {
      query: {
        bool: {
          filter: [
            {
              terms: {
                helloWorld: ["hello World"]
              }
            }
          ]
        }
      },
      aggs: {
        test_agg: {
          terms: {
            field: "hello World",
            size: 1
          }
        }
      }
    };
    const expectedResp = {
      query: {
        bool: {
          filter: [
            {
              terms: {
                helloWorld: ["hello World"]
              }
            }
          ]
        }
      },
      aggs: {
        test_agg: {
          terms: {
            field: "hello World",
            size: 1
          }
        },
        indication_counts: {
          terms: {
            size: 100,
            field: "indications",
            include: mockIndications
          }
        }
      }
    };
    clinicalTrialResourceService.buildCountForIndicationsAggregation(
      searchQuery,
      mockIndications,
      IndicationsType.ALL
    );

    expect(searchQuery).toEqual(expectedResp);
  });
});

describe("searchTrials() with CTMS", () => {
  it("queries using elasticTrialsSearchService.query() and the elasticTrialsIndexWithCTMS", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([])
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const input = generateInput();
    await clinicalTrialResourceService.searchTrials(input);

    expect(
      elasticTrialSearchService.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV2.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV3.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV4.getSignedElasticRequest
    ).not.toHaveBeenCalled();
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        index: trialsIndex
      })
    );
  });
});

describe("trialDocumentV2()", () => {
  for (let i = 0; i < fixtures.length; i += 1) {
    it(`returns expected response for fixture index ${i}`, async () => {
      const configService = createMockInstance(ConfigService);
      const elasticTrialSearchService = createMockInstance(
        ElasticTrialSearchService
      );
      elasticTrialSearchService.getSignedElasticRequest.mockResolvedValue([]);

      const elasticTrialSearchServiceV2 = createMockInstance(
        ElasticTrialSearchServiceV2
      );
      elasticTrialSearchServiceV2.getSignedElasticRequest.mockResolvedValue(
        fixtures[i]
      );

      const elasticTrialSearchServiceV3 = createMockInstance(
        ElasticTrialSearchServiceV3
      );
      const elasticTrialSearchServiceV4 = createMockInstance(
        ElasticTrialSearchServiceV4
      );
      const featureFlagsService = createMockInstance(
        FeatureFlagsService as any
      ) as jest.Mocked<FeatureFlagsService>;
      const entityTagResourceClient = createMockInstance(
        EntityTagResourceClient
      );
      const expectedResult = fixtures[i].hits.hits[0]._source;

      const queryParserService = createMockInstance(QueryParserService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );
      const clinicalTrialResourceService =
        new ClinicalTrialSearchResourceService(
          configService,
          elasticTrialSearchService,
          elasticTrialSearchServiceV2,
          elasticTrialSearchServiceV3,
          elasticTrialSearchServiceV4,
          featureFlagsService,
          entityTagResourceClient,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService,
          queryUnderstandingServiceClient
        );

      const resp = await clinicalTrialResourceService.trialDocumentV2("foo");

      expect(
        elasticTrialSearchService.getSignedElasticRequest
      ).not.toHaveBeenCalled();
      expect(
        elasticTrialSearchServiceV2.getSignedElasticRequest
      ).toHaveBeenCalled();
      expect(
        elasticTrialSearchServiceV2.getSignedElasticRequest
      ).toHaveBeenCalledWith({
        query: { match: { "identifiers.external_uuid": "foo" } }
      });
      expect(resp).toEqual(expectedResult);
    });
  }
});

describe("trialDocumentV4()", () => {
  it("returns expected response for fixtureV4", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    elasticTrialSearchServiceV4.getSignedElasticRequest.mockResolvedValue(
      fixtureV4
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const expectedResult = fixtureV4.hits.hits[0]._source;

    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const resp = await clinicalTrialResourceService.trialDocumentV4("foo");

    expect(
      elasticTrialSearchServiceV4.getSignedElasticRequest
    ).toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV4.getSignedElasticRequest
    ).toHaveBeenCalledWith({
      query: { match: { "identifiers.external_uuid": "foo" } }
    });
    expect(resp).toEqual(expectedResult);
  });
});

describe("trialDocumentWithCTMS()", () => {
  it("should call elasticTrialSearchServiceV4.query with the correct should clauses", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;
    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    elasticTrialSearchServiceV4.query.mockResolvedValueOnce(
      generateMockElasticsearchResponse([generateMockHit()])
    );

    const mockId = faker.datatype.uuid();
    await clinicalTrialResourceService.trialDocumentWithCTMS(mockId);

    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledTimes(1);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith({
      index: trialsIndex,
      query: {
        bool: {
          should: [
            {
              term: {
                _id: mockId
              }
            },
            {
              term: {
                h1dn_clinical_trial_id: mockId
              }
            },
            {
              term: {
                ["identifiers.external_uuid.keyword"]: mockId
              }
            }
          ]
        }
      }
    });
  });
});

describe("searchTrials() with Tags", () => {
  it("should have both inclusion and exclusion filter applied with OR tag option", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([])
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);

    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const inclusionTagID1 = faker.datatype.string();
    const inclusionTagID2 = faker.datatype.string();
    const exclusionTagID1 = faker.datatype.string();
    entityTagResourceClient.getByActiveTagIds.mockResolvedValueOnce([
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: inclusionTagID1,
        entityId: "TrailId-1",
        validateTimeStamp: () => null
      },
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: inclusionTagID2,
        entityId: "TrailId-2",
        validateTimeStamp: () => null
      }
    ]);
    entityTagResourceClient.getByActiveTagIds.mockResolvedValueOnce([
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: exclusionTagID1,
        entityId: "TrailId-3",
        validateTimeStamp: () => null
      }
    ]);

    const input = generateInput();
    input!.filters!.push(
      {
        name: "TagsExclusion",
        value: JSON.stringify([exclusionTagID1])
      },
      {
        name: "TagsInclusion",
        value: JSON.stringify([inclusionTagID1, inclusionTagID2])
      },
      {
        name: "TagsIntersection",
        value: JSON.stringify("OR")
      }
    );

    await clinicalTrialResourceService.searchTrials(input);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        query: {
          bool: {
            filter: [
              {
                bool: {
                  should: [
                    {
                      bool: {
                        must_not: [
                          {
                            exists: {
                              field: "project_id"
                            }
                          }
                        ]
                      }
                    },
                    {
                      bool: {
                        must: [
                          {
                            exists: {
                              field: "project_id"
                            }
                          },
                          {
                            term: {
                              project_id: input.projectId
                            }
                          }
                        ]
                      }
                    }
                  ]
                }
              },
              {
                bool: {
                  filter: {
                    bool: {
                      should: [
                        {
                          terms: {
                            h1_clinical_trial_id: ["TrailId-1", "TrailId-2"]
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  },
                  must_not: {
                    bool: {
                      should: [
                        {
                          terms: {
                            h1_clinical_trial_id: ["TrailId-3"]
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  }
                }
              }
            ]
          }
        }
      })
    );
  });
  it("should have both inclusion and exclusion filter applied with AND tag option", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([])
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);

    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const inclusionTagID1 = faker.datatype.string();
    const inclusionTagID2 = faker.datatype.string();
    const exlusionTagID1 = faker.datatype.string();
    entityTagResourceClient.getByActiveTagIds.mockResolvedValueOnce([
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: inclusionTagID1,
        entityId: "TrailId-1",
        validateTimeStamp: () => null
      },
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: inclusionTagID1,
        entityId: "TrailId-2",
        validateTimeStamp: () => null
      },
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: inclusionTagID2,
        entityId: "TrailId-2",
        validateTimeStamp: () => null // only this entityId is present in both the tags
      },
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: inclusionTagID2,
        entityId: "TrailId-3",
        validateTimeStamp: () => null
      }
    ]);

    entityTagResourceClient.getByActiveTagIds.mockResolvedValueOnce([
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: exlusionTagID1,
        entityId: "TrailId-4",
        validateTimeStamp: () => null
      }
    ]);

    const input = generateInput();
    input!.filters!.push(
      {
        name: "TagsExclusion",
        value: JSON.stringify([exlusionTagID1])
      },
      {
        name: "TagsInclusion",
        value: JSON.stringify([inclusionTagID1, inclusionTagID2])
      },
      {
        name: "TagsIntersection",
        value: JSON.stringify("AND")
      }
    );

    await clinicalTrialResourceService.searchTrials(input);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        query: {
          bool: {
            filter: [
              {
                bool: {
                  should: [
                    {
                      bool: {
                        must_not: [
                          {
                            exists: {
                              field: "project_id"
                            }
                          }
                        ]
                      }
                    },
                    {
                      bool: {
                        must: [
                          {
                            exists: {
                              field: "project_id"
                            }
                          },
                          {
                            term: {
                              project_id: input.projectId
                            }
                          }
                        ]
                      }
                    }
                  ]
                }
              },
              {
                bool: {
                  filter: {
                    bool: {
                      should: [
                        {
                          terms: {
                            h1_clinical_trial_id: ["TrailId-2"]
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  },
                  must_not: {
                    bool: {
                      should: [
                        {
                          terms: {
                            h1_clinical_trial_id: ["TrailId-4"]
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  }
                }
              }
            ]
          }
        }
      })
    );
  });

  it("should have both inclusion and exclusion filter applied with AND tag option when there is no intersection", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([])
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);

    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const inclusionTagID1 = faker.datatype.string();
    const inclusionTagID2 = faker.datatype.string();
    const exlusionTagID1 = faker.datatype.string();
    entityTagResourceClient.getByActiveTagIds.mockResolvedValueOnce([
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: inclusionTagID1,
        entityId: "TrailId-1",
        validateTimeStamp: () => null
      },
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: inclusionTagID1,
        entityId: "TrailId-2",
        validateTimeStamp: () => null
      },
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: inclusionTagID2,
        entityId: "TrailId-3",
        validateTimeStamp: () => null
      }
    ]);

    entityTagResourceClient.getByActiveTagIds.mockResolvedValueOnce([
      {
        id: faker.datatype.string(),
        assignedBy: faker.datatype.string(),
        entityType: EntityType.TRIAL,
        tagId: exlusionTagID1,
        entityId: "TrailId-4",
        validateTimeStamp: () => null
      }
    ]);

    const input = generateInput();
    input!.filters!.push(
      {
        name: "TagsExclusion",
        value: JSON.stringify([exlusionTagID1])
      },
      {
        name: "TagsInclusion",
        value: JSON.stringify([inclusionTagID1, inclusionTagID2])
      },
      {
        name: "TagsIntersection",
        value: JSON.stringify("AND")
      }
    );

    await clinicalTrialResourceService.searchTrials(input);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        query: {
          bool: {
            filter: [
              {
                bool: {
                  should: [
                    {
                      bool: {
                        must_not: [
                          {
                            exists: {
                              field: "project_id"
                            }
                          }
                        ]
                      }
                    },
                    {
                      bool: {
                        must: [
                          {
                            exists: {
                              field: "project_id"
                            }
                          },
                          {
                            term: {
                              project_id: input.projectId
                            }
                          }
                        ]
                      }
                    }
                  ]
                }
              },
              {
                bool: {
                  filter: {
                    bool: {
                      should: [
                        {
                          terms: {
                            h1_clinical_trial_id: ["no-match-trial-id"]
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  },
                  must_not: {
                    bool: {
                      should: [
                        {
                          terms: {
                            h1_clinical_trial_id: ["TrailId-4"]
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  }
                }
              }
            ]
          }
        }
      })
    );
  });
});

describe("searchBulkTrials()", () => {
  it("queries using elasticTrialsSearchService.query() and returns bulk entities array", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([
        generateMockHit()
      ])
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const input = generateInput();
    const response = await clinicalTrialResourceService.searchBulkTrials(input);

    expect(response).toBeInstanceOf(Array);

    response.forEach((obj) => {
      expect(obj).toBeInstanceOf(Object);
      expect(obj).toHaveProperty("entityId");
      expect(obj).toHaveProperty("entityType");
    });
  });
});

describe("logDiff()", () => {
  it("properly logs diff from v1 vs v2 data when in shadow mode (treatment 3)", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    elasticTrialSearchService.getSignedElasticRequest.mockResolvedValue(
      fixtureV1ForDiff
    );

    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    elasticTrialSearchServiceV2.getSignedElasticRequest.mockResolvedValue(
      fixtureV2ForDiff
    );

    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.V2_WITH_V1_SHADOW
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const infoLogSpy = jest.spyOn(
      clinicalTrialResourceService["logger"],
      "info"
    );

    const resp = await clinicalTrialResourceService.searchTrials({
      limit: 1,
      offset: 0,
      sort: { sortBy: "NctId", direction: "ascending" }
    });

    expect(
      elasticTrialSearchService.getSignedElasticRequest
    ).toHaveBeenCalled();
    expect(
      elasticTrialSearchServiceV2.getSignedElasticRequest
    ).toHaveBeenCalled();
    resp.results[0].id = 0;

    expect(resp).toEqual(expectedResult);

    expect(infoLogSpy).toHaveBeenCalledWith(
      "nct id NCT00001151 updated fields: id,startDate,estimatedEndDate,primaryCompletionDate,studyCompletionDate,facilityName"
    );

    expect(infoLogSpy).toHaveBeenCalledWith(
      "nct id NCT00001151 added fields: h1Id,facilityName,personIds,people,facilities,acronym"
    );
  });

  it("response does not fail if shadow fails for variation 3", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    elasticTrialSearchService.getSignedElasticRequest.mockResolvedValue(
      new Error("failed")
    );

    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    elasticTrialSearchServiceV2.getSignedElasticRequest.mockResolvedValue(
      fixtureV2ForDiff
    );

    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.V2_WITH_V1_SHADOW
    );

    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );

    const warnLogSpy = jest.spyOn(
      clinicalTrialResourceService["logger"],
      "warn"
    );

    const resp = await clinicalTrialResourceService.searchTrials({
      limit: 1,
      offset: 0,
      sort: { sortBy: "NctId", direction: "ascending" }
    });

    expect(
      elasticTrialSearchServiceV2.getSignedElasticRequest
    ).toHaveBeenCalled();
    expect(
      elasticTrialSearchService.getSignedElasticRequest
    ).toHaveBeenCalled();
    resp.results[0].id = 0;
    expect(resp).toEqual(expectedResult);

    expect(warnLogSpy).toHaveBeenCalledWith(
      "Failed to get TrialSearch v1 results: TypeError: Cannot read properties of undefined (reading 'total')"
    );
  });

  const expectedResult = {
    from: 0,
    pageSize: 1,
    results: [
      {
        allocation: ["N/A"],
        avgPatientsPerSites: 6,
        briefTitle: "Studies With 1,25-Dihydroxycholecalciferol",
        conditions: ["Hypocalcemia", "Rickets"],
        contactNames: [],
        eligibilityMinAge: ["N/A"],
        enrollment: 6,
        enrollmentType: "Actual",
        estimatedEndDate: 1254355200000,
        facilities: [
          {
            city: "",
            country: "",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "National Institute of Diabetes and Digestive and Kidney Diseases (NIDDK)",
            state: "",
            zip: "",
            statusHistory: undefined
          },
          {
            city: "Bethesda",
            country: "United States",
            h1MasterOrganizationId: 6874,
            id: 6874,
            name: "National Institutes of Health Clinical Center, 9000 Rockville Pike",
            state: "Maryland",
            zip: "20892",
            statusHistory: [
              {
                facility_hash:
                  "58c5375b4f9c2be827ccd24ff501d662c212fc8062204d137648694ec88f19e5",
                hash_or_id: "6874",
                start_date: "2013-11-21"
              }
            ]
          }
        ],
        facilityCity: ["Bethesda"],
        facilityCountry: ["United States"],
        facilityName: [
          "National Institute of Diabetes and Digestive and Kidney Diseases (NIDDK)",
          "National Institutes of Health Clinical Center, 9000 Rockville Pike"
        ],
        facilityState: ["Maryland"],
        h1Id: "CT-29-2c05a9ba-0f1b-44ba-8eb7-2e63d419519b",
        id: 0,
        interventionModel: ["Single Group Assignment"],
        interventionOtherNames: [],
        interventionTypes: ["Drug"],
        interventions: ["1,25-Dihydroxycholecalciferol"],
        investigators: ["Stephen J Marx, MD"],
        lastUpdatedAt: 1385337600000,
        masking: ["None (Open Label)"],
        nctId: "NCT00001151",
        patientsPerMonths: 0.01,
        patientsPerSitesPerMonth: 0.01,
        people: [
          {
            external_uuid:
              "681ae6ba7d0fbb321c65878e624c3394a8fabce09dbdf22587ca7b885e76998f",
            name: "Stephen Marx, M.D.",
            roleHistory: [
              {
                hash_or_id:
                  "e59193bb2f86176269aa490e9e076ac50dd06ce9371ecb71aa8319c4021977fd",
                role: "Responsible Party",
                start_date: "2013-11-21",
                study_person_role_hash:
                  "e59193bb2f86176269aa490e9e076ac50dd06ce9371ecb71aa8319c4021977fd"
              }
            ],
            study_person_hash:
              "946cc963c602f5bb04a615850e078e2efd1534e811577c098ef85b2ce5a9c188"
          },
          {
            external_uuid:
              "234a3a94a649178e66538b332a789eadc45f6fd942452d9215dd06d010548b7b",
            name: "Stephen J. Marx, M.D./National Institute of Diabetes and Digestive and Kidney Diseases",
            roleHistory: [
              {
                affiliation: "NIH",
                email: "<EMAIL>",
                hash_or_id:
                  "1f73c815ac9344be2ab775353ea712438df7b5cc7fbee16713ba06d326dbf31a",
                phone: "3014965051",
                role: "Result Contact",
                start_date: "2013-11-21",
                study_person_role_hash:
                  "1f73c815ac9344be2ab775353ea712438df7b5cc7fbee16713ba06d326dbf31a"
              }
            ],
            study_person_hash:
              "290b049b4bc9afb6f0edae7a8202317f5581c429b1614fd649cb0306b5e86eb3"
          },
          {
            external_uuid:
              "ea956cd0c99af812cf3e4bd423966cddb2923981145575c622df25746e46006a",
            name: "Stephen J Marx, MD",
            roleHistory: [
              {
                affiliation: "NIDDK/NIH",
                hash_or_id:
                  "a279b71625266e20828cf7446e7070cbb75cd1761a64237c590802adc06c1daa",
                role: "Principal Investigator",
                start_date: "2013-11-21",
                study_person_role_hash:
                  "a279b71625266e20828cf7446e7070cbb75cd1761a64237c590802adc06c1daa"
              }
            ],
            study_person_hash:
              "bcecae325f63c4c815b492ec79c602d5447c97d09114d64a71cf7dc401ac9bd9"
          }
        ],
        personIds: [],
        primaryCompletionDate: 1254355200000,
        primaryPurpose: ["Treatment"],
        sitesCount: 1,
        sponsors: [
          "National Institute of Diabetes and Digestive and Kidney Diseases (NIDDK)"
        ],
        startDate: 194486400000,
        status: "Terminated",
        studyCompletionDate: 1254355200000,
        studyPhase: "Phase 2",
        studyType: "Interventional",
        trialDuration: "33 yrs 7 mos"
      }
    ],
    total: 1
  };
});

describe("trialsAutocomplete()", () => {
  it("constructs correct query when filterField is CITY", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const aggregationResponse = generateAutocompleteAggregationResponse(true);
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>(
        [generateMockHit()],
        aggregationResponse
      )
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomCity = faker.address.city();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.CITY,
      randomCity
    );
    await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        size: 0,
        _source: false,
        aggs: expect.objectContaining({
          nested: {
            nested: {
              path: "facility"
            },
            aggs: {
              filtered_matching: {
                filter: {
                  multi_match: {
                    query: input.filterValue,
                    type: "bool_prefix",
                    operator: "AND",
                    fields: ["facility.city"]
                  }
                },
                aggs: {
                  matching: {
                    terms: {
                      field: "facility.city.keyword",
                      size: 10
                    }
                  }
                }
              }
            }
          }
        })
      })
    );
  });

  it("constructs correct query when filterField is STATE", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const aggregationResponse = generateAutocompleteAggregationResponse(true);
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>(
        [generateMockHit()],
        aggregationResponse
      )
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomState = faker.address.state();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.STATE,
      randomState
    );
    await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        size: 0,
        _source: false,
        aggs: expect.objectContaining({
          nested: {
            nested: {
              path: "facility"
            },
            aggs: {
              filtered_matching: {
                filter: {
                  multi_match: {
                    query: input.filterValue,
                    type: "bool_prefix",
                    operator: "AND",
                    fields: ["facility.state"]
                  }
                },
                aggs: {
                  matching: {
                    terms: {
                      field: "facility.state.keyword",
                      size: 10
                    }
                  }
                }
              }
            }
          }
        })
      })
    );
  });

  it("constructs correct query when filterField is COUNTRY", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const aggregationResponse = generateAutocompleteAggregationResponse(true);
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>(
        [generateMockHit()],
        aggregationResponse
      )
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomCountry = faker.address.country();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.COUNTRY,
      randomCountry
    );
    await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        size: 0,
        _source: false,
        aggs: expect.objectContaining({
          nested: {
            nested: {
              path: "facility"
            },
            aggs: {
              filtered_matching: {
                filter: {
                  multi_match: {
                    query: input.filterValue,
                    type: "bool_prefix",
                    operator: "AND",
                    fields: ["facility.country"]
                  }
                },
                aggs: {
                  matching: {
                    terms: {
                      field: "facility.country.keyword",
                      size: 10
                    }
                  }
                }
              }
            }
          }
        })
      })
    );
  });

  it("returns error when elastic aggregation response is not in the expected structure", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );

    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([
        generateMockHit()
      ])
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomState = faker.address.state();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.STATE,
      randomState
    );
    await expect(async () => {
      await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    }).rejects.toThrow("Unexpected aggregation structure");
  });

  it("constructs correct query when filterField is CONDITIONS", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const aggregationResponse = generateAutocompleteAggregationResponse(false);
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>(
        [generateMockHit()],
        aggregationResponse
      )
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomCondition = faker.random.word();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.CONDITIONS,
      randomCondition
    );
    await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    const filterValueExpandedForRegex =
      expandFilterValueToRegex(randomCondition);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        size: 0,
        _source: false,
        aggs: expect.objectContaining({
          filtered_matching: {
            filter: {
              multi_match: {
                query: input.filterValue,
                type: "bool_prefix",
                operator: "AND",
                fields: ["study.condition.name"]
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: "study.condition.name.keyword",
                  size: 10,
                  include: `(.*[ \\-#~.]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
                }
              }
            }
          }
        })
      })
    );
  });

  it("constructs correct query when filterField is SPONSORS", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const aggregationResponse = generateAutocompleteAggregationResponse(false);
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>(
        [generateMockHit()],
        aggregationResponse
      )
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomSponsor = faker.random.word();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.SPONSORS,
      randomSponsor
    );
    await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    const filterValueExpandedForRegex = expandFilterValueToRegex(randomSponsor);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        size: 0,
        _source: false,
        aggs: expect.objectContaining({
          filtered_matching: {
            filter: {
              multi_match: {
                query: input.filterValue,
                type: "bool_prefix",
                operator: "AND",
                fields: ["study.sponsor.name"]
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: "study.sponsor.name.keyword",
                  size: 10,
                  include: `(.*[ \\-#~.]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
                }
              }
            }
          }
        })
      })
    );
  });

  it("constructs correct query when filterField is BIOMARKERS", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const aggregationResponse = generateAutocompleteAggregationResponse(false);
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>(
        [generateMockHit()],
        aggregationResponse
      )
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomBiomarker = faker.random.word();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.BIOMARKERS,
      randomBiomarker
    );
    await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    const filterValueExpandedForRegex =
      expandFilterValueToRegex(randomBiomarker);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        size: 0,
        _source: false,
        aggs: expect.objectContaining({
          filtered_matching: {
            filter: {
              multi_match: {
                query: input.filterValue,
                type: "bool_prefix",
                operator: "AND",
                fields: ["biomarkerWithSynonyms.autocomplete_search"]
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: "biomarkerWithSynonyms",
                  size: 10,
                  include: `(.*[ \\-#~.]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
                }
              }
            }
          }
        })
      })
    );
  });

  it("constructs correct query when filterField is BIOMARKER_INCLUSION", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const aggregationResponse = generateAutocompleteAggregationResponse(false);
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>(
        [generateMockHit()],
        aggregationResponse
      )
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomBiomarker = faker.random.word();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.BIOMARKER_INCLUSION,
      randomBiomarker
    );
    await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    const filterValueExpandedForRegex =
      expandFilterValueToRegex(randomBiomarker);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        size: 0,
        _source: false,
        aggs: expect.objectContaining({
          filtered_matching: {
            filter: {
              multi_match: {
                query: input.filterValue,
                type: "bool_prefix",
                operator: "AND",
                fields: ["biomarkerWithSynonyms_inclusion.autocomplete_search"]
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: "biomarkerWithSynonyms_inclusion",
                  size: 10,
                  include: `(.*[ \\-#~.]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
                }
              }
            }
          }
        })
      })
    );
  });

  it("constructs correct query when filterField is BIOMARKER_EXCLUSION", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const aggregationResponse = generateAutocompleteAggregationResponse(false);
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>(
        [generateMockHit()],
        aggregationResponse
      )
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomBiomarker = faker.random.word();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.BIOMARKER_EXCLUSION,
      randomBiomarker
    );
    await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    const filterValueExpandedForRegex =
      expandFilterValueToRegex(randomBiomarker);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        size: 0,
        _source: false,
        aggs: expect.objectContaining({
          filtered_matching: {
            filter: {
              multi_match: {
                query: input.filterValue,
                type: "bool_prefix",
                operator: "AND",
                fields: ["biomarkerWithSynonyms_exclusion.autocomplete_search"]
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: "biomarkerWithSynonyms_exclusion",
                  size: 10,
                  include: `(.*[ \\-#~.]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
                }
              }
            }
          }
        })
      })
    );
  });

  it("constructs correct query when filterField is INDICATIONS", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const aggregationResponse = generateAutocompleteAggregationResponse(false);
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>(
        [generateMockHit()],
        aggregationResponse
      )
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomBiomarker = faker.random.word();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.INDICATIONS,
      randomBiomarker
    );
    await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    const filterValueExpandedForRegex =
      expandFilterValueToRegex(randomBiomarker);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        size: 0,
        _source: false,
        aggs: expect.objectContaining({
          filtered_matching: {
            filter: {
              multi_match: {
                query: input.filterValue,
                type: "bool_prefix",
                operator: "AND",
                fields: ["indications.autocomplete_search"]
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: "indications",
                  size: 10,
                  include: `(.*[ \\-#~.]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
                }
              }
            }
          }
        })
      })
    );
  });

  it("constructs correct query when filterField is INDICATION_INCLUSION", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const aggregationResponse = generateAutocompleteAggregationResponse(false);
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>(
        [generateMockHit()],
        aggregationResponse
      )
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomBiomarker = faker.random.word();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.INDICATION_INCLUSION,
      randomBiomarker
    );
    await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    const filterValueExpandedForRegex =
      expandFilterValueToRegex(randomBiomarker);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        size: 0,
        _source: false,
        aggs: expect.objectContaining({
          filtered_matching: {
            filter: {
              multi_match: {
                query: input.filterValue,
                type: "bool_prefix",
                operator: "AND",
                fields: ["indication_inclusions.autocomplete_search"]
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: "indication_inclusions",
                  size: 10,
                  include: `(.*[ \\-#~.]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
                }
              }
            }
          }
        })
      })
    );
  });

  it("constructs correct query when filterField is INDICATION_EXCLUSION", async () => {
    const configService = createMockInstance(ConfigService);
    const trialsIndex = faker.datatype.string();
    configService.elasticTrialsIndexWithCTMS = trialsIndex;

    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    const elasticTrialSearchServiceV2 = createMockInstance(
      ElasticTrialSearchServiceV2
    );
    const elasticTrialSearchServiceV3 = createMockInstance(
      ElasticTrialSearchServiceV3
    );
    const elasticTrialSearchServiceV4 = createMockInstance(
      ElasticTrialSearchServiceV4
    );
    const aggregationResponse = generateAutocompleteAggregationResponse(false);
    elasticTrialSearchServiceV4.query.mockResolvedValue(
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>(
        [generateMockHit()],
        aggregationResponse
      )
    );

    const featureFlagsService = createMockInstance(
      FeatureFlagsService as any
    ) as jest.Mocked<FeatureFlagsService>;
    featureFlagsService.getFlag.mockResolvedValue(
      TrialSearchFlagVariation.CTMS
    );
    const entityTagResourceClient = createMockInstance(EntityTagResourceClient);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const clinicalTrialResourceService = new ClinicalTrialSearchResourceService(
      configService,
      elasticTrialSearchService,
      elasticTrialSearchServiceV2,
      elasticTrialSearchServiceV3,
      elasticTrialSearchServiceV4,
      featureFlagsService,
      entityTagResourceClient,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient
    );
    const randomBiomarker = faker.random.word();
    const input = generateAutocompleteInput(
      {},
      ClinicalTrialFilterAutocompleteFilterField.INDICATION_EXCLUSION,
      randomBiomarker
    );
    await clinicalTrialResourceService.autocompleteForTrialFilters(input);
    const filterValueExpandedForRegex =
      expandFilterValueToRegex(randomBiomarker);
    expect(elasticTrialSearchServiceV4.query).toHaveBeenCalledWith(
      expect.objectContaining({
        size: 0,
        _source: false,
        aggs: expect.objectContaining({
          filtered_matching: {
            filter: {
              multi_match: {
                query: input.filterValue,
                type: "bool_prefix",
                operator: "AND",
                fields: ["indication_exclusions.autocomplete_search"]
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: "indication_exclusions",
                  size: 10,
                  include: `(.*[ \\-#~.]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
                }
              }
            }
          }
        })
      })
    );
  });
});

const expectedResults = [
  {
    from: 0,
    pageSize: 1,
    results: [
      {
        allocation: ["Non-Randomized"],
        avgPatientsPerSites: Infinity,
        briefTitle:
          "Safety and Preliminary Efficacy of BNA035 in Patients With Advanced Solid Tumors",
        conditions: ["Refractory Solid Tumors"],
        contactNames: [],
        eligibilityMinAge: ["18 Years"],
        enrollment: 48,
        enrollmentType: "Anticipated",
        estimatedEndDate: 1701302400000,
        facilityCity: [],
        facilityCountry: [],
        facilityName: [],
        facilityState: [],
        h1Id: "CT-29-3c9172f8-26d9-494f-8b4a-cad5b716bf2f",
        id: 0,
        interventionModel: ["Sequential Assignment"],
        interventionOtherNames: [],
        interventionTypes: ["Drug"],
        interventions: ["BNA035"],
        investigators: [],
        lastUpdatedAt: 1639008000000,
        masking: ["None (Open Label)"],
        nctId: "NCT05150457",
        patientsPerMonths: 1.07,
        patientsPerSitesPerMonth: Infinity,
        personIds: [],
        primaryCompletionDate: 1701302400000,
        primaryPurpose: ["Treatment"],
        sitesCount: 0,
        sponsors: [],
        startDate: 1643587200000,
        status: "Not yet recruiting",
        studyCompletionDate: 1764460800000,
        studyPhase: "Phase 1",
        studyType: "Interventional",
        trialDuration: "3 yrs 9 mos"
      }
    ],
    total: 1
  },
  {
    from: 0,
    pageSize: 1,
    results: [
      {
        acronym: "COURAGE-ALS",
        allocation: ["Randomized"],
        avgPatientsPerSites: 10.88,
        briefTitle:
          "A Study to Evaluate the Efficacy and Safety of Reldesemtiv in Patients With Amyotrophic Lateral Sclerosis (ALS)",
        conditions: ["Amyotrophic Lateral Sclerosis"],
        contactNames: [
          "Rebecca Kuenzler, MD",
          "Ximena Arcila-Londono, MD",
          "Wendy Johnston",
          "James Grogan, MD",
          "Daragh Heitzman, MD",
          "Angela Genge",
          "Steve Vucic",
          "TImothy M. Miller, MD, PhD",
          "Dale J. Lange",
          "Deborah Bradshaw, MD",
          "Kelly Gwathmey, MD",
          "Robert Henderson",
          "Shafeeq Ladha, MD",
          "Lorne Zinman",
          "Jeffrey M. Statland, MD",
          "Sabrina Paganoni, MD, PhD",
          "Merrilee Needham",
          "Yuen T. So, MD, PhD",
          "Cynthia Bodkin, MD",
          "Richard Lewis, MD",
          "Jeffrey D. Rothstein, MD, PhD",
          "Namita Goyal, MD",
          "Markus Weber, MD",
          "Gary L. Pattee, MD",
          "Bjorn E. Oskarsson, MD",
          "Terry Heiman-Patterson, MD",
          "John Turnbull",
          "Jesus Mora",
          "Elham Bayat, MD",
          "Kerri Schellenberg",
          "Kourosh Rezania",
          "Colleen O'Connell",
          "L.H. van den Berg, Dr.",
          "Lawrence Korngut",
          "Amanda C. Peltier, MD",
          "Christen Shoesmith",
          "Ariel Breiner",
          "Orla Hardiman",
          "Genevieve Matte",
          "Leo McCluskey",
          "Avery St. Sauveur",
          "Tuan H. Vu, MD",
          "Jinsy Andrews, MD, MSc",
          "Michael Pulley, MD, PhD",
          "Stephen A. Goutman, MD, MS",
          "Jonathan Katz, MD",
          "Annie Dionne",
          "Dianna Quan, MD",
          "Rup Tandan, MD",
          "Philip Van Damme",
          "Dominic Fee, MD",
          "Margaret A. Owegi, DO",
          "Cytokinetics, MD"
        ],
        eligibilityMinAge: ["18 Years"],
        enrollment: 555,
        enrollmentType: "Anticipated",
        estimatedEndDate: 1703980800000,
        facilities: [
          {
            city: "Syracuse",
            country: "United States",
            h1MasterOrganizationId: 4577,
            id: 4577,
            name: "SUNY Upstate Medical University",
            state: "New York",
            zip: "13210",
            statusHistory: [
              {
                facility_hash:
                  "2211f356c317280794bad3d147d208551ae464f3932b3fc5750affb4a3e4b502",
                hash_or_id: "4577",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Cleveland",
            country: "United States",
            h1MasterOrganizationId: 820,
            id: 820,
            name: "Cleveland Clinic",
            state: "Ohio",
            zip: "44195",
            statusHistory: [
              {
                facility_hash:
                  "5f1cec9d15b159117600aa49c99e55705b390c66129851e4348d11d88baa016e",
                hash_or_id: "820",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Indianapolis",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Indiana University",
            state: "Indiana",
            zip: "46202",
            statusHistory: [
              {
                facility_hash:
                  "250a99ac6051d57b0f0d0b2d4d76c28d9f07906d3d06a536b43daf4a33772b05",
                hash_or_id:
                  "250a99ac6051d57b0f0d0b2d4d76c28d9f07906d3d06a536b43daf4a33772b05",
                start_date: "2022-02-11",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Saint Gallen",
            country: "Switzerland",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Muskelzentrum/ALS Clinic",
            state: "",
            zip: "9007",
            statusHistory: [
              {
                facility_hash:
                  "1b042d26e1fd28eedf3dd00c36cac59842856e9cd877707d1968d8d1c907155a",
                hash_or_id:
                  "1b042d26e1fd28eedf3dd00c36cac59842856e9cd877707d1968d8d1c907155a",
                start_date: "2022-02-11",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "London",
            country: "Canada",
            h1MasterOrganizationId: 17887,
            id: 17887,
            name: "London Health Sciences Centre",
            state: "Ontario",
            zip: "N6A 5A5",
            statusHistory: [
              {
                facility_hash:
                  "d37038c5b7ff97c5eee6766d6717b2bb9fdc515304a6cba6d84940d56ebeadfa",
                hash_or_id: "17887",
                start_date: "2021-12-08",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Boston",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Massachusetts General Hospital - Neurological Clinical Research Institute",
            state: "Massachusetts",
            zip: "02114",
            statusHistory: [
              {
                facility_hash:
                  "fee6b876e1288c6fe6b1e04baae242f1d04f74ad8d44918e106a2dae10d9d86a",
                hash_or_id:
                  "fee6b876e1288c6fe6b1e04baae242f1d04f74ad8d44918e106a2dae10d9d86a",
                start_date: "2022-01-11",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Washington",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "GW Medical Faculty Associates",
            state: "District of Columbia",
            zip: "20037",
            statusHistory: [
              {
                facility_hash:
                  "161654fe98ca9ad2ea6ec0a67cec58153467e1aeac0d8a86d21d7f2aaafb83b5",
                hash_or_id:
                  "161654fe98ca9ad2ea6ec0a67cec58153467e1aeac0d8a86d21d7f2aaafb83b5",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Fredericton",
            country: "Canada",
            h1MasterOrganizationId: 86784,
            id: 86784,
            name: "Stan Cassidy Centre for Rehabilitation",
            state: "New Brunswick",
            zip: "E3B 0C7",
            statusHistory: [
              {
                facility_hash:
                  "63ae490c04e337dd21c48971cfe60cd5cd19d5e744393166721fc28447e5f234",
                hash_or_id: "86784",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Montréal",
            country: "Canada",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "McGill University, Montreal Neurological Institute & Hospital",
            state: "Quebec",
            zip: "H3A 2B4",
            statusHistory: [
              {
                facility_hash:
                  "00c64141e2fa0413488689c1b57fcf25bbeae82f7fc7dd81f13ab5eca93c656a",
                hash_or_id:
                  "00c64141e2fa0413488689c1b57fcf25bbeae82f7fc7dd81f13ab5eca93c656a",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Calgary",
            country: "Canada",
            h1MasterOrganizationId: 128946,
            id: 128946,
            name: "University of Calgary - Heritage Medical Research Clinic",
            state: "Alberta",
            zip: "T2N 4Z6",
            statusHistory: [
              {
                facility_hash:
                  "8334432a34a1ec53d569c67a9e080caa5d40f75012ab14a7aee6ad81fbd6f960",
                hash_or_id: "128946",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Ann Arbor",
            country: "United States",
            h1MasterOrganizationId: 5390,
            id: 5390,
            name: "Michigan Medicine",
            state: "Michigan",
            zip: "48109",
            statusHistory: [
              {
                facility_hash:
                  "6a74629bdca19e99fad996bab6878423951cb08cab880152d6254e3c8710b265",
                hash_or_id: "5390",
                start_date: "2022-01-11",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Québec",
            country: "Canada",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "CHU de Quebec-Université Laval",
            state: "Quebec",
            zip: "G1J 1Z4",
            statusHistory: [
              {
                facility_hash:
                  "4174373179685f8776293fe6c1816a1a0eb0f43618ad2c821e7281d77fd66c21",
                hash_or_id:
                  "4174373179685f8776293fe6c1816a1a0eb0f43618ad2c821e7281d77fd66c21",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Baltimore",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Johns Hopkins Outpatient Center",
            state: "Maryland",
            zip: "21287",
            statusHistory: [
              {
                facility_hash:
                  "01a2a0c4a98feb3f54a3e0a95143ccda3fae308b3d38cdd366c9a132593f8cdb",
                hash_or_id:
                  "01a2a0c4a98feb3f54a3e0a95143ccda3fae308b3d38cdd366c9a132593f8cdb",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Jacksonville",
            country: "United States",
            h1MasterOrganizationId: 2393,
            id: 2393,
            name: "Mayo Clinic Florida",
            state: "Florida",
            zip: "32224",
            statusHistory: [
              {
                facility_hash:
                  "1f71eeccc51ca5bc0a4261a8f902d2ac9108ad65aea87a35164b5a90bdf7ed4e",
                hash_or_id: "2393",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Lincoln",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Neurology Associates, PC",
            state: "Nebraska",
            zip: "68506",
            statusHistory: [
              {
                facility_hash:
                  "ca6651681df6a0afc082add6c113cb8cbbf31df01a87d0240d971306cf42f6fd",
                hash_or_id:
                  "ca6651681df6a0afc082add6c113cb8cbbf31df01a87d0240d971306cf42f6fd",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Dallas",
            country: "United States",
            h1MasterOrganizationId: 67631,
            id: 67631,
            name: "Texas Neurology, P.A.",
            state: "Texas",
            zip: "75206",
            statusHistory: [
              {
                facility_hash:
                  "f9350f8293d8b83c8f5e1f812e3ffae66d040e1fc2dae3e5ee0bb0953dc622d6",
                hash_or_id: "67631",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Charlotte",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Atrium Health Neuroscience Institute - Charlotte",
            state: "North Carolina",
            zip: "28207",
            statusHistory: [
              {
                facility_hash:
                  "85a08db642b6bfd5d4557ac825f74f3efbb08a57ef048d4ceb05369954597cdd",
                hash_or_id:
                  "85a08db642b6bfd5d4557ac825f74f3efbb08a57ef048d4ceb05369954597cdd",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Aurora",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "University of Colorado Hospital Anschutz Outpatient Pavilion",
            state: "Colorado",
            zip: "80045",
            statusHistory: [
              {
                facility_hash:
                  "68ee6b0c9678bcf3ef170774834ddc03bb5d952bb10d066018735d1cf58c5619",
                hash_or_id:
                  "68ee6b0c9678bcf3ef170774834ddc03bb5d952bb10d066018735d1cf58c5619",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Phoenix",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "St. Joseph's Hospital & Medical Center - Barrow Neurological Institute",
            state: "Arizona",
            zip: "85013",
            statusHistory: [
              {
                facility_hash:
                  "fde48b3325f6bdc44aa0dcd5396f31e1ebb933dc053eacbbde2aa14acc07e279",
                hash_or_id:
                  "fde48b3325f6bdc44aa0dcd5396f31e1ebb933dc053eacbbde2aa14acc07e279",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Tampa",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "University of South Florida - Carol and Frank Morsani Center for Advanced Health Care",
            state: "Florida",
            zip: "33612",
            statusHistory: [
              {
                facility_hash:
                  "8e0c0d576e50239feff07fe490d55496b024c695858c5658a6d3569815415ea5",
                hash_or_id:
                  "8e0c0d576e50239feff07fe490d55496b024c695858c5658a6d3569815415ea5",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Madrid",
            country: "Spain",
            h1MasterOrganizationId: 28217,
            id: 28217,
            name: "Hospital San Rafael",
            state: "",
            zip: "28016",
            statusHistory: [
              {
                facility_hash:
                  "1d83006788aab11d27313c50e0bbc5be9bbcc33dd48169be65e4f628ad5ceb35",
                hash_or_id: "28217",
                start_date: "2022-02-11",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Saint Louis",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Washington University School of Medicine - Center for Advance Medicine",
            state: "Missouri",
            zip: "63108",
            statusHistory: [
              {
                facility_hash:
                  "ba6355474b050db66d7f57027b9fce5dab5311e7e2c444a82fe3b022e6c12785",
                hash_or_id:
                  "ba6355474b050db66d7f57027b9fce5dab5311e7e2c444a82fe3b022e6c12785",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Worcester",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "University of Massachusetts Memorial Medical Center/Medical School",
            state: "Massachusetts",
            zip: "01655",
            statusHistory: [
              {
                facility_hash:
                  "813acce6c55e77a6f5ce73ae738934ea37e32036f8efed435b958a81866f017d",
                hash_or_id:
                  "813acce6c55e77a6f5ce73ae738934ea37e32036f8efed435b958a81866f017d",
                start_date: "2021-12-08",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Beaumont",
            country: "Ireland",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "RSCI Education and Research Centre, Beaumont Hospital",
            state: "Dublin",
            zip: "9",
            statusHistory: [
              {
                facility_hash:
                  "8e0a368ca6d953c76b3d8acc526dbd39e21447d314c6acaf7be3a21679a1d388",
                hash_or_id:
                  "8e0a368ca6d953c76b3d8acc526dbd39e21447d314c6acaf7be3a21679a1d388",
                start_date: "2021-12-20",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Stanford",
            country: "United States",
            h1MasterOrganizationId: 5988,
            id: 5988,
            name: "Stanford Hospital and Clinics",
            state: "California",
            zip: "94305",
            statusHistory: [
              {
                facility_hash:
                  "e9a4c2934d0efe2abbbeb46ea38435d8de035603b040128f25aed4b1c7dff13d",
                hash_or_id: "5988",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Nedlands",
            country: "Australia",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "The Perron Institute",
            state: "Western Australia",
            zip: "6009",
            statusHistory: [
              {
                facility_hash:
                  "152dae985d3d21cdfb2aa0e716e30da3f4acfc644f63979441bffd1cbf731d11",
                hash_or_id:
                  "152dae985d3d21cdfb2aa0e716e30da3f4acfc644f63979441bffd1cbf731d11",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Orange",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "University of California Irvine - ALS & Neuromuscular Center",
            state: "California",
            zip: "92868",
            statusHistory: [
              {
                facility_hash:
                  "c662f823265df7a5ee63ce740cd3a5663da9bfd27a6f61021f779dd544ae5989",
                hash_or_id:
                  "c662f823265df7a5ee63ce740cd3a5663da9bfd27a6f61021f779dd544ae5989",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Hershey",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Penn State Health Milton S. Hershey Medical Center",
            state: "Pennsylvania",
            zip: "17033",
            statusHistory: [
              {
                facility_hash:
                  "07328d69adc1dbd039af2dd4eb2c8f87371a7f0e4372448fb129ee17131c9760",
                hash_or_id:
                  "07328d69adc1dbd039af2dd4eb2c8f87371a7f0e4372448fb129ee17131c9760",
                start_date: "2022-02-11",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Montréal",
            country: "Canada",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Centre de recherche du CHUM",
            state: "Quebec",
            zip: "H2X 0A9",
            statusHistory: [
              {
                facility_hash:
                  "df2e8dcb00a3b88689b071b20cbe089a1c07368a57be2822f168b491a39d8a5f",
                hash_or_id:
                  "df2e8dcb00a3b88689b071b20cbe089a1c07368a57be2822f168b491a39d8a5f",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Kansas City",
            country: "United States",
            h1MasterOrganizationId: 11627,
            id: 11627,
            name: "The University of Kansas Medical Center",
            state: "Kansas",
            zip: "66160",
            statusHistory: [
              {
                facility_hash:
                  "9d54fdac47d2be5c0f6ad276ef14bb6ff687d3cd9e555b7257d591babcc3251a",
                hash_or_id: "11627",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Philadelphia",
            country: "United States",
            h1MasterOrganizationId: 8542,
            id: 8542,
            name: "Lewis Katz School of Medicine at Temple University",
            state: "Pennsylvania",
            zip: "19140",
            statusHistory: [
              {
                facility_hash:
                  "40d654330cf64a8f8beff2cddbfd525eb4faafff2125c4d75f5b38764ccb7d87",
                hash_or_id: "8542",
                start_date: "2022-02-11",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Chicago",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Duchossois Center for Advanced Medicine",
            state: "Illinois",
            zip: "60637",
            statusHistory: [
              {
                facility_hash:
                  "d5939b0e48442917eb5e33230c745582cb63bb0bd15dec615c99e8c92d310f60",
                hash_or_id:
                  "d5939b0e48442917eb5e33230c745582cb63bb0bd15dec615c99e8c92d310f60",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Los Angeles",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Cedars-Sinai Medical Center",
            state: "California",
            zip: "90048",
            statusHistory: [
              {
                facility_hash:
                  "ab35d7ff83fa66d307822f3cbdfd0014efa5721b45a69216b83948f214c8af1a",
                hash_or_id:
                  "ab35d7ff83fa66d307822f3cbdfd0014efa5721b45a69216b83948f214c8af1a",
                start_date: "2022-02-11",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Milwaukee",
            country: "United States",
            h1MasterOrganizationId: 24402,
            id: 24402,
            name: "Froedtert Hospital",
            state: "Wisconsin",
            zip: "53226",
            statusHistory: [
              {
                facility_hash:
                  "357d34f6d37f0f9a97ca3994722c3440210a5357c27d537405ca3f8a131edf04",
                hash_or_id: "24402",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "New York",
            country: "United States",
            h1MasterOrganizationId: 1768,
            id: 1768,
            name: "Hospital for Special Surgery",
            state: "New York",
            zip: "10021",
            statusHistory: [
              {
                facility_hash:
                  "f203db51f994ba912128c2e9d9013157f43027cb426eba822e11989930a47a2f",
                hash_or_id: "1768",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Edmonton",
            country: "Canada",
            h1MasterOrganizationId: 11449,
            id: 11449,
            name: "University of Alberta",
            state: "Alberta",
            zip: "T6G 2B7",
            statusHistory: [
              {
                facility_hash:
                  "81c7d62e848e5fd32eb1c5a5a18e18fbf38c0711ea71bc30a5b55b6464e939bf",
                hash_or_id: "11449",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Utrecht",
            country: "Netherlands",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "UMC Utrecht, Department of Neurology, ALS Center",
            state: "",
            zip: "3584 CX",
            statusHistory: [
              {
                facility_hash:
                  "7d29319dfa1d0acd0c381d6b6bb97ed35e37eb2d2511c70cffa2e83da15afa87",
                hash_or_id:
                  "7d29319dfa1d0acd0c381d6b6bb97ed35e37eb2d2511c70cffa2e83da15afa87",
                start_date: "2021-12-20",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Toronto",
            country: "Canada",
            h1MasterOrganizationId: 72643,
            id: 72643,
            name: "Sunnybrook Research Institute",
            state: "Ontario",
            zip: "M4N 3M5",
            statusHistory: [
              {
                facility_hash:
                  "6fc1f36c484ce82fad58a3a32180e41ecd12a73222c9a540bce2c402b0c8e28a",
                hash_or_id: "72643",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Burlington",
            country: "United States",
            h1MasterOrganizationId: 4622,
            id: 4622,
            name: "University of Vermont Medical Center",
            state: "Vermont",
            zip: "05401",
            statusHistory: [
              {
                facility_hash:
                  "d28b563a8b9fbb95372a76d48d84645c22bbd27152830cf40cf65bd44740d6af",
                hash_or_id: "4622",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Leuven",
            country: "Belgium",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "UZ Leuven Gasthuisberg, Department of Neurology",
            state: "",
            zip: "3000",
            statusHistory: [
              {
                facility_hash:
                  "dd152ba85532ed4db470cc1c1eef8447d3e53b8577eb4ddd0e0f8d2a5ead1f04",
                hash_or_id:
                  "dd152ba85532ed4db470cc1c1eef8447d3e53b8577eb4ddd0e0f8d2a5ead1f04",
                start_date: "2022-02-11",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "New York",
            country: "United States",
            h1MasterOrganizationId: 6095,
            id: 6095,
            name: "Columbia University Medical Center",
            state: "New York",
            zip: "10032",
            statusHistory: [
              {
                facility_hash:
                  "102e8019e48369c02b74df603226333dc5bd927233749073eb85bb663f0e1b16",
                hash_or_id: "6095",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Nashville",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Vanderbilt University Medical Center - Clinical Research Center",
            state: "Tennessee",
            zip: "37232",
            statusHistory: [
              {
                facility_hash:
                  "2bfe4a32ed4a8143492c7d796a227b04d16ec4ca36465ee3382f60cfb24dc593",
                hash_or_id:
                  "2bfe4a32ed4a8143492c7d796a227b04d16ec4ca36465ee3382f60cfb24dc593",
                start_date: "2022-01-11",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Herston",
            country: "Australia",
            h1MasterOrganizationId: 23922,
            id: 23922,
            name: "Royal Brisbane and Women's Hospital",
            state: "Queensland",
            zip: "4029",
            statusHistory: [
              {
                facility_hash:
                  "e3c261c474bba40fbbed8090ce8c3f342bfaf1d20b436909c9a711d426f3cff9",
                hash_or_id: "23922",
                start_date: "2021-12-08",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Ottawa",
            country: "Canada",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Ottawa Hospital Research Institute - Civic Campus",
            state: "Ontario",
            zip: "K1Y 4E9",
            statusHistory: [
              {
                facility_hash:
                  "69b56b35b3435e5cc81804c246b40735b19a75e83cd3c9d75e010783b075ee6b",
                hash_or_id:
                  "69b56b35b3435e5cc81804c246b40735b19a75e83cd3c9d75e010783b075ee6b",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Jacksonville",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "University of Florida Jacksonville",
            state: "Florida",
            zip: "32209",
            statusHistory: [
              {
                facility_hash:
                  "4a3641f547e5c5653fc8319efc387d15f1d962a3e4e5eb1854e7a542a889bfae",
                hash_or_id:
                  "4a3641f547e5c5653fc8319efc387d15f1d962a3e4e5eb1854e7a542a889bfae",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Hamilton",
            country: "Canada",
            h1MasterOrganizationId: 74752,
            id: 74752,
            name: "McMaster University Medical Centre",
            state: "Ontario",
            zip: "L8N 3Z5",
            statusHistory: [
              {
                facility_hash:
                  "d94ce253da0571446435d0f7b87c49839d6669cc9f2e82a9b0d29798e345726e",
                hash_or_id: "74752",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Detroit",
            country: "United States",
            h1MasterOrganizationId: 5293,
            id: 5293,
            name: "Henry Ford Health System",
            state: "Michigan",
            zip: "48202",
            statusHistory: [
              {
                facility_hash:
                  "0aa0d47e711019af2420bf3ee446ffe4992b396aff7de1a34ea407d085a5674f",
                hash_or_id: "5293",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Concord",
            country: "Australia",
            h1MasterOrganizationId: 77346,
            id: 77346,
            name: "Concord Repatriation General Hospital",
            state: "New South Wales",
            zip: "2139",
            statusHistory: [
              {
                facility_hash:
                  "b59227bfe930ac11d4b9f84770be382cad05f70008c3b95f890ec41959ef65e1",
                hash_or_id: "77346",
                start_date: "2021-12-08",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "San Francisco",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "California Pacific Medical Center - Forbes Norris MDA/ALS Research Center",
            state: "California",
            zip: "94109",
            statusHistory: [
              {
                facility_hash:
                  "60cb8141a5ffe7e236a9684eed8da24cda43ed32b4b9ce95548f8242ab3fe8e9",
                hash_or_id:
                  "60cb8141a5ffe7e236a9684eed8da24cda43ed32b4b9ce95548f8242ab3fe8e9",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Saskatoon",
            country: "Canada",
            h1MasterOrganizationId: 74758,
            id: 74758,
            name: "Saskatoon City Hospital",
            state: "Saskatchewan",
            zip: "S7K 0M7",
            statusHistory: [
              {
                facility_hash:
                  "ed0d8917dcfb5f4b953fac663f896612bff91c028055207f29000f95a0624d78",
                hash_or_id: "74758",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "Henrico",
            country: "United States",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "VCU Neuroscience Orthopaedic and Wellness Center (NOW)",
            state: "Virginia",
            zip: "23233",
            statusHistory: [
              {
                facility_hash:
                  "4b8c063e7ea4272ef689fd029b633b899ae38311ada51718c23d9cf1be6e6f32",
                hash_or_id:
                  "4b8c063e7ea4272ef689fd029b633b899ae38311ada51718c23d9cf1be6e6f32",
                start_date: "2021-10-29",
                status: "Recruiting"
              }
            ]
          },
          {
            city: "",
            country: "",
            h1MasterOrganizationId: undefined,
            id: 0,
            name: "Cytokinetics",
            state: "",
            zip: "",
            statusHistory: undefined
          }
        ],
        facilityCity: [
          "Syracuse",
          "Cleveland",
          "Indianapolis",
          "Saint Gallen",
          "London",
          "Boston",
          "Washington",
          "Fredericton",
          "Montréal",
          "Calgary",
          "Ann Arbor",
          "Québec",
          "Baltimore",
          "Jacksonville",
          "Lincoln",
          "Dallas",
          "Charlotte",
          "Aurora",
          "Phoenix",
          "Tampa",
          "Madrid",
          "Saint Louis",
          "Worcester",
          "Beaumont",
          "Stanford",
          "Nedlands",
          "Orange",
          "Hershey",
          "Kansas City",
          "Philadelphia",
          "Chicago",
          "Los Angeles",
          "Milwaukee",
          "New York",
          "Edmonton",
          "Utrecht",
          "Toronto",
          "Burlington",
          "Leuven",
          "Nashville",
          "Herston",
          "Ottawa",
          "Hamilton",
          "Detroit",
          "Concord",
          "San Francisco",
          "Saskatoon",
          "Henrico"
        ],
        facilityCountry: [
          "United States",
          "Switzerland",
          "Canada",
          "Spain",
          "Ireland",
          "Australia",
          "Netherlands",
          "Belgium"
        ],
        facilityName: [
          "SUNY Upstate Medical University",
          "Cleveland Clinic",
          "Indiana University",
          "Muskelzentrum/ALS Clinic",
          "London Health Sciences Centre",
          "Massachusetts General Hospital - Neurological Clinical Research Institute",
          "GW Medical Faculty Associates",
          "Stan Cassidy Centre for Rehabilitation",
          "McGill University, Montreal Neurological Institute & Hospital",
          "University of Calgary - Heritage Medical Research Clinic",
          "Michigan Medicine",
          "CHU de Quebec-Université Laval",
          "Johns Hopkins Outpatient Center",
          "Mayo Clinic Florida",
          "Neurology Associates, PC",
          "Texas Neurology, P.A.",
          "Atrium Health Neuroscience Institute - Charlotte",
          "University of Colorado Hospital Anschutz Outpatient Pavilion",
          "St. Joseph's Hospital & Medical Center - Barrow Neurological Institute",
          "University of South Florida - Carol and Frank Morsani Center for Advanced Health Care",
          "Hospital San Rafael",
          "Washington University School of Medicine - Center for Advance Medicine",
          "University of Massachusetts Memorial Medical Center/Medical School",
          "RSCI Education and Research Centre, Beaumont Hospital",
          "Stanford Hospital and Clinics",
          "The Perron Institute",
          "University of California Irvine - ALS & Neuromuscular Center",
          "Penn State Health Milton S. Hershey Medical Center",
          "Centre de recherche du CHUM",
          "The University of Kansas Medical Center",
          "Lewis Katz School of Medicine at Temple University",
          "Duchossois Center for Advanced Medicine",
          "Cedars-Sinai Medical Center",
          "Froedtert Hospital",
          "Hospital for Special Surgery",
          "University of Alberta",
          "UMC Utrecht, Department of Neurology, ALS Center",
          "Sunnybrook Research Institute",
          "University of Vermont Medical Center",
          "UZ Leuven Gasthuisberg, Department of Neurology",
          "Columbia University Medical Center",
          "Vanderbilt University Medical Center - Clinical Research Center",
          "Royal Brisbane and Women's Hospital",
          "Ottawa Hospital Research Institute - Civic Campus",
          "University of Florida Jacksonville",
          "McMaster University Medical Centre",
          "Henry Ford Health System",
          "Concord Repatriation General Hospital",
          "California Pacific Medical Center - Forbes Norris MDA/ALS Research Center",
          "Saskatoon City Hospital",
          "VCU Neuroscience Orthopaedic and Wellness Center (NOW)",
          "Cytokinetics"
        ],
        facilityState: [
          "New York",
          "Ohio",
          "Indiana",
          "Ontario",
          "Massachusetts",
          "District of Columbia",
          "New Brunswick",
          "Quebec",
          "Alberta",
          "Michigan",
          "Maryland",
          "Florida",
          "Nebraska",
          "Texas",
          "North Carolina",
          "Colorado",
          "Arizona",
          "Missouri",
          "Dublin",
          "California",
          "Western Australia",
          "Pennsylvania",
          "Kansas",
          "Illinois",
          "Wisconsin",
          "Vermont",
          "Tennessee",
          "Queensland",
          "New South Wales",
          "Saskatchewan",
          "Virginia"
        ],
        id: 0,
        h1Id: "CT-29-846c91b0-d34d-4991-91b2-b94464a37bf2",
        interventionModel: ["Parallel Assignment"],
        interventionOtherNames: [],
        interventionTypes: ["Drug"],
        interventions: ["Reldesemtiv", "Placebo"],
        investigators: [],
        lastUpdatedAt: 1646006400000,
        masking: ["Quadruple"],
        nctId: "NCT04944784",
        patientsPerMonths: 17.9,
        patientsPerSitesPerMonth: 0.35,
        people: [
          {
            external_uuid:
              "f089c5a9923d4dd0cb585605d6f0de6862263f0b414de971829ca920bc4fd1a8",
            h1_person_id: 670639,
            name: "Rebecca Kuenzler, MD",
            roleHistory: [
              {
                hash_or_id: "670639",
                role: "Responsible Party",
                start_date: "2021-10-29",
                study_person_role_hash:
                  "279477d3c2eac2597e8eea8f96664dd290a301e69c71c6b0c717204df0913bd9"
              }
            ],
            study_person_hash:
              "e026473801da36070c4187997c9f57140fed3161ce2b15648e94864ed46cb034"
          },
          {
            external_uuid:
              "f089c5a9923d4dd0cb585605d6f0de6862263f0b414de971829ca920bc4fd1a8",
            h1_person_id: 670639,
            name: "Rebecca Kuenzler, MD",
            roleHistory: [
              {
                hash_or_id: "670639",
                role: "Facility Contact",
                start_date: "2021-10-29",
                study_person_role_hash:
                  "279477d3c2eac2597e8eea8f96664dd290a301e69c71c6b0c717204df0913bd9"
              }
            ],
            study_person_hash:
              "e026473801da36070c4187997c9f57140fed3161ce2b15648e94864ed46cb034"
          },
          {
            external_uuid:
              "e45caaf0691bada71198c56b27d030e901ff5a78be9c153fbc0900644b819238",
            h1_person_id: 3275896,
            name: "Ximena Arcila-Londono, MD",
            roleHistory: [
              {
                hash_or_id: "3275896",
                role: "Facility Contact",
                start_date: "2021-09-23",
                study_person_role_hash:
                  "c721c1f74548842176f57746dcecba01858f1f2c9d1476228bce85ea6bcd474d"
              }
            ],
            study_person_hash:
              "c48e0b3ad8478db5d9fdc8cef61bb9f29f64558d38459f0387fce8b9feddfdb2"
          },
          {
            external_uuid:
              "a6015e809f30485a7ac8d9f686022d42871692023de873551be2202e6344462a",
            h1_person_id: 7817212,
            name: "Wendy Johnston",
            roleHistory: [
              {
                hash_or_id: "7817212",
                role: "Facility Contact",
                start_date: "2021-08-26",
                study_person_role_hash:
                  "ad62502db88643a825071d2df13b93f147d24b32e1dd5b2178c11fbd370c7652"
              }
            ],
            study_person_hash:
              "83ee3efbc78c6d7c872f68e3cb4fb53aa63cf28d49c4cd5f0cc668e1a2b15e17"
          },
          {
            external_uuid:
              "2d19c0d42566a0482fe03b656ae90624f861ab524bdd85b4e1eb742866d0b979",
            name: "Cytokinetics, MD",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "b1d33857815467de3f9e0dba1d2a303b1c775912fe984c9fb0283da862af4be2",
                phone: "************",
                role: "Central Contact",
                start_date: "2021-06-30",
                study_person_role_hash:
                  "b1d33857815467de3f9e0dba1d2a303b1c775912fe984c9fb0283da862af4be2"
              },
              {
                affiliation: "Cytokinetics",
                hash_or_id:
                  "6755089643734c933f8ee0916a2a39a9f2861cb2d65bf3f87569777b8db34629",
                role: "Study Director",
                start_date: "2021-06-30",
                study_person_role_hash:
                  "6755089643734c933f8ee0916a2a39a9f2861cb2d65bf3f87569777b8db34629"
              }
            ],
            study_person_hash:
              "9e58071b0d5e29eebe96d985287d580a3ebac1612e31ccd3cd4caa9aa9f01e23"
          },
          {
            external_uuid:
              "4acc41962efcc5f78bdf3105a5d39d5ce9b78268cf0978a198723378352cc3ba",
            name: "James Grogan, MD",
            roleHistory: [
              {
                hash_or_id:
                  "18d9c1429d4f2c63695d29b6b08190bdb939e18e5f537a50e4cb5264f26350e4",
                role: "Facility Contact",
                start_date: "2022-02-11",
                study_person_role_hash:
                  "18d9c1429d4f2c63695d29b6b08190bdb939e18e5f537a50e4cb5264f26350e4"
              }
            ],
            study_person_hash:
              "eeda379a5896e4c96dfd7390bcdbabb4141d228cc86eebd0968b2e072a7d98ff"
          },
          {
            external_uuid:
              "8388145b78da2891ee3c61fe25c458560443f073d7b080a83e767dec6bc25cfe",
            h1_person_id: 112023,
            name: "Daragh Heitzman, MD",
            roleHistory: [
              {
                hash_or_id: "112023",
                role: "Facility Contact",
                start_date: "2021-08-17",
                study_person_role_hash:
                  "d6b014577942f0057ff5c7c8f19815656817973f6617450a6c76d0f6ad7140e5"
              }
            ],
            study_person_hash:
              "008bdc3b5c04ede0811af0260881033b51449dbc610c8e42967a85fad5cfd175"
          },
          {
            external_uuid:
              "b30f7ecfb45c62fb4ef1f157b1e1b13cfd0d780040d595a71c5eb597519b4454",
            h1_person_id: 5184419,
            name: "Angela Genge",
            roleHistory: [
              {
                hash_or_id: "5184419",
                role: "Facility Contact",
                start_date: "2021-08-17",
                study_person_role_hash:
                  "e5241186b7644ce3ba1709f412486164f28af83c57b14273ee38cad7c958ba63"
              }
            ],
            study_person_hash:
              "64f816fe121862bd0bac7fecd802831dfe81c3dbf22cb021a34d32ea32cbcca3"
          },
          {
            external_uuid:
              "db9ed738271ba260122f3ed79d326b02da68985e9165b52fa289219d4bb7ce6d",
            name: "Steve Vucic",
            roleHistory: [
              {
                hash_or_id:
                  "353b67f9621b6f7803843e96a6dc0e33e4f82d5a543b12905e6e47b02879eaa7",
                role: "Facility Contact",
                start_date: "2021-12-08",
                study_person_role_hash:
                  "353b67f9621b6f7803843e96a6dc0e33e4f82d5a543b12905e6e47b02879eaa7"
              }
            ],
            study_person_hash:
              "a2154936f8ebda4e5f592c92677637b59d61a08e47641442d9589a9051993f34"
          },
          {
            external_uuid:
              "3361525093d180bbeaf4c1be22c12f757fd45d7557feddbcc730d0f95c12cb23",
            h1_person_id: 1097144,
            name: "TImothy M. Miller, MD, PhD",
            roleHistory: [
              {
                hash_or_id: "1097144",
                role: "Facility Contact",
                start_date: "2021-08-17",
                study_person_role_hash:
                  "b1d0cafaa8e0841d30908ec4daecc57a677403446c2ef6d4a30669eeeb87c17d"
              }
            ],
            study_person_hash:
              "f62bc0cf1b03274514afb42b65e09a0d0b5b5ce7ea1b2aff35873b1db33b75bf"
          },
          {
            external_uuid:
              "1f457129df6ffca574d2dbd7d31a519020797bc45f5d4ff11cac27f3531de8e2",
            h1_person_id: 2043661,
            name: "Dale J. Lange",
            roleHistory: [
              {
                hash_or_id: "2043661",
                role: "Facility Contact",
                start_date: "2021-08-31",
                study_person_role_hash:
                  "43096e43c646997bf377d9457566d9d739e89aef50d7fe60bd84c343ffe63db5"
              }
            ],
            study_person_hash:
              "47b96326e1b163d7339837035ecc3322f537dcba71c99e23fb59411533a6bff6"
          },
          {
            external_uuid:
              "e5dc24f3f63c2edb8b677f84d3ba8ba80364f72eaa98e8663e8ea189017e1b6e",
            h1_person_id: 61300,
            name: "Deborah Bradshaw, MD",
            roleHistory: [
              {
                hash_or_id: "61300",
                role: "Facility Contact",
                start_date: "2021-08-17",
                study_person_role_hash:
                  "c3aed4c38f0be348e0b291b3030e207e7f39ae3e7d0ebce6141c87fdcef26faf"
              },
              {
                hash_or_id: "613001",
                role: "Principal Investigator",
                start_date: "2021-08-17",
                study_person_role_hash:
                  "c3aed4c38f0be348e0b291b3030e207e7f39ae3e7d0ebce6141c87fdcef26fax"
              }
            ],
            study_person_hash:
              "0f4665d3106393aeeefe9cf4485f824ffe3684f8f49cb9184417d8039e1ebbf5"
          },
          {
            external_uuid:
              "df3b660a7af3862f318f9878bb0f3f50c40a699feccffbb130a90ba17d91e13e",
            h1_person_id: 372304,
            name: "Kelly Gwathmey, MD",
            roleHistory: [
              {
                hash_or_id: "372304",
                role: "Facility Contact",
                start_date: "2021-08-30",
                study_person_role_hash:
                  "fa3dee73e962b91a52f9060500ced67e0269e3e79dc241b1f1980389964af808"
              }
            ],
            study_person_hash:
              "1137817f20ca0163299343b7cef37c602f182324a27029f20d565a2ecf5edd19"
          },
          {
            external_uuid:
              "d5395fd25b7b071378e59de651f43a8a1fe69c26f923d2ca7668b348818c6fb8",
            name: "Robert Henderson",
            roleHistory: [
              {
                hash_or_id:
                  "78e284f2b54e2665814d0657afe7a273bc08b6ea613b7c1c7d4dee10ec40dd37",
                role: "Facility Contact",
                start_date: "2021-11-25",
                study_person_role_hash:
                  "78e284f2b54e2665814d0657afe7a273bc08b6ea613b7c1c7d4dee10ec40dd37"
              }
            ],
            study_person_hash:
              "cad65b20c7751df2dd3bf17f33bc974d2f2c26875f756fc7c23d4b66d5d54bf0"
          },
          {
            external_uuid:
              "163e110c18ada670131aa736b8e11ac49ded21ecbf23721cdf43927835a51709",
            h1_person_id: 1677852,
            name: "Shafeeq Ladha, MD",
            roleHistory: [
              {
                hash_or_id: "1677852",
                role: "Facility Contact",
                start_date: "2021-08-19",
                study_person_role_hash:
                  "84c9cfc08ebf5cb4c261897a7f0137df4223a02de72f6c6312d6f30bddaa1198"
              }
            ],
            study_person_hash:
              "0cb3df5fd38e8372af6befe6a2a613323c7f07f84bc4736c6074354906f3a89e"
          },
          {
            external_uuid:
              "342c11681c967f2e145d003012e6cce79f83334fd0f440d8ad2ed184024c4fbc",
            h1_person_id: 5161832,
            name: "Lorne Zinman",
            roleHistory: [
              {
                hash_or_id: "5161832",
                role: "Facility Contact",
                start_date: "2021-08-19",
                study_person_role_hash:
                  "fcd55bb5cd65e95bb35b35631048d7c654dfe829a5a51eff2584a27446b4e38b"
              }
            ],
            study_person_hash:
              "6bc7234edb0654c8567742c1a9271da478d86f763b6c5ef2bb0eb456e38a3ad7"
          },
          {
            external_uuid:
              "6afd414b1fd4813bc04bbfd5fc0985f2596804e432ce38f254e1d38b63fb3cf1",
            h1_person_id: 2330605,
            name: "Jeffrey M. Statland, MD",
            roleHistory: [
              {
                hash_or_id: "2330605",
                role: "Facility Contact",
                start_date: "2021-08-17",
                study_person_role_hash:
                  "2f4595f8cd0ac614a20fd3a8626238a6babd28666d6ab86866f164aa58fec146"
              }
            ],
            study_person_hash:
              "43072b13c0bcee3b1a46682f4105dee1cca88d120030e71df43270338688bd7b"
          },
          {
            external_uuid:
              "ebad1b70979476c74ecf2260d9b5405c78ae329dfcdfd0964c314004a25f87b6",
            name: "Sabrina Paganoni, MD, PhD",
            roleHistory: [
              {
                hash_or_id:
                  "8117af20e2a4dba2a9a1e4d18b2ca7e35e94a2295ad24623622868479f12c236",
                role: "Facility Contact",
                start_date: "2022-01-11",
                study_person_role_hash:
                  "8117af20e2a4dba2a9a1e4d18b2ca7e35e94a2295ad24623622868479f12c236"
              }
            ],
            study_person_hash:
              "25318f72a2c577eb78d7ccf9dd33af0513f95ea025eac10dc344c5317212522f"
          },
          {
            external_uuid:
              "9df280d7e167f0fc97b3ff43ea4ead87f247af1a335a998488dcc53e4bec7bdd",
            h1_person_id: 10359830,
            name: "Merrilee Needham",
            roleHistory: [
              {
                hash_or_id: "10359830",
                role: "Facility Contact",
                start_date: "2021-10-04",
                study_person_role_hash:
                  "f129a468d3d80528bb9559ef69ecb712aa1f9e319ccb2a8851c8251b77ece1fc"
              }
            ],
            study_person_hash:
              "e794168d7781742fe2cb9afa0fb6e4527c34628d8b9bfdd57d4fcb034bfb11ec"
          },
          {
            external_uuid:
              "e658e883024444414edb95853daeb8e4d65ba4b7a94c09ba1fbd6969f459ff2c",
            h1_person_id: 3701275,
            name: "Yuen T. So, MD, PhD",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id: "3701275",
                phone: "************",
                role: "Facility Contact",
                start_date: "2021-09-08",
                study_person_role_hash:
                  "a5b59dd688b800d5aa1b1be3f54c555a5eb76206ebb82ca2d1707995a1ef2a67"
              }
            ],
            study_person_hash:
              "07ffdcebd03366467d4ca91b286a082902f3b7fad78f19d20b9e799197675be5"
          },
          {
            external_uuid:
              "748bc7803b6e24a194a1bc4ed1d69fc43dda0b202f177ca1ee07326eb15fb214",
            name: "Cynthia Bodkin, MD",
            roleHistory: [
              {
                hash_or_id:
                  "a3d76e1cf6ff722159a550c4ce966b45a1891668bd679ee830cddf76458e41ef",
                role: "Facility Contact",
                start_date: "2022-01-28",
                study_person_role_hash:
                  "a3d76e1cf6ff722159a550c4ce966b45a1891668bd679ee830cddf76458e41ef"
              }
            ],
            study_person_hash:
              "c6d7ebfbc4afa4a889cd0f412841e79c5d32771b25a0497186e399e8be376a36"
          },
          {
            external_uuid:
              "24a2316d7083d28cec93806059c6e339aa79944d194b026014af54a5291b3235",
            name: "Richard Lewis, MD",
            roleHistory: [
              {
                hash_or_id:
                  "66fa3fb256d19f7dd73e07116582389d9da02ce43bb17377c2a9eac64e67cd75",
                role: "Facility Contact",
                start_date: "2022-01-28",
                study_person_role_hash:
                  "66fa3fb256d19f7dd73e07116582389d9da02ce43bb17377c2a9eac64e67cd75"
              }
            ],
            study_person_hash:
              "54b2ca0cb00c77b9e80e39e7ef4ebacb7dfa8aa83e054fbf8b4252e0b3673df7"
          },
          {
            external_uuid:
              "76f1844e1545b3bc0e257759c6ae7f1046bce51863eacb99aaa0ce9bcd3c264f",
            h1_person_id: 2268678,
            name: "Jeffrey D. Rothstein, MD, PhD",
            roleHistory: [
              {
                hash_or_id: "2268678",
                role: "Facility Contact",
                start_date: "2021-08-26",
                study_person_role_hash:
                  "76dc73ab1354146e580c84acda493b7b9f746c94fb91d1d1a597d277f40d6831"
              }
            ],
            study_person_hash:
              "5e4fb536465e5956e47f7bc4d091f4b88c3664fcb7463e43d5907c2550e2b14f"
          },
          {
            external_uuid:
              "b9514f63ec657d32bae37f7b661d17be5d460789ae836ea8824d22f319d46947",
            h1_person_id: 1400869,
            name: "Namita Goyal, MD",
            roleHistory: [
              {
                hash_or_id: "1400869",
                role: "Facility Contact",
                start_date: "2021-09-16",
                study_person_role_hash:
                  "77f92e46050772ff90d0ee2ab4c432074ea9bbdb887c22b1bab4509464924dd7"
              }
            ],
            study_person_hash:
              "8f28ac876c8338c447a6440d2876fb001eced76b9578bdb6f60258a9b2172230"
          },
          {
            external_uuid:
              "8a7488e23bd629f61ce9ca0f8ac79b0210158dc059508f770962f34789a650e7",
            name: "Markus Weber, MD",
            roleHistory: [
              {
                hash_or_id:
                  "5102f7c48f1d399b6b09c57737bb643f5c4266519a302a665e44d4e185e9e339",
                role: "Facility Contact",
                start_date: "2022-01-28",
                study_person_role_hash:
                  "5102f7c48f1d399b6b09c57737bb643f5c4266519a302a665e44d4e185e9e339"
              }
            ],
            study_person_hash:
              "9175e1500e67550204e5f53c6e34e474f379143818d64dc9aef2460cf2758ae5"
          },
          {
            external_uuid:
              "330c255634cee07c1fa8359884cea902acc014a4a607da803d1f6343b7ae071a",
            h1_person_id: 1622182,
            name: "Gary L. Pattee, MD",
            roleHistory: [
              {
                hash_or_id: "1622182",
                role: "Facility Contact",
                start_date: "2021-09-08",
                study_person_role_hash:
                  "c6f4df8cfa24e083273f3240a9bc9a41f982576d32ecb512df1fcd41d8f33cc1"
              }
            ],
            study_person_hash:
              "2ab26c6b3e1c4327f8599d33891f8f0e1ef7845289fd0c2142fc44ab63fff107"
          },
          {
            external_uuid:
              "ad7c0733fabf9f337a3c0866e765c614353bc3075c4e4087e50f75dc34e03358",
            h1_person_id: 3161287,
            name: "Bjorn E. Oskarsson, MD",
            roleHistory: [
              {
                hash_or_id: "3161287",
                role: "Facility Contact",
                start_date: "2021-10-12",
                study_person_role_hash:
                  "df78cd65e267c8ce45c757488fc4ad10fae6262b0510a9f55a9cde45639e210e"
              }
            ],
            study_person_hash:
              "ca9dff03fe380baeddbd5d453ce8c8cc316726826c2526af6dde6af0eb34283c"
          },
          {
            external_uuid:
              "3ba499fa1262f375e06be2453cdbaf9999e5cf50a05577481821b8de7d3384fc",
            h1_person_id: 1829842,
            name: "Terry Heiman-Patterson, MD",
            roleHistory: [
              {
                hash_or_id: "1829842",
                role: "Facility Contact",
                start_date: "2022-01-13",
                study_person_role_hash:
                  "7f4ef102766aeb428a9141e5fa321858e889e723d41aab50417f7c26099fbec9"
              }
            ],
            study_person_hash:
              "e7834f32a8118ae16610265a205bff939043cf1fcd64f628e10cbc18e44e0f05"
          },
          {
            external_uuid:
              "493af97d361035e9c45b82d58be5d71128b1774d9149dd319a5cb1a6499accf3",
            h1_person_id: 5366694,
            name: "John Turnbull",
            roleHistory: [
              {
                hash_or_id: "5366694",
                role: "Facility Contact",
                start_date: "2021-09-23",
                study_person_role_hash:
                  "20c740b2cfb6a856b8ad35e864d01e1a5aee69e2e4cf806ac282846c42498faa"
              }
            ],
            study_person_hash:
              "375268c78ca78df7f76fad00e65c4e724abdbe8cd6182c89205173de4a9c040c"
          },
          {
            external_uuid:
              "059f5234f65da8e8b7f60f5f713f33602b7206d3baead2c0f5ed28aa47dff657",
            name: "Jesus Mora",
            roleHistory: [
              {
                hash_or_id:
                  "3dc200761669fd79e83cc41a92781d22e23bd3873eb6d5e9829343ee659c73e0",
                role: "Facility Contact",
                start_date: "2022-01-13",
                study_person_role_hash:
                  "3dc200761669fd79e83cc41a92781d22e23bd3873eb6d5e9829343ee659c73e0"
              }
            ],
            study_person_hash:
              "216357843d60dd732a79afe0027b9b847503ea46061cadc5f7869c1d6b3d60b7"
          },
          {
            external_uuid:
              "6b604e0f0160ec67a0b0e386825384d34c33fe21bcf8811588846223be1eda3c",
            h1_person_id: 1039271,
            name: "Elham Bayat, MD",
            roleHistory: [
              {
                hash_or_id: "1039271",
                role: "Facility Contact",
                start_date: "2021-10-04",
                study_person_role_hash:
                  "0136a9e264e46ca3694eb6ad92ba578b874a9cc3a27af7d57490f57fe138464c"
              }
            ],
            study_person_hash:
              "710926e27833619735e616d56da2bd50d2f7d8ee6d40202c98c5caf5c36f49f4"
          },
          {
            external_uuid:
              "e22978139ffa590437a14dc47469595f4f4ea03863d54f0bd45c1770a93a84f1",
            h1_person_id: 7810000,
            name: "Kerri Schellenberg",
            roleHistory: [
              {
                hash_or_id: "7810000",
                role: "Facility Contact",
                start_date: "2021-08-31",
                study_person_role_hash:
                  "f679c9319dca03de0abf3851a24724232edbd0e7f046045511b49554b15ef69d"
              }
            ],
            study_person_hash:
              "3fb32dc19adbad5790b1415d3ae6c85a4bec4d2c7284a1291772c3c1ab4ec35a"
          },
          {
            external_uuid:
              "8c72091102e2a9f357f048e16aa4eb6d06516a3598cdbab0a97b3d417ae370ab",
            h1_person_id: 1527352,
            name: "Kourosh Rezania",
            roleHistory: [
              {
                hash_or_id: "1527352",
                role: "Facility Contact",
                start_date: "2021-08-26",
                study_person_role_hash:
                  "66eacb73aa1d81722f6efbc03064f7c901e634a92dc999a3c59f8d95dfa63132"
              }
            ],
            study_person_hash:
              "bbdf35123a35f692dc5cbc411429dcfc5d4d082edea01e351485a437618c16ac"
          },
          {
            external_uuid:
              "6198b4118ce5c2aa089a5cb69d5d31b0343b20543845dc08e85c78835ebe65e0",
            h1_person_id: 4910315,
            name: "Colleen O'Connell",
            roleHistory: [
              {
                hash_or_id: "4910315",
                role: "Facility Contact",
                start_date: "2021-08-17",
                study_person_role_hash:
                  "91567765cf85d0778557a01b5468fdfb79333549f6209216f4230f8f932694ea"
              }
            ],
            study_person_hash:
              "73aebe2c994f4033a7e99d6cdae3410f35d6af3bc677a4aa8de1c99cb7be0415"
          },
          {
            external_uuid:
              "620eba1c61ee5ab1988cd03b50546cf2450da04ccf07908aa6484b045f1345a1",
            name: "L.H. van den Berg, Dr.",
            roleHistory: [
              {
                hash_or_id:
                  "fc40a83176cd1076cd78a7440e502e1967825ac86fede9723749cde9f4131e43",
                role: "Facility Contact",
                start_date: "2021-12-20",
                study_person_role_hash:
                  "fc40a83176cd1076cd78a7440e502e1967825ac86fede9723749cde9f4131e43"
              }
            ],
            study_person_hash:
              "c3889dc9bcea929add4dfb175980bb9465411fdbfe37b05dbac85b274f3eb091"
          },
          {
            external_uuid:
              "cd1f601e4277e0f1aaa5ee0951e21a45d4e855a25482523915e27857a4a6f93b",
            h1_person_id: 5397935,
            name: "Lawrence Korngut",
            roleHistory: [
              {
                hash_or_id: "5397935",
                role: "Facility Contact",
                start_date: "2021-08-17",
                study_person_role_hash:
                  "4ccb6c711b29d8744c29cf808c4cce880d7f5513d6bd411cc89364111a339755"
              }
            ],
            study_person_hash:
              "c58e126dc4ad3d0591ce80e317ae488d1577a6da6106b5aacfb1a9668aae22e4"
          },
          {
            external_uuid:
              "ff03883a6f2fc2083e213e10d0978dff6afe563a70a296540b15de39cad9f2a6",
            h1_person_id: 297865,
            name: "Amanda C. Peltier, MD",
            roleHistory: [
              {
                hash_or_id: "297865",
                role: "Facility Contact",
                start_date: "2022-01-11",
                study_person_role_hash:
                  "c3ad82bcd684d806f260337338e8b03b212d951978a02c42e3101dc1eb0f9240"
              }
            ],
            study_person_hash:
              "c6c1a488138022bec731c9026dfa61347f5726af9fd5bdaf09c2c0c79b6981a0"
          },
          {
            external_uuid:
              "133614d2245d94bcf6e87391fbd6786241269d8c9437d34ff08134edbf6250df",
            h1_person_id: 5153671,
            name: "Christen Shoesmith",
            roleHistory: [
              {
                hash_or_id: "5153671",
                role: "Facility Contact",
                start_date: "2021-11-16",
                study_person_role_hash:
                  "a55d8dd5a1d4232c74852f385804f13bb07ddf9ce912dcb4cf0efecad4e0ec9a"
              }
            ],
            study_person_hash:
              "00731369f8ad6f7cc3b75432dd67df385eff933a2409524fb8e5cd8558de0ee2"
          },
          {
            external_uuid:
              "345bccd72d2e3620dc24fb9242a29a191436d5718ab3c3bd91281e69949100da",
            name: "Ariel Breiner",
            roleHistory: [
              {
                hash_or_id:
                  "631c9100a817fcc32b520afe139583a5bc0d2ab4e61993c7efc631110f31e1cf",
                role: "Facility Contact",
                start_date: "2021-09-14",
                study_person_role_hash:
                  "631c9100a817fcc32b520afe139583a5bc0d2ab4e61993c7efc631110f31e1cf"
              }
            ],
            study_person_hash:
              "c612916ecb360c2fea089330dd2ef6eef34179d40f95aeddd9fce2e6f41c30c4"
          },
          {
            external_uuid:
              "98a1bcdb75b0eda0fb9ac804dcd205eebffd3a9e2f153da65a99e82c52ddbb44",
            h1_person_id: 10024279,
            name: "Orla Hardiman",
            roleHistory: [
              {
                hash_or_id: "10024279",
                role: "Facility Contact",
                start_date: "2021-12-20",
                study_person_role_hash:
                  "bbb7abe57536bf952057067a190fb6c63101539c71f97441c3b0b95790e371ea"
              }
            ],
            study_person_hash:
              "8e1a381385e97fc53f280915597ea0cbb457bb17f905105e984697625a6cb6f0"
          },
          {
            external_uuid:
              "8006d2a9f9a34c2257ec9b3a8103dd6e749ed3f88b6a7f7a3b5c2edaf376f816",
            h1_person_id: 2818574,
            name: "Genevieve Matte",
            roleHistory: [
              {
                hash_or_id: "2818574",
                role: "Facility Contact",
                start_date: "2021-10-21",
                study_person_role_hash:
                  "d558f8c96f9bf3267f9c779e413a73d4d776ad34780049a25297ad532c86b213"
              }
            ],
            study_person_hash:
              "97cb6de15190be3c10473e3a3d4d04764517447d38fba7ceaa0d379204c5d8b9"
          },
          {
            external_uuid:
              "bbc4ec36db52dfdc59905ba830700a25259c206a334dcd487bb1b1df2bbdf3fc",
            h1_person_id: 3760285,
            name: "Leo McCluskey",
            roleHistory: [
              {
                hash_or_id: "3760285",
                role: "Facility Contact",
                start_date: "2021-09-28",
                study_person_role_hash:
                  "2e5a840c5684b76425a732eec8724589069206d3c59a8b71b9a809896762c8fc"
              }
            ],
            study_person_hash:
              "96026177723305acdf7c1df2f930493289c996078308e3f2063b4be60976993f"
          },
          {
            external_uuid:
              "fdaab2ddd3e7127e8fc666942ba84e7fdeccbe4071a5ddc1986714950ec6dff2",
            name: "Avery St. Sauveur",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "003de854732b5a9a2ced3ea659371bc0dc1bf3b58bcd8a46d39c1ca9597b551d",
                role: "Facility Contact",
                start_date: "2022-02-03",
                study_person_role_hash:
                  "003de854732b5a9a2ced3ea659371bc0dc1bf3b58bcd8a46d39c1ca9597b551d"
              }
            ],
            study_person_hash:
              "606338a919eda9147cb73b255e82b01d844b62edcbc830441a4e01d5cb422c65"
          },
          {
            external_uuid:
              "eaa7a73980279feee18a3305152df56854380ded8b43e3f934bc1a045b75831b",
            h1_person_id: 2683656,
            name: "Tuan H. Vu, MD",
            roleHistory: [
              {
                hash_or_id: "2683656",
                role: "Facility Contact",
                start_date: "2021-08-19",
                study_person_role_hash:
                  "b6e14ab8cc78defcc81280560a3570ecfcbd19121fd1566b748c5c3c5a681771"
              }
            ],
            study_person_hash:
              "1bd44b2165e5ee138bbbe1c9d003e4bdaaef0b45121a3118983bbca08a7a69b0"
          },
          {
            external_uuid:
              "34cf0b13c827115574bc76b6f8ad814858e5b3bfa91d7d7dc9089ec81e28b32a",
            h1_person_id: 2988988,
            name: "Jinsy Andrews, MD, MSc",
            roleHistory: [
              {
                hash_or_id: "2988988",
                role: "Facility Contact",
                start_date: "2021-10-21",
                study_person_role_hash:
                  "c513420d9d237fb7533563b0e7aa75e5d569181d78d870862e7309cdf7e0d34b"
              }
            ],
            study_person_hash:
              "de4ca8971c98461de192eebb91abc225916b4c3de855ddb27810a475b33edbf7"
          },
          {
            external_uuid:
              "7c6dd223bd8084191fb3d6915311f373798f9cf923e2bdbd8f2ac129fa40a3ef",
            h1_person_id: 3440368,
            name: "Michael Pulley, MD, PhD",
            roleHistory: [
              {
                hash_or_id: "3440368",
                role: "Facility Contact",
                start_date: "2021-10-07",
                study_person_role_hash:
                  "02de117e491f6015a59498ff921a6f19569c7ae4395554ad6707135cd034c201"
              }
            ],
            study_person_hash:
              "775f448dac4454cff53c5fcf101315a1f5c0ffccfda34a4b9c2ddca014408679"
          },
          {
            external_uuid:
              "6a3057600e95b2e2f910f537751903469b2a1282fa1340717a8985571592e19a",
            h1_person_id: 3038274,
            name: "Stephen A. Goutman, MD, MS",
            roleHistory: [
              {
                hash_or_id: "3038274",
                role: "Facility Contact",
                start_date: "2021-12-27",
                study_person_role_hash:
                  "bb946679d13445247b24bd2d8f8ac46d904e65c3d5d147b528ede6fbf9770a60"
              }
            ],
            study_person_hash:
              "60896dce2fc6b9911b76caa75e890e3bda0540efc19410c47e0c1df5c7fa8e24"
          },
          {
            external_uuid:
              "4cd0e6766b3e78a5c23a6a11b14c3d35cf70d09a57899faac54991c543d94a82",
            name: "Jonathan Katz, MD",
            roleHistory: [
              {
                hash_or_id:
                  "e75216a697376cb41767bb877333d049208f1bf0a4d64c4f2934000f114b3308",
                role: "Facility Contact",
                start_date: "2021-10-12",
                study_person_role_hash:
                  "e75216a697376cb41767bb877333d049208f1bf0a4d64c4f2934000f114b3308"
              }
            ],
            study_person_hash:
              "e3f69c12de58fc3e3c65bf9a4fa0ae8878259e31670956891a22a7b70a85a728"
          },
          {
            external_uuid:
              "d6cd93d67c3626109fcc23747730b6fe760ba34cdad6a5f50f610382d51a3ccc",
            h1_person_id: 5374485,
            name: "Annie Dionne",
            roleHistory: [
              {
                hash_or_id: "5374485",
                role: "Facility Contact",
                start_date: "2021-08-17",
                study_person_role_hash:
                  "f88c80ffa2a5e6c06e0f505bece6ecfd1f5f30996d09684a63802be20421e524"
              }
            ],
            study_person_hash:
              "4a5ae2aebe0af298d3898809205da3b6eefd954901ae90e6e980cd9d8fb11549"
          },
          {
            external_uuid:
              "d26ad665a9e96c0abfa33923ebdc36d97435eb87351d578acb622a3a5793900f",
            h1_person_id: 3610327,
            name: "Dianna Quan, MD",
            roleHistory: [
              {
                hash_or_id: "3610327",
                role: "Facility Contact",
                start_date: "2021-09-16",
                study_person_role_hash:
                  "8758175000675f8f4ff3362f303b8a780de9c74e9cc82f47eee98077dc1b28c6"
              }
            ],
            study_person_hash:
              "7649b2894edbda46bee0acf504bd9d0dd06482f13a0523a638289667e1bf43d9"
          },
          {
            external_uuid:
              "0a965ab4f4f77f0a12f640c53c369e4b4160a28a30cf2f3ed306f6e1edbdf05b",
            h1_person_id: 253041,
            name: "Rup Tandan, MD",
            roleHistory: [
              {
                hash_or_id: "253041",
                role: "Facility Contact",
                start_date: "2021-08-26",
                study_person_role_hash:
                  "73e739982805e74575c49abfe133c5145a38fdad9ede90551e9dd32d258fd912"
              }
            ],
            study_person_hash:
              "18d713af98c72d6ac3e61a3c5d908b1e26c084d4dc20a0928ded209a557a618b"
          },
          {
            external_uuid:
              "c027420f5514fbfc490e7a07e22a3b0d1149562f03d1eb9ee9c1fb59d1552adc",
            name: "Philip Van Damme",
            roleHistory: [
              {
                hash_or_id:
                  "8a66cdf6a295979f9e4cac991d19c9574d22cefaecbe6febf8f604e411012597",
                role: "Facility Contact",
                start_date: "2022-01-24",
                study_person_role_hash:
                  "8a66cdf6a295979f9e4cac991d19c9574d22cefaecbe6febf8f604e411012597"
              }
            ],
            study_person_hash:
              "bfd47128758f6fe89e1f32f443c330491619821ceb7417c88596867b3a46d1bd"
          },
          {
            external_uuid:
              "904b116f4b9023a59d40e621dd387ee3adb2311060d5f83d8c9bcd1ce7321dcb",
            h1_person_id: 3231705,
            name: "Dominic Fee, MD",
            roleHistory: [
              {
                hash_or_id: "3231705",
                role: "Facility Contact",
                start_date: "2021-10-04",
                study_person_role_hash:
                  "9093ea8374146250c59f62dea51ee30238ede00d79e211e500b816a43d6b3a1b"
              }
            ],
            study_person_hash:
              "fd0bb0a3745f0ab910828acd938753e0084824b755e3c1b225c374b7acc5dfdf"
          },
          {
            external_uuid:
              "75ad46267c499dd06653fad2827fd0942073bc0a862a9a2a31986085d5b54b84",
            h1_person_id: 1946052,
            name: "Margaret A. Owegi, DO",
            roleHistory: [
              {
                hash_or_id: "1946052",
                role: "Facility Contact",
                start_date: "2021-12-08",
                study_person_role_hash:
                  "61fdd86e32d1cbcd93cea69d8fd8374ab2715042a527bfe26812d19b52662f7a"
              }
            ],
            study_person_hash:
              "9f580025c62e2a8dc99d353367ec50ef8acaf379cc1f63c07cb88d017c466e5a"
          }
        ],
        personIds: [
          "670639",
          "3275896",
          "7817212",
          "112023",
          "5184419",
          "1097144",
          "2043661",
          "61300",
          "372304",
          "1677852",
          "5161832",
          "2330605",
          "10359830",
          "3701275",
          "2268678",
          "1400869",
          "1622182",
          "3161287",
          "1829842",
          "5366694",
          "1039271",
          "7810000",
          "1527352",
          "4910315",
          "5397935",
          "297865",
          "5153671",
          "10024279",
          "2818574",
          "3760285",
          "2683656",
          "2988988",
          "3440368",
          "3038274",
          "5374485",
          "3610327",
          "253041",
          "3231705",
          "1946052"
        ],
        primaryCompletionDate: 1703980800000,
        primaryPurpose: ["Treatment"],
        sitesCount: 51,
        sponsors: ["Cytokinetics"],
        startDate: 1629072000000,
        status: "Recruiting",
        studyCompletionDate: 1711843200000,
        studyPhase: "Phase 3",
        studyType: "Interventional",
        trialDuration: "2 yrs 7 mos"
      }
    ],
    total: 1
  },
  {
    from: 0,
    pageSize: 1,
    results: [
      {
        acronym: "VOTER",
        allocation: [],
        avgPatientsPerSites: Infinity,
        briefTitle:
          "Vertebral Artery Origin Treatment Via Endovascular Techniques Registry (VOTER)",
        conditions: ["Vertebral Artery Stenosis"],
        contactNames: [
          "Nancy Ebreo, CCRC,RRT,CRA",
          "Makenzie Woodford, BS",
          "Konrad Bach, MD",
          "Dhruvil Pandya, MD",
          "Andre Guthrie, BS, CCRP",
          "Kristine Below",
          "Waldo Guerrero, MD",
          "Benjamin Cummins, MPH/MPA",
          "Kara Christopher, MS, MPH, PhD",
          "Lauren Todd",
          "Israel Alba",
          "Amy Denardo",
          "Santiago Ortega, MD, MS",
          "Amanda Nolte",
          "Randall Edgell, MD",
          "Andre Guthrie, BS",
          "Kara Christopher, PhD"
        ],
        eligibilityMinAge: ["18 Years"],
        enrollment: 100,
        enrollmentType: "Anticipated",
        estimatedEndDate: 1685491200000,
        facilityCity: [],
        facilityCountry: [],
        facilityName: [],
        facilityState: [],
        h1Id: "CT-29-4eda9e53-2466-482e-b8b2-7975271588a9",
        id: 0,
        interventionModel: [],
        interventionOtherNames: ["Observational"],
        interventionTypes: ["Other"],
        interventions: ["Observational Prospective Registry"],
        investigators: [
          "Vikas Gupta, MD",
          "Amer Alsheklee, MD",
          "Randall Edgell, MD",
          "Dhruvil Pandya, MD",
          "Osama Zaidat, MD",
          "Santy Ortega-Gutierrez, MD",
          "Waldo Guerrero, MD",
          "Nima Aghaebrahim, MD",
          "Gabor Toth, MD",
          "Lucas Elijovich, MD"
        ],
        lastUpdatedAt: 1633651200000,
        masking: [],
        nctId: "NCT03999983",
        patientsPerMonths: 1.85,
        patientsPerSitesPerMonth: Infinity,
        people: [
          {
            external_uuid:
              "3a0615bdee616810a840ae5329505eb9e6bfb6faa6da38b70920f0875793adca",
            name: "Nancy Ebreo, CCRC,RRT,CRA",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "441e417121d8dde3dff6ca6ef232a89cab9b84ab6d32d69ad6db7435c03fdce1",
                role: "Facility Contact",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "441e417121d8dde3dff6ca6ef232a89cab9b84ab6d32d69ad6db7435c03fdce1"
              }
            ],
            study_person_hash:
              "d36559765e5976f0700fe048d593a868bd23e5b816348e9d3c1ca32485f023dc"
          },
          {
            external_uuid:
              "6804c84dc455670cbc272afcae16ac0c8ce380531f62179d9a93b8af008860dd",
            name: "Vikas Gupta, MD",
            roleHistory: [
              {
                hash_or_id:
                  "a896db5d8ea7d8961fa51a38e0957f4fbc3e2201be6079f361530d292f0aa1e7",
                role: "Principal Investigator",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "a896db5d8ea7d8961fa51a38e0957f4fbc3e2201be6079f361530d292f0aa1e7"
              }
            ],
            study_person_hash:
              "62a6d00b1e1fd3785f883015e2b2b21c1176a76af04b8f0eb40686220b73e552"
          },
          {
            external_uuid:
              "155efbdac81bd64d649c8adadf70cfa1ae37cbba62cc39610091e5159af124fd",
            name: "Amer Alsheklee, MD",
            roleHistory: [
              {
                hash_or_id:
                  "45d14e33ee590a6b9aae56f0449bccba87c30db1b735fec9d7c4ddb47324145a",
                role: "Principal Investigator",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "45d14e33ee590a6b9aae56f0449bccba87c30db1b735fec9d7c4ddb47324145a"
              }
            ],
            study_person_hash:
              "687edd907ed2681d1e12461f8ebd97245fea3627e165e8c5f6ac934a4f919ec3"
          },
          {
            external_uuid:
              "eed1973b07ad7d0b1108f77aff31c692512b1ad9d3fb743e6431646211c2536e",
            name: "Makenzie Woodford, BS",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "cba9584bdb06f0e415a48b942465bd98ec4b532de7e2e75594d874b6dc36d3db",
                phone: "************",
                role: "Facility Contact",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "cba9584bdb06f0e415a48b942465bd98ec4b532de7e2e75594d874b6dc36d3db"
              }
            ],
            study_person_hash:
              "50ce3432f3ea90989edd280fafc6bc3e4a1dd06d5a5321dfdf9a6d9afe55ebbe"
          },
          {
            external_uuid:
              "89470a45db6d9cb5acd6477678d10e6e74d204f1bacea2c35dba84cea3d1152e",
            name: "Konrad Bach, MD",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "c3091dee104f29894a004a539d902dfb6a13675195c9b3bc36e0783aad17e072",
                role: "Facility Contact",
                start_date: "2020-11-27",
                study_person_role_hash:
                  "c3091dee104f29894a004a539d902dfb6a13675195c9b3bc36e0783aad17e072"
              }
            ],
            study_person_hash:
              "587f00676a4584186783f93491eee96961dda7afb35caa46ee0a498fb09aecda"
          },
          {
            external_uuid:
              "030a3b6c896634edfddb3a1e1de3ff93186aaf7e14c705489c3162457a77dead",
            name: "Randall Edgell, MD",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "0fc8172d42a892d8023a41f23413c288cb4b359981fc5bbf51a639554899c207",
                phone: "************",
                role: "Central Contact",
                start_date: "2019-06-27",
                study_person_role_hash:
                  "0fc8172d42a892d8023a41f23413c288cb4b359981fc5bbf51a639554899c207"
              },
              {
                affiliation: "St. Louis University",
                hash_or_id:
                  "6d8ac189c9446057f8c0559974e2f0d85b2f28a1b3f0679d7ea4b4f04a8440b0",
                role: "Principal Investigator",
                start_date: "2019-12-13",
                study_person_role_hash:
                  "6d8ac189c9446057f8c0559974e2f0d85b2f28a1b3f0679d7ea4b4f04a8440b0"
              }
            ],
            study_person_hash:
              "8f0a4fb7f36013165220b31198257ef267482edbc56ef03da77ffec053e51187"
          },
          {
            external_uuid:
              "ece3dbe69f9c3ac9bb1c7f87c86aaff0936b4ec2edece57f1783f8c3d5039c12",
            name: "Dhruvil Pandya, MD",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "e13d0210c24217421b1f3303a18e2ba574ff1ed1f622295cfc6096000d4a8404",
                role: "Facility Contact",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "e13d0210c24217421b1f3303a18e2ba574ff1ed1f622295cfc6096000d4a8404"
              },
              {
                hash_or_id:
                  "c71a3723c6406072fd8acdb50397e51414c005c560a86b493299920644a72542",
                role: "Principal Investigator",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "c71a3723c6406072fd8acdb50397e51414c005c560a86b493299920644a72542"
              }
            ],
            study_person_hash:
              "59bd01932541af138266f9f67fd4cf0dae5448aa7ce70e5a1953ffec36b27ab4"
          },
          {
            external_uuid:
              "d6f31009df65ceecb6fdc810aa5a57dabc98dad3a64289a4e685175f3e64a691",
            name: "Osama Zaidat, MD",
            roleHistory: [
              {
                hash_or_id:
                  "1a450da517fbd539135246a38d1977e06c16dfebd27d1af399d263c9737dbc60",
                role: "Principal Investigator",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "1a450da517fbd539135246a38d1977e06c16dfebd27d1af399d263c9737dbc60"
              }
            ],
            study_person_hash:
              "8b184b60e6e02bd9494b89f560d12d63de139627cb239287f1dc2890f84717f6"
          },
          {
            external_uuid:
              "79d2dfc9f36c29f033d0f608b7714dc044bcd10dda994fa98ce25e92d443cb8a",
            name: "Andre Guthrie, BS",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "8f475e85a7a47e2169ac13a8153a3e7e2e342782817691fff11c7c23e4d0d880",
                phone: "************",
                role: "Central Contact",
                start_date: "2019-06-27",
                study_person_role_hash:
                  "8f475e85a7a47e2169ac13a8153a3e7e2e342782817691fff11c7c23e4d0d880"
              }
            ],
            study_person_hash:
              "616bb5a14283e1119fd63be5e052642cb675696925dd6b44ffe3908286e45850"
          },
          {
            external_uuid:
              "17b1f4151afcd94721bad32573e41f5cdd583ff3c80cbc8acb23e0dca73cec77",
            name: "Andre Guthrie, BS, CCRP",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "74eb41320241a21c10fd976d7b96ea9a5ab2fdb1cf6ec2fd416f22cd5ff646d0",
                phone: "************",
                role: "Facility Contact",
                start_date: "2019-12-13",
                study_person_role_hash:
                  "74eb41320241a21c10fd976d7b96ea9a5ab2fdb1cf6ec2fd416f22cd5ff646d0"
              }
            ],
            study_person_hash:
              "289c24c0a0bccf4e6c8c275d90fef74ac682e9ee22bad4210dcad9bc3f0e5328"
          },
          {
            external_uuid:
              "c30fc902a0985f5c989dde17856a42c83b90d6071553c6b511b19aa43987c72e",
            name: "Kristine Below",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "426670453e200e65fa59331571ea5a0dc556ade721f00315a83b39684737f857",
                role: "Facility Contact",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "426670453e200e65fa59331571ea5a0dc556ade721f00315a83b39684737f857"
              }
            ],
            study_person_hash:
              "a76e7ef64ff95a8ba6d5ca15d71fe2260c5df96f16ff58bc4075f9f49cdd4cc3"
          },
          {
            external_uuid:
              "8cdaf17f3cd590d06501a2cdb6d98c2c553180ae51c9bd1072ffa4b6b9a17376",
            name: "Santy Ortega-Gutierrez, MD",
            roleHistory: [
              {
                hash_or_id:
                  "b149a7e97c3e21b97bf99a07fe93b4438a4b473628fafa2b81a09a0725859aff",
                role: "Principal Investigator",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "b149a7e97c3e21b97bf99a07fe93b4438a4b473628fafa2b81a09a0725859aff"
              }
            ],
            study_person_hash:
              "0597d4bc9373e21262a45b174d14e57349894cf3577f47a8bed6f3cd6c8035d5"
          },
          {
            external_uuid:
              "27875cdadeceddd17502e88a52d06a3f8dad7997043d45f225d1c2600932dd68",
            name: "Waldo Guerrero, MD",
            roleHistory: [
              {
                hash_or_id:
                  "98b92d4291040dc69c8a3f13a9b0a16d85e09ae8cd33a90d2c5c78648fdce46a",
                role: "Principal Investigator",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "98b92d4291040dc69c8a3f13a9b0a16d85e09ae8cd33a90d2c5c78648fdce46a"
              },
              {
                email: "<EMAIL>",
                hash_or_id:
                  "ea8e6f07af59e58b7e9d268caa5e0838431dba9e25c16471a51b96d2cc17cfea",
                role: "Facility Contact",
                start_date: "2020-11-27",
                study_person_role_hash:
                  "ea8e6f07af59e58b7e9d268caa5e0838431dba9e25c16471a51b96d2cc17cfea"
              }
            ],
            study_person_hash:
              "303988d1f1aa733a2e4b54baa3929064229d14997a59d32b9f0d7c0277a7466d"
          },
          {
            external_uuid:
              "8f2ed68404eb3478ab8fb9b7f0b7ba87c930864f13c680f2c33c0e68cd3bed24",
            name: "Benjamin Cummins, MPH/MPA",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "b40ffdad06a2651506b06393f96cbf6893b3a7aa1d74660ac3c3b9d7fbff9bdc",
                phone: "************",
                role: "Facility Contact",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "b40ffdad06a2651506b06393f96cbf6893b3a7aa1d74660ac3c3b9d7fbff9bdc"
              }
            ],
            study_person_hash:
              "dae3e093dd768310e1afea078bf144792b012ecb0a9ec7f32e0e451613212944"
          },
          {
            external_uuid:
              "af859045d8b73be433970558587639a87f586334051aa89e1ff7fe8de2fc864d",
            name: "Kara Christopher, MS, MPH, PhD",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "15d6bcfd9028e1b7b451fdd5b3920b521e86473bd898fd33d63653eaeb96c506",
                phone: "************",
                role: "Facility Contact",
                start_date: "2019-12-13",
                study_person_role_hash:
                  "15d6bcfd9028e1b7b451fdd5b3920b521e86473bd898fd33d63653eaeb96c506"
              }
            ],
            study_person_hash:
              "19d42a7cd8762286d6863615e2e8e6fbcc0dd4b8db2c61453f536d78dca1e13e"
          },
          {
            external_uuid:
              "a3b4cb8da97f37b3d789f526f9ffa6a21775efb8791f50ac8cefaef2e7fef499",
            name: "Lauren Todd",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "93c451532b413b7fea9016b6807563b23bf13015a801ad0db89f1ba86afd8b02",
                phone: "************",
                role: "Facility Contact",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "93c451532b413b7fea9016b6807563b23bf13015a801ad0db89f1ba86afd8b02"
              }
            ],
            study_person_hash:
              "449714921921a0f5cf1f467445ee7e95755cf6719cce8c2115a074e42f0c8a9c"
          },
          {
            external_uuid:
              "3b437435c0a85c278b8028d5f12fc82b8276efd9d8830b9a1d3da031a408c5f7",
            name: "Israel Alba",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "a6a0c47def7da6f39d7c3af9ce2e5bb43c95e4a4e693fa837921fd9677ab7ca0",
                role: "Facility Contact",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "a6a0c47def7da6f39d7c3af9ce2e5bb43c95e4a4e693fa837921fd9677ab7ca0"
              }
            ],
            study_person_hash:
              "28c5bef4fc6c24841bd31fad255ab6fc8d4799cd66e2988192c89e8fbfdbf8f1"
          },
          {
            external_uuid:
              "d589120075bc997a153ff5731b7d9e237d84a2c8c153c065853419edddc07f53",
            name: "Kara Christopher, PhD",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "cc9c78ed18247845ab91b43aaf39c9453542946bc8bebcaee644d7d3802a03d0",
                phone: "************",
                role: "Central Contact",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "cc9c78ed18247845ab91b43aaf39c9453542946bc8bebcaee644d7d3802a03d0"
              }
            ],
            study_person_hash:
              "a69ee1f009980bb81c69dd082ac0dc8e120c460b77c773cc33768de6aef739b8"
          },
          {
            external_uuid:
              "4524a4ef3eafe562e2bcce5630fff27ffac4b856005fc114fee0916fe703da5f",
            name: "Amy Denardo",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "81117b61b251aee456a2501056a589a370323f2c4caedad6e1f22f4c978e724b",
                role: "Facility Contact",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "81117b61b251aee456a2501056a589a370323f2c4caedad6e1f22f4c978e724b"
              }
            ],
            study_person_hash:
              "ad7c68060343c91d947b6491b828c6587fc51886aa1049d8c3106e57967a8513"
          },
          {
            external_uuid:
              "b92e9677a46b8c200aed1069e9b6f42b5bdd6484505fba1dd8a9da777f89d8ab",
            name: "Nima Aghaebrahim, MD",
            roleHistory: [
              {
                hash_or_id:
                  "09a3a43b420e6c4cac400efabe6e104bff4fd2b3ad146e8582b35307facaa4d9",
                role: "Principal Investigator",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "09a3a43b420e6c4cac400efabe6e104bff4fd2b3ad146e8582b35307facaa4d9"
              }
            ],
            study_person_hash:
              "431622915474b432ae2b8d6c30f2e5cac72f1116824909d18fdcd79d49b99133"
          },
          {
            external_uuid:
              "8292533c36f2afb2d410cb6b75b7e3876772cf10cfc502942b2abb43459aebbb",
            name: "Gabor Toth, MD",
            roleHistory: [
              {
                hash_or_id:
                  "45d9a1dd409f1385fbdc3d0a3a5e4377996271f7770ea09a2ec7fc845451c123",
                role: "Principal Investigator",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "45d9a1dd409f1385fbdc3d0a3a5e4377996271f7770ea09a2ec7fc845451c123"
              }
            ],
            study_person_hash:
              "cdb37856384aa3121cc672fe29bf7897e472d13b668a7d6ed8436bb11dd493f9"
          },
          {
            external_uuid:
              "d4070c71dd2005f12de431e9b486a9693b1c4d26b1d2eba0fc437bc08617a84c",
            name: "Randall C. Edgell, MD, FSVIN",
            roleHistory: [
              {
                hash_or_id:
                  "22b3ef115298f286ba91010e35172d51956211c9787dba9d8888d478e1bcded8",
                role: "Responsible Party",
                start_date: "2019-06-27",
                study_person_role_hash:
                  "22b3ef115298f286ba91010e35172d51956211c9787dba9d8888d478e1bcded8"
              }
            ],
            study_person_hash:
              "2cec178fa0952fb123f701393572e83ec209ae3c03531489ef6c3d45dc8f43ee"
          },
          {
            external_uuid:
              "ba4430276b76b5302df4a6bcc21beb5cdc5d24a20993385964538d6a7c376872",
            name: "Santiago Ortega, MD, MS",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "65934c5c64e90dc02b14bb2b269d9e324fe3bb088daacae43ca9ab4140817ae2",
                role: "Facility Contact",
                start_date: "2020-11-27",
                study_person_role_hash:
                  "65934c5c64e90dc02b14bb2b269d9e324fe3bb088daacae43ca9ab4140817ae2"
              }
            ],
            study_person_hash:
              "8678ddc7ddbed1e7e313cbffd92deaf09ef7fea36bd2802f5fe620e743a12fe8"
          },
          {
            external_uuid:
              "0bca6702521a2a29900532a2244e90c4f930a30bf917fa00408da970b5fd0e47",
            name: "Lucas Elijovich, MD",
            roleHistory: [
              {
                hash_or_id:
                  "8b9a886b158cc2c9a9d7ccb466f0f1616ae99280408145ed6d4f4a96d01a2b5f",
                role: "Principal Investigator",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "8b9a886b158cc2c9a9d7ccb466f0f1616ae99280408145ed6d4f4a96d01a2b5f"
              }
            ],
            study_person_hash:
              "04697b2929b6fc8ef81263c735f53f6b160fbcfa478d5b9351e7c9db965503f8"
          },
          {
            external_uuid:
              "321d6eab8172402b5ce3eeb6dd2acbec83fd4099c50f04943d8ea66b6b22d678",
            name: "Amanda Nolte",
            roleHistory: [
              {
                email: "<EMAIL>",
                hash_or_id:
                  "4fc7440c4f396dfdb46bdb627d2a1981c3e61ecf74641a16c2ba632518add0a9",
                role: "Facility Contact",
                start_date: "2021-10-08",
                study_person_role_hash:
                  "4fc7440c4f396dfdb46bdb627d2a1981c3e61ecf74641a16c2ba632518add0a9"
              }
            ],
            study_person_hash:
              "05deba56d7caa6cb957f30e69174ec409d5304c586bf2d2e1e9cf76eea5aed09"
          }
        ],
        personIds: [],
        primaryCompletionDate: 1685491200000,
        primaryPurpose: [],
        sitesCount: 0,
        sponsors: [],
        startDate: 1574121600000,
        status: "Recruiting",
        studyCompletionDate: 1717113600000,
        studyType: "Observational",
        trialDuration: "4 yrs 6 mos"
      }
    ],
    total: 1
  }
];

const expectedResultForFixtureV4 = {
  from: 0,
  pageSize: 1,
  results: [
    {
      allocation: ["Non-Randomized"],
      avgPatientsPerSites: Infinity,
      briefTitle:
        "Safety and Preliminary Efficacy of BNA035 in Patients With Advanced Solid Tumors",
      conditions: ["Amyotrophic Lateral Sclerosis"],
      contactNames: ["Thomas Seidl, PhD", "William K. Ward, MD"],
      contacts: [
        { name: "Thomas Seidl, PhD", isActive: true },
        { name: "William K. Ward, MD", isActive: true }
      ],
      eligibilityMinAge: ["18 Years"],
      estimatedEnrollment: {
        earliest: {
          effective_date: "2021-06-18",
          enrollment: "654",
          enrollment_type: "Anticipated"
        },
        latest: {
          effective_date: "2021-06-18",
          enrollment: "654",
          enrollment_type: "Anticipated"
        }
      },
      actualEnrollment: {
        effective_date: "2024-06-04",
        enrollment: "687",
        enrollment_type: "Actual"
      },
      enrollmentHistory: [
        {
          effective_date: "2023-09-13",
          enrollment: "687",
          enrollment_type: "Actual"
        },
        {
          effective_date: "2021-06-18",
          enrollment: "654",
          enrollment_type: "Anticipated"
        },
        {
          effective_date: "2023-05-10",
          enrollment: "688",
          enrollment_type: "Actual"
        },
        {
          effective_date: "2024-06-04",
          enrollment: "687",
          enrollment_type: "Actual"
        }
      ],
      enrollment: 48,
      enrollmentType: "Anticipated",
      estimatedEndDate: 1701302400000,
      facilityCity: [],
      facilityCountry: [],
      facilityName: [],
      facilityState: [],
      h1Id: "CT-29-3c9172f8-26d9-494f-8b4a-cad5b716bf2f",
      id: 0,
      interventionModel: ["Sequential Assignment"],
      interventionOtherNames: [],
      interventionTypes: ["Drug"],
      interventions: ["BNA035"],
      investigators: [],
      lastUpdatedAt: 1639008000000,
      masking: ["None (Open Label)"],
      nctId: "NCT05150457",
      patientsPerMonths: 1.07,
      patientsPerSitesPerMonth: undefined,
      people: [
        {
          collection_source_id: 29,
          external_uuid:
            "de1d5b50bd5090baf812d602f297ad8b38e350766d8318d6a41defbb8572fc21",
          name: "William K. Ward, MD",
          roleHistory: [
            {
              affiliation: "Pacific Diabetes Technologies",
              collection_source_id: 29,
              hash_or_id:
                "defabc2e507b013adc00f48eab4dc0a666896ceb59cfe6eb5f823d9403ce9477",
              role: "Study Director",
              start_date: "2022-01-11",
              study_person_role_hash:
                "defabc2e507b013adc00f48eab4dc0a666896ceb59cfe6eb5f823d9403ce9477"
            }
          ],
          study_person_hash:
            "4489b158c7149ac1b81c7866378d7e9b8277b47f73cb45bff479a647fd6a782a"
        },
        {
          collection_source_id: 29,
          external_uuid:
            "3c8fb8f52b0991838abd26fe8cf0a3684aebd051503380fbde917d442b44869b",
          name: "Thomas Seidl, PhD",
          roleHistory: [
            {
              collection_source_id: 29,
              email: "<EMAIL>",
              hash_or_id:
                "df1bdcf0bccf1268eceb4261adb020472ba0c873ece182660eb2a87cb89825f5",
              phone: "9713405601",
              role: "Central Contact",
              start_date: "2022-01-11",
              study_person_role_hash:
                "df1bdcf0bccf1268eceb4261adb020472ba0c873ece182660eb2a87cb89825f5"
            }
          ],
          study_person_hash:
            "14b2a26fb3dd06e9dec1ffc9b54dc2a5849ae7c5084548c56382fd977b2bee3c"
        },
        {
          collection_source_id: 29,
          external_uuid:
            "5a5b7c99f83c40bb97a7463a20bce1222830e3032274dfb8f062c63af8622bda",
          name: "Thomas Seidl, PhD",
          study_person_hash:
            "329a42cd6d3d6d19e2c0dc0a3a9347afaca2fcfb5d0d1af5bf4fdb0d71af2060"
        },
        {
          collection_source_id: 29,
          external_uuid:
            "3a14f0cbc3051960bb5ba803abcf49aa8c5afdf1b226186ca93c5e808c02744e",
          name: "William K. Ward, MD",
          study_person_hash:
            "e06fb2944e7e514a4462f2559d0261db25156354f76d179edeafe0bc444276fb"
        },
        {
          collection_source_id: 29,
          external_uuid:
            "91310877b34d4e91f4a4b709e1a133dca12d4c2c05062bb44642c8b0d9089f33",
          name: "William K. Ward, MD",
          roleHistory: [
            {
              collection_source_id: 29,
              email: "<EMAIL>",
              hash_or_id:
                "54e7c16de1164c75348b2452091be83fea10e2047188d7a9b69b40d56b845608",
              phone: "9715702632",
              role: "Central Contact",
              start_date: "2022-01-11",
              study_person_role_hash:
                "54e7c16de1164c75348b2452091be83fea10e2047188d7a9b69b40d56b845608"
            }
          ],
          study_person_hash:
            "8d0f961365571691bbb25b13a7bc55c8e2ab26270104691846e56af78683a568"
        },
        {
          collection_source_id: 29,
          external_uuid:
            "3c8fb8f52b0991838abd26fe8cf0a3684aebd051503380fbde917d442b44869b",
          h1_person_id: 1255504,
          name: "Hello World",
          roleHistory: [
            {
              collection_source_id: 29,
              email: "<EMAIL>",
              hash_or_id:
                "df1bdcf0bccf1268eceb4261adb020472ba0c873ece182660eb2a87cb89825f5",
              phone: "9713405601",
              role: "Study Director",
              start_date: "2022-01-11",
              study_person_role_hash:
                "df1bdcf0bccf1268eceb4261adb020472ba0c873ece182660eb2a87cb89825f5"
            }
          ],
          study_person_hash:
            "14b2a26fb3dd06e9dec1ffc9b54dc2a5849ae7c5084548c56382fd977b2bee3c"
        },
        {
          collection_source_id: 29,
          external_uuid:
            "3c8fb8f52b0991838abd26fe8cf0a3684aebd051503380fbde917d442b44869b",
          h1_person_id: 1255504,
          name: "Hello World 2",
          roleHistory: [
            {
              collection_source_id: 29,
              email: "<EMAIL>",
              hash_or_id:
                "df1bdcf0bccf1268eceb4261adb020472ba0c873ece182660eb2a87cb89825f5",
              phone: "9713405601",
              role: "Central Contact",
              start_date: "2022-01-11",
              study_person_role_hash:
                "df1bdcf0bccf1268eceb4261adb020472ba0c873ece182660eb2a87cb89825f5"
            }
          ],
          study_person_hash:
            "14b2a26fb3dd06e9dec1ffc9b54dc2a5849ae7c5084548c56382fd977b2bee3c"
        },
        {
          collection_source_id: 29,
          external_uuid:
            "3c8fb8f52b0991838abd26fe8cf0a3684aebd051503380fbde917d442b44869b",
          h1_person_id: 1255504,
          name: "Lorem Ipsum",
          roleHistory: [
            {
              collection_source_id: 29,
              email: "<EMAIL>",
              hash_or_id:
                "df1bdcf0bccf1268eceb4261adb020472ba0c873ece182660eb2a87cb89825f5",
              phone: "9713405601",
              role: "Study Director",
              start_date: "2022-01-11",
              study_person_role_hash:
                "df1bdcf0bccf1268eceb4261adb020472ba0c873ece182660eb2a87cb89825f5"
            }
          ],
          study_person_hash:
            "14b2a26fb3dd06e9dec1ffc9b54dc2a5849ae7c5084548c56382fd977b2bee3c"
        }
      ],
      personIds: ["1255504"],
      h1People: [{ id: "1255504", isActive: true }],
      primaryCompletionDate: 1701302400000,
      primaryPurpose: ["Therapy", "Efficacy"],
      sitesCount: 0,
      source: ClinicalTrialSource.NCT,
      sources: [ClinicalTrialSource.NCT],
      sponsors: [],
      startDate: 1643587200000,
      status: "Not yet recruiting",
      studyCompletionDate: 1764460800000,
      studyPhase: "Phase 1",
      studyType: "Interventional",
      trialDuration: "3 yrs 9 mos",
      studyDirectors: ["William K. Ward, MD"],
      studyLinks: []
    }
  ],
  total: 1
};

const expectedResultForFixtureV4Enrollment = {
  from: 0,
  pageSize: 1,
  results: [
    {
      allocation: ["Non-Randomized"],
      avgPatientsPerSites: Infinity,
      briefTitle:
        "Safety and Preliminary Efficacy of BNA035 in Patients With Advanced Solid Tumors",
      conditions: ["Amyotrophic Lateral Sclerosis"],
      contactNames: [],
      contacts: [],
      eligibilityMinAge: ["18 Years"],
      estimatedEnrollment: {
        earliest: null,
        latest: null
      },
      actualEnrollment: null,
      enrollment: 300,
      enrollmentType: "Anticipated",
      estimatedEndDate: 1701302400000,
      facilityCity: [],
      facilityCountry: [],
      facilityName: [],
      facilityState: [],
      h1Id: "CT-29-3c9172f8-26d9-494f-8b4a-cad5b716bf2f",
      id: 0,
      interventionModel: ["Sequential Assignment"],
      interventionOtherNames: [],
      interventionTypes: ["Drug"],
      interventions: ["BNA035"],
      investigators: [],
      lastUpdatedAt: 1639008000000,
      masking: ["None (Open Label)"],
      nctId: "NCT05150457",
      patientsPerMonths: 6.67,
      personIds: [],
      h1People: [],
      primaryCompletionDate: 1701302400000,
      primaryPurpose: ["Therapy", "Efficacy"],
      sitesCount: 0,
      source: ClinicalTrialSource.NCT,
      sources: [ClinicalTrialSource.NCT],
      sponsors: [],
      startDate: 1643587200000,
      status: "Not yet recruiting",
      studyCompletionDate: 1764460800000,
      studyPhase: "Phase 1",
      studyType: "Interventional",
      trialDuration: "3 yrs 9 mos",
      studyLinks: []
    }
  ],
  total: 1
};
