// Simple test script to check similarity scores
function getEditDistanceMatrix(str1, str2) {
  const m = str1.length;
  const n = str2.length;

  const dist = Array(m + 1)
    .fill(0)
    .map(() => Array(n + 1).fill(0));

  for (let index = 1; index <= m; index++) {
    dist[index][0] = index;
  }

  for (let index = 1; index <= n; index++) {
    dist[0][index] = index;
  }

  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      let cost = 0;
      if (str1[i - 1].toLowerCase() == str2[j - 1].toLowerCase()) {
        cost = 0;
      } else {
        cost = 1;
      }
      dist[i][j] = Math.min(
        dist[i - 1][j] + 1, // Deletion of character
        dist[i][j - 1] + 1, // Insertion of character
        dist[i - 1][j - 1] + cost
      );
    }
  }

  return dist;
}

function calculateStringSimilarity(str1, str2) {
  if (!str1 || !str2) return 0;
  
  const normalizedStr1 = str1.toLowerCase().trim();
  const normalizedStr2 = str2.toLowerCase().trim();
  
  if (normalizedStr1 === normalizedStr2) return 1;
  
  const maxLength = Math.max(normalizedStr1.length, normalizedStr2.length);
  if (maxLength === 0) return 1;
  
  const editMatrix = getEditDistanceMatrix(normalizedStr1, normalizedStr2);
  const editDistance = editMatrix[normalizedStr1.length][normalizedStr2.length];
  
  // Normalize the edit distance to get similarity score
  return 1 - (editDistance / maxLength);
}

console.log('Similarity scores:');
console.log('Hematology vs Haematology:', calculateStringSimilarity('Hematology', 'Haematology'));
console.log('Hematology vs Hematology:', calculateStringSimilarity('Hematology', 'Hematology'));
console.log('Hematology vs Dermatology:', calculateStringSimilarity('Hematology', 'Dermatology'));
console.log('Hematology vs Cardiology:', calculateStringSimilarity('Hematology', 'Cardiology'));

console.log('\nWith threshold 0.85:');
const threshold = 0.85;
const values = ['Haematology', 'Hematology', 'Dermatology', 'Cardiology'];
const originalQuery = 'Hematology';

values.forEach(value => {
  const similarity = calculateStringSimilarity(originalQuery, value);
  const passes = similarity >= threshold;
  console.log(`${originalQuery} vs ${value}: ${similarity.toFixed(3)} - ${passes ? 'PASS' : 'FAIL'}`);
});
