{"name": "@h1eng/search-resource-server", "version": "1.30.6", "main": "src/index.ts", "license": "UNLICENSED", "private": true, "scripts": {"start": "ts-node --transpile-only src/index.ts", "start:dev": "nodemon", "precommit": "lint-staged", "lint": "eslint . --ext .ts,.js", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "test": "LOG_LEVEL=fatal jest", "test:ci": "LOG_LEVEL=fatal JEST_JUNIT_OUTPUT_DIR=../../test-artifacts JEST_JUNIT_OUTPUT_NAME=resource-server.xml jest --ci --reporters=default --reporters=jest-junit"}, "lint-staged": {"src/**/*.ts": ["eslint --fix --quiet"]}, "dependencies": {"@elastic/elasticsearch": "8.9.0", "@geemike/twitter-search-query-parser": "^1.0.1", "@grpc/grpc-js": "^1.7.2", "@h1nyc/account-sdk": "^1.55.1", "@h1nyc/notifications-sdk": "^1.27.1", "@h1nyc/pipeline-entities": "^1.15.23", "@h1nyc/pipeline-repositories": "^1.15.23", "@h1nyc/search-sdk": "/Users/<USER>/Documents/h1/h1-search/packages/sdk", "@h1nyc/systems-apm": "^0.6.0", "@h1nyc/systems-config": "^0.3.0", "@h1nyc/systems-feature-flags": "^0.6.0", "@h1nyc/systems-rpc": "^0.9.1", "@modelcontextprotocol/sdk": "^1.10.2", "axios": "^1.2.1", "date-fns": "^2.29.2", "deep-object-diff": "^1.1.7", "express": "^4.18.1", "franc-min": "^5.0.0", "grpc_tools_node_protoc_ts": "^5.3.2", "ioredis": "^4.19.4", "launchdarkly-node-server-sdk": "^6.4.2", "lodash": "^4.17.21", "long": "^5.2.1", "nice-grpc": "^2.1.0", "object-hash": "^3.0.0", "openai": "^4.96.2", "pg": "^8.11.0", "pino": "^8.4.2", "protobufjs": "^7.2.0", "ts-node-dev": "^2.0.0", "typedi": "^0.10.0", "zod": "^3.24.3"}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@types/express": "^4.17.13", "@types/google-protobuf": "^3.15.6", "@types/ioredis": "^4.17.9", "@types/ioredis-mock": "^8.2.1", "@types/lodash": "4.14.191", "@types/object-hash": "^3.0.2", "@types/supertest": "^2.0.11", "grpc-tools": "^1.11.2", "grpc_tools_node_protoc_ts": "^5.3.2", "ioredis-mock": "v7", "mockdate": "^3.0.5", "pino-pretty": "^9.1.0", "supertest": "^6.2.4"}}